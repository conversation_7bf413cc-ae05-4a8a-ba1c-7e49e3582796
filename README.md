## [SW Backoffice / Engagement](https://ubo-hub-engagement.qa-server.dev-qa.ss211208.com)

## Prepare

* `npm config set always-auth true`
* `npm login`
* `nvm i && npm i`

## Env

* `open -e .env`

```conf
MAPI=https://ubo-hub-engagement.qa-server.dev-qa.ss211208.com
CONFIG_URL=/mocks/config-local.json
```

## Run locally

* `cd ../sw-ubo-hub-admin/ && npm run start`
* `cd ../sw-ubo-hub-base/ && npm run start`
* `npm run mock-api`
* `npm run start`
