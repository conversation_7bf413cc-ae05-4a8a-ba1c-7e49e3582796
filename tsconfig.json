{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "skipLibCheck": true, "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "module": "esnext", "moduleResolution": "node", "importHelpers": true, "target": "es2015", "typeRoots": ["node_modules/@types"], "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "lib": ["es2018", "dom"]}}