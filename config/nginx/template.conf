upstream upstream_mapi {
  server ${MAPI} fail_timeout=30s max_fails=100;
}

upstream upstream_promotion_api {
  server ${PROMOTION_API} fail_timeout=30s max_fails=100;
}

upstream upstream_jpn_api {
  server ${JPN_API} fail_timeout=30s max_fails=100;
}

# Default server block for regular hosts
server {
  listen 80 reuseport default_server;
  server_name  localhost;

  include /usr/share/nginx/common.conf;

  location /favicon.ico {
    rewrite ^ /favicon.ico break;
  }

  location /api/config {
    default_type application/json;
    set $ENV_CONFIG '';
    set $ENV_CONFIG '${ENV_CONFIG} {';
    set $ENV_CONFIG '${ENV_CONFIG} "host": "${host}",';
    set $ENV_CONFIG '${ENV_CONFIG} "bridge": "${BRIDGE_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG} "loginUrl": "${LOGIN_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG} "hubs": {';
    set $ENV_CONFIG '${ENV_CONFIG} "casino": "${CASINO_HUB_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG} "engagement": "${ENGAGEMENT_HUB_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG} "analytics": "${DATA_HUB_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG} "studio": "${STUDIO_HUB_URL}"';
    set $ENV_CONFIG '${ENV_CONFIG} },';
    set $ENV_CONFIG '${ENV_CONFIG} "envName": "${ENV_NAME}",';
    set $ENV_CONFIG '${ENV_CONFIG} "locationName": "${LOCATION_NAME}"';
    set $ENV_CONFIG '${ENV_CONFIG} }';
    return 200 $ENV_CONFIG;
  }

  location /api/jpn/ {
      proxy_pass http://upstream_jpn_api/;
  }

  location /api/promotion/ {
      proxy_pass http://upstream_promotion_api/;
  }

  location /api/tournament/ {
    proxy_pass ${TOURNAMENT_API}/;
  }

  location /api/jackpot/ {
    proxy_pass ${JACKPOT_API}/;
  }

  location /api/prizedrop/ {
      proxy_pass ${PRIZE_DROP_API}/;
  }

  location /api/main-configuration/ {
    proxy_pass ${CONFIGURATION_API}/;
  }

  location /mapi/ {
    proxy_pass http://upstream_mapi/;
  }
}

# Virtual server block for Softgate host
server {
  listen 80;
  server_name ${SOFTGATE_HOST};

  include /usr/share/nginx/common.conf;

  location /favicon.ico {
    rewrite ^ /img/softgate/favicon.ico break;
  }

  location /api/config {
    default_type application/json;
    set $ENV_CONFIG '';
    set $ENV_CONFIG '${ENV_CONFIG} {';
    set $ENV_CONFIG '${ENV_CONFIG} "host": "${host}",';
    set $ENV_CONFIG '${ENV_CONFIG} "bridge": "${SOFTGATE_BRIDGE_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG} "loginUrl": "${SOFTGATE_LOGIN_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG} "hubs": {';
    set $ENV_CONFIG '${ENV_CONFIG}   "casino": "${SOFTGATE_CASINO_HUB_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG}   "engagement": "${SOFTGATE_ENGAGEMENT_HUB_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG}   "analytics": "${SOFTGATE_DATA_HUB_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG}   "studio": "${SOFTGATE_STUDIO_HUB_URL}"';
    set $ENV_CONFIG '${ENV_CONFIG} },';
    set $ENV_CONFIG '${ENV_CONFIG} "logo": {';
    set $ENV_CONFIG '${ENV_CONFIG}   "main": "/img/softgate/logo.png",';
    set $ENV_CONFIG '${ENV_CONFIG}   "solo": "/img/softgate/logo.png",';
    set $ENV_CONFIG '${ENV_CONFIG}   "white": ""';
    set $ENV_CONFIG '${ENV_CONFIG} },';
    set $ENV_CONFIG '${ENV_CONFIG} "envName": "${ENV_NAME}",';
    set $ENV_CONFIG '${ENV_CONFIG} "locationName": "${LOCATION_NAME}"';
    set $ENV_CONFIG '${ENV_CONFIG} }';
    return 200 $ENV_CONFIG;
  }

  location /api/jpn/ {
      proxy_pass http://upstream_jpn_api/;
  }

  location /api/promotion/ {
      proxy_pass http://upstream_promotion_api/;
  }

  location /api/tournament/ {
    proxy_pass ${TOURNAMENT_API}/;
  }

  location /api/jackpot/ {
    proxy_pass ${JACKPOT_API}/;
  }

  location /api/prizedrop/ {
      proxy_pass ${PRIZE_DROP_API}/;
  }

  location /api/main-configuration/ {
    proxy_pass ${CONFIGURATION_API}/;
  }

  location /mapi/ {
    proxy_pass http://upstream_mapi/;
  }
}
