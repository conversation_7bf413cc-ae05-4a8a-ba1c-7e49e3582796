#!/bin/sh

if [ -z "${SOFTGATE_HOST}" ]; then
  export SOFTGATE_HOST='unknown'
fi

if [ -z "${SOFTGATE_MAPI}" ]; then
  export SOFTGATE_MAPI="${MAPI}"
fi

mkdir -p conf.d
envsubst '$MAPI $SOFTGATE_MAPI $TOURNAMENT_API $JACKPOT_API $JPN_API $PROMOTION_API $PRIZE_DROP_API $CONFIGURATION_API $BRIDGE_URL $LOGIN_URL $CASINO_HUB_URL $ENGAGEMENT_HUB_URL $DATA_HUB_URL $STUDIO_HUB_URL $ENV_NAME $LOCATION_NAME $SOFTGATE_HOST $SOFTGATE_BRIDGE_URL $SOFTGATE_LOGIN_URL $SOFTGATE_CASINO_HUB_URL $SOFTGATE_ENGAGEMENT_HUB_URL $SOFTGATE_DATA_HUB_URL $SOFTGATE_STUDIO_HUB_URL' < /usr/share/nginx/template.conf > /etc/nginx/conf.d/default.conf
nginx -g "daemon off;"
