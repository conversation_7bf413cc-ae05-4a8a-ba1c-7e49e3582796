import { TimeZone } from './common/models/time-zone';


export const FORMAT_DATETIME = 'DD.MM.YY HH:mm';

export function formatCurrency( value: string ) {
  return value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

export const DEFAULT_IMAGES = {
  engagementBarLogoEu: 'https://static-cdntw.hep200512.com/sweh-assets-box/assets/jackpots.png',
  daily: 'https://static-cdntw.hep200512.com/sweh-assets/default_logos/Daily.png',
  hourly: 'https://static-cdntw.hep200512.com/sweh-assets/default_logos/Hourly.png',
  mega: 'https://static-cdntw.hep200512.com/sweh-assets/default_logos/Mega.png',
  minorEng: 'https://static-cdntw.hep200512.com/sweh-assets/default_logos/MinorENG.png',
  minorCn: 'https://static-cdntw.hep200512.com/sweh-assets/default_logos/MinorCN.png',
  majorEng: 'https://static-cdntw.hep200512.com/sweh-assets/default_logos/MajorENG.png',
  majorCn: 'https://static-cdntw.hep200512.com/sweh-assets/default_logos/MajorCN.png',
  grandEng: 'https://static-cdntw.hep200512.com/sweh-assets/default_logos/GrandENG.png',
  grandCn: 'https://static-cdntw.hep200512.com/sweh-assets/default_logos/GrandCN.png',
};

export const REPORT_ID =
  {
    tournament: 'pages/engagement-tools/tournaments',
    jackpot: 'pages/engagement-tools/jackpots',
    promotion: 'pages/engagement-tools/bonus_coins',
    prizeDrops: 'pages/engagement-tools/prize_drops'
  };

export function getTimeZones(): TimeZone[] {
  return TIME_ZONES.reduce(( acc: TimeZone[], cur: TimeZone ) => {
    cur = {
      id: cur.id,
      text: `(UTC${cur.text}) ${cur.id}`
    };
    acc.push(cur);
    return acc;
  }, []);
}

const TIME_ZONES: TimeZone[] =
  [
    {
      id:'Etc/GMT+12',
      text:'−12:00'
    },
    {
      id:'Etc/GMT+11',
      text:'−11:00'
    },
    {
      id:'Pacific/Niue',
      text:'−11:00'
    },
    {
      id:'Pacific/Pago_Pago',
      text:'−11:00'
    },
    {
      id:'America/Adak',
      text:'−10:00'
    },
    {
      id:'Etc/GMT+10',
      text:'−10:00'
    },
    {
      id:'Pacific/Honolulu',
      text:'−10:00'
    },
    {
      id:'Pacific/Rarotonga',
      text:'−10:00'
    },
    {
      id:'Pacific/Tahiti',
      text:'−10:00'
    },
    {
      id:'Pacific/Marquesas',
      text:'−09:30'
    },
    {
      id:'America/Anchorage',
      text:'−09:00'
    },
    {
      id:'America/Juneau',
      text:'−09:00'
    },
    {
      id:'America/Metlakatla',
      text:'−09:00'
    },
    {
      id:'America/Nome',
      text:'−09:00'
    },
    {
      id:'America/Sitka',
      text:'−09:00'
    },
    {
      id:'America/Yakutat',
      text:'−09:00'
    },
    {
      id:'Etc/GMT+9',
      text:'−09:00'
    },
    {
      id:'Pacific/Gambier',
      text:'−09:00'
    },
    {
      id:'America/Los_Angeles',
      text:'−08:00'
    },
    {
      id:'America/Tijuana',
      text:'−08:00'
    },
    {
      id:'America/Vancouver',
      text:'−08:00'
    },
    {
      id:'Etc/GMT+8',
      text:'−08:00'
    },
    {
      id:'Pacific/Pitcairn',
      text:'−08:00'
    },
    {
      id:'America/Boise',
      text:'−07:00'
    },
    {
      id:'America/Cambridge_Bay',
      text:'−07:00'
    },
    {
      id:'America/Chihuahua',
      text:'−07:00'
    },
    {
      id:'America/Creston',
      text:'−07:00'
    },
    {
      id:'America/Dawson',
      text:'−07:00'
    },
    {
      id:'America/Dawson_Creek',
      text:'−07:00'
    },
    {
      id:'America/Denver',
      text:'−07:00'
    },
    {
      id:'America/Edmonton',
      text:'−07:00'
    },
    {
      id:'America/Fort_Nelson',
      text:'−07:00'
    },
    {
      id:'America/Hermosillo',
      text:'−07:00'
    },
    {
      id:'America/Inuvik',
      text:'−07:00'
    },
    {
      id:'America/Mazatlan',
      text:'−07:00'
    },
    {
      id:'America/Ojinaga',
      text:'−07:00'
    },
    {
      id:'America/Phoenix',
      text:'−07:00'
    },
    {
      id:'America/Whitehorse',
      text:'−07:00'
    },
    {
      id:'America/Yellowknife',
      text:'−07:00'
    },
    {
      id:'Etc/GMT+7',
      text:'−07:00'
    },
    {
      id:'America/Bahia_Banderas',
      text:'−06:00'
    },
    {
      id:'America/Belize',
      text:'−06:00'
    },
    {
      id:'America/Chicago',
      text:'−06:00'
    },
    {
      id:'America/Costa_Rica',
      text:'−06:00'
    },
    {
      id:'America/El_Salvador',
      text:'−06:00'
    },
    {
      id:'America/Guatemala',
      text:'−06:00'
    },
    {
      id:'America/Indiana/Knox',
      text:'−06:00'
    },
    {
      id:'America/Indiana/Tell_City',
      text:'−06:00'
    },
    {
      id:'America/Managua',
      text:'−06:00'
    },
    {
      id:'America/Matamoros',
      text:'−06:00'
    },
    {
      id:'America/Menominee',
      text:'−06:00'
    },
    {
      id:'America/Merida',
      text:'−06:00'
    },
    {
      id:'America/Mexico_City',
      text:'−06:00'
    },
    {
      id:'America/Monterrey',
      text:'−06:00'
    },
    {
      id:'America/North_Dakota/Beulah',
      text:'−06:00'
    },
    {
      id:'America/North_Dakota/Center',
      text:'−06:00'
    },
    {
      id:'America/North_Dakota/New_Salem',
      text:'−06:00'
    },
    {
      id:'America/Rainy_River',
      text:'−06:00'
    },
    {
      id:'America/Rankin_Inlet',
      text:'−06:00'
    },
    {
      id:'America/Regina',
      text:'−06:00'
    },
    {
      id:'America/Resolute',
      text:'−06:00'
    },
    {
      id:'America/Swift_Current',
      text:'−06:00'
    },
    {
      id:'America/Tegucigalpa',
      text:'−06:00'
    },
    {
      id:'America/Winnipeg',
      text:'−06:00'
    },
    {
      id:'Etc/GMT+6',
      text:'−06:00'
    },
    {
      id:'Pacific/Easter',
      text:'−06:00'
    },
    {
      id:'Pacific/Galapagos',
      text:'−06:00'
    },
    {
      id:'America/Atikokan',
      text:'−05:00'
    },
    {
      id:'America/Bogota',
      text:'−05:00'
    },
    {
      id:'America/Cancun',
      text:'−05:00'
    },
    {
      id:'America/Detroit',
      text:'−05:00'
    },
    {
      id:'America/Eirunepe',
      text:'−05:00'
    },
    {
      id:'America/Grand_Turk',
      text:'−05:00'
    },
    {
      id:'America/Guayaquil',
      text:'−05:00'
    },
    {
      id:'America/Havana',
      text:'−05:00'
    },
    {
      id:'America/Indiana/Indianapolis',
      text:'−05:00'
    },
    {
      id:'America/Indiana/Marengo',
      text:'−05:00'
    },
    {
      id:'America/Indiana/Petersburg',
      text:'−05:00'
    },
    {
      id:'America/Indiana/Vevay',
      text:'−05:00'
    },
    {
      id:'America/Indiana/Vincennes',
      text:'−05:00'
    },
    {
      id:'America/Indiana/Winamac',
      text:'−05:00'
    },
    {
      id:'America/Iqaluit',
      text:'−05:00'
    },
    {
      id:'America/Jamaica',
      text:'−05:00'
    },
    {
      id:'America/Kentucky/Louisville',
      text:'−05:00'
    },
    {
      id:'America/Kentucky/Monticello',
      text:'−05:00'
    },
    {
      id:'America/Lima',
      text:'−05:00'
    },
    {
      id:'America/Nassau',
      text:'−05:00'
    },
    {
      id:'America/New_York',
      text:'−05:00'
    },
    {
      id:'America/Nipigon',
      text:'−05:00'
    },
    {
      id:'America/Panama',
      text:'−05:00'
    },
    {
      id:'America/Pangnirtung',
      text:'−05:00'
    },
    {
      id:'America/Port-au-Prince',
      text:'−05:00'
    },
    {
      id:'America/Rio_Branco',
      text:'−05:00'
    },
    {
      id:'America/Thunder_Bay',
      text:'−05:00'
    },
    {
      id:'America/Toronto',
      text:'−05:00'
    },
    {
      id:'Etc/GMT+5',
      text:'−05:00'
    },
    {
      id:'America/Asuncion',
      text:'−04:00'
    },
    {
      id:'America/Barbados',
      text:'−04:00'
    },
    {
      id:'America/Blanc-Sablon',
      text:'−04:00'
    },
    {
      id:'America/Boa_Vista',
      text:'−04:00'
    },
    {
      id:'America/Campo_Grande',
      text:'−04:00'
    },
    {
      id:'America/Caracas',
      text:'−04:00'
    },
    {
      id:'America/Cuiaba',
      text:'−04:00'
    },
    {
      id:'America/Curacao',
      text:'−04:00'
    },
    {
      id:'America/Glace_Bay',
      text:'−04:00'
    },
    {
      id:'America/Goose_Bay',
      text:'−04:00'
    },
    {
      id:'America/Guyana',
      text:'−04:00'
    },
    {
      id:'America/Halifax',
      text:'−04:00'
    },
    {
      id:'America/La_Paz',
      text:'−04:00'
    },
    {
      id:'America/Manaus',
      text:'−04:00'
    },
    {
      id:'America/Martinique',
      text:'−04:00'
    },
    {
      id:'America/Moncton',
      text:'−04:00'
    },
    {
      id:'America/Port_of_Spain',
      text:'−04:00'
    },
    {
      id:'America/Porto_Velho',
      text:'−04:00'
    },
    {
      id:'America/Puerto_Rico',
      text:'−04:00'
    },
    {
      id:'America/Santiago',
      text:'−04:00'
    },
    {
      id:'America/Santo_Domingo',
      text:'−04:00'
    },
    {
      id:'America/Thule',
      text:'−04:00'
    },
    {
      id:'Atlantic/Bermuda',
      text:'−04:00'
    },
    {
      id:'Etc/GMT+4',
      text:'−04:00'
    },
    {
      id:'America/St_Johns',
      text:'−03:30'
    },
    {
      id:'America/Araguaina',
      text:'−03:00'
    },
    {
      id:'America/Argentina/Buenos_Aires',
      text:'−03:00'
    },
    {
      id:'America/Argentina/Catamarca',
      text:'−03:00'
    },
    {
      id:'America/Argentina/Cordoba',
      text:'−03:00'
    },
    {
      id:'America/Argentina/Jujuy',
      text:'−03:00'
    },
    {
      id:'America/Argentina/La_Rioja',
      text:'−03:00'
    },
    {
      id:'America/Argentina/Mendoza',
      text:'−03:00'
    },
    {
      id:'America/Argentina/Rio_Gallegos',
      text:'−03:00'
    },
    {
      id:'America/Argentina/Salta',
      text:'−03:00'
    },
    {
      id:'America/Argentina/San_Juan',
      text:'−03:00'
    },
    {
      id:'America/Argentina/San_Luis',
      text:'−03:00'
    },
    {
      id:'America/Argentina/Tucuman',
      text:'−03:00'
    },
    {
      id:'America/Argentina/Ushuaia',
      text:'−03:00'
    },
    {
      id:'America/Bahia',
      text:'−03:00'
    },
    {
      id:'America/Belem',
      text:'−03:00'
    },
    {
      id:'America/Cayenne',
      text:'−03:00'
    },
    {
      id:'America/Fortaleza',
      text:'−03:00'
    },
    {
      id:'America/Maceio',
      text:'−03:00'
    },
    {
      id:'America/Miquelon',
      text:'−03:00'
    },
    {
      id:'America/Montevideo',
      text:'−03:00'
    },
    {
      id:'America/Nuuk',
      text:'−03:00'
    },
    {
      id:'America/Paramaribo',
      text:'−03:00'
    },
    {
      id:'America/Punta_Arenas',
      text:'−03:00'
    },
    {
      id:'America/Recife',
      text:'−03:00'
    },
    {
      id:'America/Santarem',
      text:'−03:00'
    },
    {
      id:'America/Sao_Paulo',
      text:'−03:00'
    },
    {
      id:'Antarctica/Palmer',
      text:'−03:00'
    },
    {
      id:'Antarctica/Rothera',
      text:'−03:00'
    },
    {
      id:'Atlantic/Stanley',
      text:'−03:00'
    },
    {
      id:'Etc/GMT+3',
      text:'−03:00'
    },
    {
      id:'America/Noronha',
      text:'−02:00'
    },
    {
      id:'Atlantic/South_Georgia',
      text:'−02:00'
    },
    {
      id:'Etc/GMT+2',
      text:'−02:00'
    },
    {
      id:'America/Scoresbysund',
      text:'−01:00'
    },
    {
      id:'Atlantic/Azores',
      text:'−01:00'
    },
    {
      id:'Atlantic/Cape_Verde',
      text:'−01:00'
    },
    {
      id:'Etc/GMT+1',
      text:'−01:00'
    },
    {
      id:'Africa/Abidjan',
      text:'+00:00'
    },
    {
      id:'Africa/Accra',
      text:'+00:00'
    },
    {
      id:'Africa/Bissau',
      text:'+00:00'
    },
    {
      id:'Africa/Monrovia',
      text:'+00:00'
    },
    {
      id:'Africa/Sao_Tome',
      text:'+00:00'
    },
    {
      id:'America/Danmarkshavn',
      text:'+00:00'
    },
    {
      id:'Antarctica/Troll',
      text:'+00:00'
    },
    {
      id:'Atlantic/Canary',
      text:'+00:00'
    },
    {
      id:'Atlantic/Faroe',
      text:'+00:00'
    },
    {
      id:'Atlantic/Madeira',
      text:'+00:00'
    },
    {
      id:'Atlantic/Reykjavik',
      text:'+00:00'
    },
    {
      id:'Etc/GMT',
      text:'+00:00'
    },
    {
      id:'Etc/UTC',
      text:'+00:00'
    },
    {
      id:'Europe/Lisbon',
      text:'+00:00'
    },
    {
      id:'Europe/London',
      text:'+00:00'
    },
    {
      id:'Factory',
      text:'+00:00'
    },
    {
      id:'Africa/Algiers',
      text:'+01:00'
    },
    {
      id:'Africa/Casablanca',
      text:'+01:00'
    },
    {
      id:'Africa/Ceuta',
      text:'+01:00'
    },
    {
      id:'Africa/El_Aaiun',
      text:'+01:00'
    },
    {
      id:'Africa/Lagos',
      text:'+01:00'
    },
    {
      id:'Africa/Ndjamena',
      text:'+01:00'
    },
    {
      id:'Africa/Tunis',
      text:'+01:00'
    },
    {
      id:'Etc/GMT-1',
      text:'+01:00'
    },
    {
      id:'Europe/Amsterdam',
      text:'+01:00'
    },
    {
      id:'Europe/Andorra',
      text:'+01:00'
    },
    {
      id:'Europe/Belgrade',
      text:'+01:00'
    },
    {
      id:'Europe/Berlin',
      text:'+01:00'
    },
    {
      id:'Europe/Brussels',
      text:'+01:00'
    },
    {
      id:'Europe/Budapest',
      text:'+01:00'
    },
    {
      id:'Europe/Copenhagen',
      text:'+01:00'
    },
    {
      id:'Europe/Dublin',
      text:'+01:00'
    },
    {
      id:'Europe/Gibraltar',
      text:'+01:00'
    },
    {
      id:'Europe/Luxembourg',
      text:'+01:00'
    },
    {
      id:'Europe/Madrid',
      text:'+01:00'
    },
    {
      id:'Europe/Malta',
      text:'+01:00'
    },
    {
      id:'Europe/Monaco',
      text:'+01:00'
    },
    {
      id:'Europe/Oslo',
      text:'+01:00'
    },
    {
      id:'Europe/Paris',
      text:'+01:00'
    },
    {
      id:'Europe/Prague',
      text:'+01:00'
    },
    {
      id:'Europe/Rome',
      text:'+01:00'
    },
    {
      id:'Europe/Stockholm',
      text:'+01:00'
    },
    {
      id:'Europe/Tirane',
      text:'+01:00'
    },
    {
      id:'Europe/Vienna',
      text:'+01:00'
    },
    {
      id:'Europe/Warsaw',
      text:'+01:00'
    },
    {
      id:'Europe/Zurich',
      text:'+01:00'
    },
    {
      id:'Africa/Cairo',
      text:'+02:00'
    },
    {
      id:'Africa/Johannesburg',
      text:'+02:00'
    },
    {
      id:'Africa/Khartoum',
      text:'+02:00'
    },
    {
      id:'Africa/Maputo',
      text:'+02:00'
    },
    {
      id:'Africa/Tripoli',
      text:'+02:00'
    },
    {
      id:'Africa/Windhoek',
      text:'+02:00'
    },
    {
      id:'Asia/Amman',
      text:'+02:00'
    },
    {
      id:'Asia/Beirut',
      text:'+02:00'
    },
    {
      id:'Asia/Damascus',
      text:'+02:00'
    },
    {
      id:'Asia/Famagusta',
      text:'+02:00'
    },
    {
      id:'Asia/Gaza',
      text:'+02:00'
    },
    {
      id:'Asia/Hebron',
      text:'+02:00'
    },
    {
      id:'Asia/Jerusalem',
      text:'+02:00'
    },
    {
      id:'Asia/Nicosia',
      text:'+02:00'
    },
    {
      id:'Etc/GMT-2',
      text:'+02:00'
    },
    {
      id:'Europe/Athens',
      text:'+02:00'
    },
    {
      id:'Europe/Bucharest',
      text:'+02:00'
    },
    {
      id:'Europe/Chisinau',
      text:'+02:00'
    },
    {
      id:'Europe/Helsinki',
      text:'+02:00'
    },
    {
      id:'Europe/Kaliningrad',
      text:'+02:00'
    },
    {
      id:'Europe/Kiev',
      text:'+02:00'
    },
    {
      id:'Europe/Riga',
      text:'+02:00'
    },
    {
      id:'Europe/Sofia',
      text:'+02:00'
    },
    {
      id:'Europe/Tallinn',
      text:'+02:00'
    },
    {
      id:'Europe/Uzhgorod',
      text:'+02:00'
    },
    {
      id:'Europe/Vilnius',
      text:'+02:00'
    },
    {
      id:'Europe/Zaporozhye',
      text:'+02:00'
    },
    {
      id:'Africa/Juba',
      text:'+03:00'
    },
    {
      id:'Africa/Nairobi',
      text:'+03:00'
    },
    {
      id:'Antarctica/Syowa',
      text:'+03:00'
    },
    {
      id:'Asia/Baghdad',
      text:'+03:00'
    },
    {
      id:'Asia/Qatar',
      text:'+03:00'
    },
    {
      id:'Asia/Riyadh',
      text:'+03:00'
    },
    {
      id:'Etc/GMT-3',
      text:'+03:00'
    },
    {
      id:'Europe/Istanbul',
      text:'+03:00'
    },
    {
      id:'Europe/Kirov',
      text:'+03:00'
    },
    {
      id:'Europe/Minsk',
      text:'+03:00'
    },
    {
      id:'Europe/Moscow',
      text:'+03:00'
    },
    {
      id:'Europe/Simferopol',
      text:'+03:00'
    },
    {
      id:'Asia/Tehran',
      text:'+03:30'
    },
    {
      id:'Asia/Baku',
      text:'+04:00'
    },
    {
      id:'Asia/Dubai',
      text:'+04:00'
    },
    {
      id:'Asia/Tbilisi',
      text:'+04:00'
    },
    {
      id:'Asia/Yerevan',
      text:'+04:00'
    },
    {
      id:'Etc/GMT-4',
      text:'+04:00'
    },
    {
      id:'Europe/Astrakhan',
      text:'+04:00'
    },
    {
      id:'Europe/Samara',
      text:'+04:00'
    },
    {
      id:'Europe/Saratov',
      text:'+04:00'
    },
    {
      id:'Europe/Ulyanovsk',
      text:'+04:00'
    },
    {
      id:'Europe/Volgograd',
      text:'+04:00'
    },
    {
      id:'Indian/Mahe',
      text:'+04:00'
    },
    {
      id:'Indian/Mauritius',
      text:'+04:00'
    },
    {
      id:'Indian/Reunion',
      text:'+04:00'
    },
    {
      id:'Asia/Kabul',
      text:'+04:30'
    },
    {
      id:'Antarctica/Mawson',
      text:'+05:00'
    },
    {
      id:'Asia/Aqtau',
      text:'+05:00'
    },
    {
      id:'Asia/Aqtobe',
      text:'+05:00'
    },
    {
      id:'Asia/Ashgabat',
      text:'+05:00'
    },
    {
      id:'Asia/Atyrau',
      text:'+05:00'
    },
    {
      id:'Asia/Dushanbe',
      text:'+05:00'
    },
    {
      id:'Asia/Karachi',
      text:'+05:00'
    },
    {
      id:'Asia/Oral',
      text:'+05:00'
    },
    {
      id:'Asia/Qyzylorda',
      text:'+05:00'
    },
    {
      id:'Asia/Samarkand',
      text:'+05:00'
    },
    {
      id:'Asia/Tashkent',
      text:'+05:00'
    },
    {
      id:'Asia/Yekaterinburg',
      text:'+05:00'
    },
    {
      id:'Etc/GMT-5',
      text:'+05:00'
    },
    {
      id:'Indian/Kerguelen',
      text:'+05:00'
    },
    {
      id:'Indian/Maldives',
      text:'+05:00'
    },
    {
      id:'Asia/Colombo',
      text:'+05:30'
    },
    {
      id:'Asia/Kolkata',
      text:'+05:30'
    },
    {
      id:'Asia/Kathmandu',
      text:'+05:45'
    },
    {
      id:'Antarctica/Vostok',
      text:'+06:00'
    },
    {
      id:'Asia/Almaty',
      text:'+06:00'
    },
    {
      id:'Asia/Bishkek',
      text:'+06:00'
    },
    {
      id:'Asia/Dhaka',
      text:'+06:00'
    },
    {
      id:'Asia/Omsk',
      text:'+06:00'
    },
    {
      id:'Asia/Qostanay',
      text:'+06:00'
    },
    {
      id:'Asia/Thimphu',
      text:'+06:00'
    },
    {
      id:'Asia/Urumqi',
      text:'+06:00'
    },
    {
      id:'Etc/GMT-6',
      text:'+06:00'
    },
    {
      id:'Indian/Chagos',
      text:'+06:00'
    },
    {
      id:'Asia/Yangon',
      text:'+06:30'
    },
    {
      id:'Indian/Cocos',
      text:'+06:30'
    },
    {
      id:'Antarctica/Davis',
      text:'+07:00'
    },
    {
      id:'Asia/Bangkok',
      text:'+07:00'
    },
    {
      id:'Asia/Barnaul',
      text:'+07:00'
    },
    {
      id:'Asia/Ho_Chi_Minh',
      text:'+07:00'
    },
    {
      id:'Asia/Hovd',
      text:'+07:00'
    },
    {
      id:'Asia/Jakarta',
      text:'+07:00'
    },
    {
      id:'Asia/Krasnoyarsk',
      text:'+07:00'
    },
    {
      id:'Asia/Novokuznetsk',
      text:'+07:00'
    },
    {
      id:'Asia/Novosibirsk',
      text:'+07:00'
    },
    {
      id:'Asia/Pontianak',
      text:'+07:00'
    },
    {
      id:'Asia/Tomsk',
      text:'+07:00'
    },
    {
      id:'Etc/GMT-7',
      text:'+07:00'
    },
    {
      id:'Indian/Christmas',
      text:'+07:00'
    },
    {
      id:'Asia/Brunei',
      text:'+08:00'
    },
    {
      id:'Asia/Choibalsan',
      text:'+08:00'
    },
    {
      id:'Asia/Hong_Kong',
      text:'+08:00'
    },
    {
      id:'Asia/Irkutsk',
      text:'+08:00'
    },
    {
      id:'Asia/Kuala_Lumpur',
      text:'+08:00'
    },
    {
      id:'Asia/Kuching',
      text:'+08:00'
    },
    {
      id:'Asia/Macau',
      text:'+08:00'
    },
    {
      id:'Asia/Makassar',
      text:'+08:00'
    },
    {
      id:'Asia/Manila',
      text:'+08:00'
    },
    {
      id:'Asia/Shanghai',
      text:'+08:00'
    },
    {
      id:'Asia/Singapore',
      text:'+08:00'
    },
    {
      id:'Asia/Taipei',
      text:'+08:00'
    },
    {
      id:'Asia/Ulaanbaatar',
      text:'+08:00'
    },
    {
      id:'Australia/Perth',
      text:'+08:00'
    },
    {
      id:'Etc/GMT-8',
      text:'+08:00'
    },
    {
      id:'Australia/Eucla',
      text:'+08:45'
    },
    {
      id:'Asia/Chita',
      text:'+09:00'
    },
    {
      id:'Asia/Dili',
      text:'+09:00'
    },
    {
      id:'Asia/Jayapura',
      text:'+09:00'
    },
    {
      id:'Asia/Khandyga',
      text:'+09:00'
    },
    {
      id:'Asia/Pyongyang',
      text:'+09:00'
    },
    {
      id:'Asia/Seoul',
      text:'+09:00'
    },
    {
      id:'Asia/Tokyo',
      text:'+09:00'
    },
    {
      id:'Asia/Yakutsk',
      text:'+09:00'
    },
    {
      id:'Etc/GMT-9',
      text:'+09:00'
    },
    {
      id:'Pacific/Palau',
      text:'+09:00'
    },
    {
      id:'Australia/Adelaide',
      text:'+09:30'
    },
    {
      id:'Australia/Broken_Hill',
      text:'+09:30'
    },
    {
      id:'Australia/Darwin',
      text:'+09:30'
    },
    {
      id:'Antarctica/DumontDUrville',
      text:'+10:00'
    },
    {
      id:'Antarctica/Macquarie',
      text:'+10:00'
    },
    {
      id:'Asia/Ust-Nera',
      text:'+10:00'
    },
    {
      id:'Asia/Vladivostok',
      text:'+10:00'
    },
    {
      id:'Australia/Brisbane',
      text:'+10:00'
    },
    {
      id:'Australia/Currie',
      text:'+10:00'
    },
    {
      id:'Australia/Hobart',
      text:'+10:00'
    },
    {
      id:'Australia/Lindeman',
      text:'+10:00'
    },
    {
      id:'Australia/Melbourne',
      text:'+10:00'
    },
    {
      id:'Australia/Sydney',
      text:'+10:00'
    },
    {
      id:'Etc/GMT-10',
      text:'+10:00'
    },
    {
      id:'Pacific/Chuuk',
      text:'+10:00'
    },
    {
      id:'Pacific/Guam',
      text:'+10:00'
    },
    {
      id:'Pacific/Port_Moresby',
      text:'+10:00'
    },
    {
      id:'Australia/Lord_Howe',
      text:'+10:30'
    },
    {
      id:'Antarctica/Casey',
      text:'+11:00'
    },
    {
      id:'Asia/Magadan',
      text:'+11:00'
    },
    {
      id:'Asia/Sakhalin',
      text:'+11:00'
    },
    {
      id:'Asia/Srednekolymsk',
      text:'+11:00'
    },
    {
      id:'Etc/GMT-11',
      text:'+11:00'
    },
    {
      id:'Pacific/Bougainville',
      text:'+11:00'
    },
    {
      id:'Pacific/Efate',
      text:'+11:00'
    },
    {
      id:'Pacific/Guadalcanal',
      text:'+11:00'
    },
    {
      id:'Pacific/Kosrae',
      text:'+11:00'
    },
    {
      id:'Pacific/Norfolk',
      text:'+11:00'
    },
    {
      id:'Pacific/Noumea',
      text:'+11:00'
    },
    {
      id:'Pacific/Pohnpei',
      text:'+11:00'
    },
    {
      id:'Asia/Anadyr',
      text:'+12:00'
    },
    {
      id:'Asia/Kamchatka',
      text:'+12:00'
    },
    {
      id:'Etc/GMT-12',
      text:'+12:00'
    },
    {
      id:'Pacific/Auckland',
      text:'+12:00'
    },
    {
      id:'Pacific/Fiji',
      text:'+12:00'
    },
    {
      id:'Pacific/Funafuti',
      text:'+12:00'
    },
    {
      id:'Pacific/Kwajalein',
      text:'+12:00'
    },
    {
      id:'Pacific/Majuro',
      text:'+12:00'
    },
    {
      id:'Pacific/Nauru',
      text:'+12:00'
    },
    {
      id:'Pacific/Tarawa',
      text:'+12:00'
    },
    {
      id:'Pacific/Wake',
      text:'+12:00'
    },
    {
      id:'Pacific/Wallis',
      text:'+12:00'
    },
    {
      id:'Pacific/Chatham',
      text:'+12:45'
    },
    {
      id:'Etc/GMT-13',
      text:'+13:00'
    },
    {
      id:'Pacific/Apia',
      text:'+13:00'
    },
    {
      id:'Pacific/Enderbury',
      text:'+13:00'
    },
    {
      id:'Pacific/Fakaofo',
      text:'+13:00'
    },
    {
      id:'Pacific/Tongatapu',
      text:'+13:00'
    },
    {
      id:'Etc/GMT-14',
      text:'+14:00'
    },
    {
      id:'Pacific/Kiritimati',
      text:'+14:00'
    }
  ];
