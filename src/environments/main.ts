import { env } from './base';

export interface Environment {
  production: boolean;
  APP_VERSION: string;
  MAPI: string;
  CONFIGURATION_API: string;
  TOURNAMENT_API: string;
  PRIZE_DROP_API: string;
  JACKPOT_API: string;
  JPN_API: string;
  JACKPOT_CONFIGURATION_API: string;
  PROMOTION_API: string;
}

function gitVersion( { build = 0, git: { revision = '', datetime = '', branch = '' } = {} } ) {
  return `${revision} ${build} ${branch} ${datetime}`;
}

export const main: Environment = {
  production: false,
  APP_VERSION: `${env.version} ${gitVersion(env)}`,
  MAPI: '/mapi/v1',
  TOURNAMENT_API: '/api/tournament/ubo/admin',
  PRIZE_DROP_API: '/api/prizedrop/ubo/admin',
  JACKPOT_API: '/api/jackpot/ubo/admin',
  JPN_API: '/api/jpn/api/v2/jpn',
  JACKPOT_CONFIGURATION_API: 'api/jackpot/configuration/jackpot',
  PROMOTION_API: '/api/promotion/v1',
  CONFIGURATION_API: '/api/main-configuration',
};
