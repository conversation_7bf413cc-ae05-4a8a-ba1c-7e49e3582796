{"ALL": {"DEFAULT_SCHEMA_SECTIONS": {"basic": "General", "dateTime": "Date/Time"}, "settings": "Settings", "selectAll": "Select all"}, "FILTER": {"reset": "Reset", "search": "Search", "rangeFrom": "From", "rangeTo": "To", "resellerOperator": "Reseller / Operator"}, "COMMON": {"seconds": "seconds", "yes": "Yes", "no": "No", "ACTIONS": {"ok": "OK", "save": "Save", "cancel": "Cancel", "add": "Add", "remove": "Remove", "edit": "Edit", "delete": "Delete", "viewEdit": "View / Edit", "duplicate": "Duplicate"}}, "MENU_SELECT": {"MULTISELECT": {"multiple": "Multiple values"}}, "DIALOG": {"close": "Close", "confirmAction": "Confirm", "pleaseConfirm": "Please confirm your action", "chooseFile": "Choose file", "cancel": "Cancel", "next": "Next", "saveFilter": "Save filter", "savePlayers": "Save players", "save": "Save", "previous": "Previous", "yes": "Yes", "no": "No", "ok": "OK"}, "MENU_SECTIONS": {"tournaments": "Tournaments", "jackpots": "Must-Win Jackpots", "promotions": "Promotions", "prize_drops": "Prize drops"}, "TOURNAMENT": {"NOTIFICATIONS": {"deleted": "Tournament \"{{name}}\" successfully deleted!", "removeTournament": "Are you sure you want to delete this tournament?", "createdTournamentMessage": "Tournament was successfully created", "updatedTournamentMessage": "Tournament \"{{name}}\" was successfully updated", "makeChanges": "You are about to make changes to a live Tournament! Are you sure?", "makeChangesWillAffectAllPlayers": "You are about to make changes to a live Tournament! This will affect all players for {{brands}} brand(s). Are you sure?", "statusChangedMessage": "Status was changed successfully", "forceRemove": "You are about to force remove tournament {{ name }}! Are you sure?"}, "LIST": {"title": "Tournaments", "newTournament": "New tournament", "tournamentTitleSingle": "{{length}} Tournament", "tournamentTitleMultiple": "{{length}} Tournaments"}, "GRID": {"id": "Tournament ID", "featureId": "Feature ID", "name": "Name", "lastRun": "Last Run", "nextRun": "Next Run", "status": "Status", "active": "Active", "customers": "Customers", "games": "Games", "owner": "Owner", "operators": "Operators", "created": "Created", "reports": "Reports"}, "FORM": {"GENERAL": {"NAME": {"title": "Name", "hint": "Daily tournament"}, "DESCRIPTION": {"title": "Description"}, "CURRENCIES": {"title": "Currencies", "hint": "The tournament will only be visible to players with these currencies"}, "BASE_CURRENCY": {"title": "Base currency"}, "options": "Options", "showTournamentInEngagementBar": "Show tournament in Engagement bar", "optIn": "Player opt-in required"}, "SCHEDULE": {"timezone": "Time zone"}, "SEGMENTATION": {"playerId": "Player ID", "brandId": "Brand ID", "operator": "Operator", "decryptedBrand": "Decrypted ID", "removePlayer": "remove", "availableEntities": "Available Entities", "selectedEntities": "Selected Entities", "playerIDs": "Enter player IDs", "selectedPlayers": "{{count}} players", "selectedPlayer": "{{count}} player", "allPlayers": "All players", "unableToRead": "Unable to read ", "importValidFile": "Please import valid .csv file", "players": "Players", "playersHintStart": "This tournament is visible to", "all": "all", "playersHintEnd": " players", "viewPlayers": "View players", "addPlayersFilter": "Add player filter", "uploadPlayerList": "Upload player list", "playersInCSV": " players in CSV", "previewPlayerList": "Preview player list", "addPlayersDescription": "Add players to the filter by uploading a CSV file with your player IDs. The Tournament will be shown to the listed players only.", "csvFileDescription": "Your CSV file should have PlayerID and BrandID columns", "example": "Example", "fileType": "File type: CSV", "maximumRows": "Maximum rows: {{maximumRows}}", "currencies": "Currencies", "countries": "Countries", "currenciesHint": "The Tournament is visible to players with these currencies:", "currenciesSubTitle": "Select the currencies to show this tournament in", "playersInFilterLabel": " players in Tournament filter", "invalidCsv": "Your CSV file should have only PlayerID and BrandID columns", "maximumNumberOfRowsIsExceeded": "The maximum number of rows is exceeded.", "removeExistingPlayers": "Remove existing players", "importDescription": "Add players to the filter by uploading a CSV file with your player IDs. The Tournament will be shown to the listed players only.", "applyingRatesMessage": "Rates will be applied to the next tournament cycle"}, "PAYOUT": {"configuration": "Configuration", "baseCurrency": "Base currency", "pointsBetAndWinAmounts": "Points by bet and win amounts", "pointsByBetWin": "Points by win multiplier"}}, "TOOLTIP": {"duration": "Duration of each tournament instance repetition from start to finish.", "dateSchedule": "Tournament instances can be set to run on specific dates or on specific days of the week.", "startTime": "Defines one or more times of day when Tournament instances start. The difference between two Tournament start times must be longer than the duration of the tournament.", "owner": "Choose the entity who can manage this Tournament", "specificDates": "Run the tournament on specific dates", "dailySchedule": "Run the tournament on specific days of the week", "descriptionDailySchedule": "Use this settings to run the tournament on specific days of the week, between the validity dates"}}, "PRIZE_DROP": {"NOTIFICATIONS": {"deleted": "Prize drop \"{{name}}\" successfully deleted!", "removePrizeDrop": "Are you sure you want to delete this prize drop?", "createdPrizeDropMessage": "Prize drop was successfully created", "updatedPrizeDropMessage": "Prize drop \"{{name}}\" was successfully updated", "makeChanges": "You are about to make changes to a live Prize drop! Are you sure?", "makeChangesWillAffectAllPlayers": "You are about to make changes to a live prize drop! This will affect all players for {{brands}} brand(s). Are you sure?", "statusChangedMessage": "Status was changed successfully", "forceRemove": "You are about to force remove prize drop {{ name }}! Are you sure?"}, "LIST": {"title": "Prize Drops", "newPrizeDrop": "New prize drop", "prizeDropTitleSingle": "{{length}} Prize drop", "prizeDropTitleMultiple": "{{length}} Prize drop"}, "GRID": {"id": "Prize drop ID", "featureId": "Feature ID", "name": "Name", "lastRun": "Last Run", "nextRun": "Next Run", "status": "Status", "active": "Active", "customers": "Customers", "games": "Games", "owner": "Owner", "operators": "Operators", "created": "Created", "reports": "Reports"}, "FORM": {"GENERAL": {"NAME": {"title": "Name", "hint": "Daily prize drop"}, "DESCRIPTION": {"title": "Description"}, "CURRENCIES": {"title": "Currencies", "hint": "The prize drop will only be visible to players with these currencies"}, "BASE_CURRENCY": {"title": "Base currency"}, "options": "Options", "showPrizeDropInEngagementBar": "Show prize drop in Engagement bar", "optIn": "Player opt-in required"}, "SCHEDULE": {"timezone": "Time zone"}, "SEGMENTATION": {"playerId": "Player ID", "brandId": "Brand ID", "operator": "Operator", "removePlayer": "remove", "availableEntities": "Available Entities", "selectedEntities": "Selected Entities", "playerIDs": "Enter player IDs", "selectedPlayers": "{{count}} players", "selectedPlayer": "{{count}} player", "allPlayers": "All players", "unableToRead": "Unable to read ", "importValidFile": "Please import valid .csv file", "players": "Players", "playersHintStart": "This Prize drop is visible to", "all": "all", "playersHintEnd": " players", "viewPlayers": "View players", "addPlayersFilter": "Add player filter", "uploadPlayerList": "Upload player list", "playersInCSV": " players in CSV", "previewPlayerList": "Preview player list", "addPlayersDescription": "Add players to the filter by uploading a CSV file with your player IDs. The Prize drop will be shown to the listed players only.", "csvFileDescription": "Your CSV file should have PlayerID and BrandID columns", "example": "Example", "fileType": "File type: CSV", "maximumRows": "Maximum rows: {{maximumRows}}", "currencies": "Currencies", "countries": "Countries", "currenciesHint": "The Prize drop is visible to players with these currencies:", "currenciesSubTitle": "Select the currencies to show this prize drop in", "playersInFilterLabel": " players in Prize drop filter", "invalidCsv": "Your CSV file should have only PlayerID and BrandID columns", "maximumNumberOfRowsIsExceeded": "The maximum number of rows is exceeded.", "removeExistingPlayers": "Remove existing players", "importDescription": "Add players to the filter by uploading a CSV file with your player IDs. The Prize Drop will be shown to the listed players only."}, "PAYOUT": {"notifications": "Notifications", "payoutStructure": "Payout structure", "payoutStructureDescription": "In each row, enter the # of prizes, prize type, the payout amount and drop chances per player.", "configuration": "Configuration", "baseCurrency": "Base currency", "pointsBetAndWinAmounts": "Points by bet and win amounts", "pointsByBetWin": "Points by win multiplier", "nonDroppedPrizes": "Non-dropped prizes", "nonDroppedPrizesDescription": "Choose what to do with the remaining prizes at the end of a feature cycle", "rescheduleOption": "Reschedule", "rescheduleOptionDescription": "Prizes that were not won are added to the next cycle", "removeOption": "Remove", "removeOptionDescription": "Prizes that were not won are removed with no compensation", "winNotificationAmountLabel": "Prize Drops notifications", "winNotificationAmountControl": "Show drop notifications when drop amount is at least"}}, "TOOLTIP": {"duration": "Duration of each prize drop instance repetition from start to finish.", "dateSchedule": "Prize drop instances can be set to run on specific dates or on specific days of the week.", "startTime": "Defines one or more times of day when Prize drop instances start. The difference between two Prize drop start times must be longer than the duration of the prize drop.", "owner": "Choose the entity who can manage this Prize drop", "specificDates": "Run the prize drop on specific dates", "dailySchedule": "Run the prize drop on specific days of the week", "descriptionDailySchedule": "Use this settings to run the prize drop on specific days of the week, between the validity dates", "winNotificationAmount": "Shows a message to all online players when a prize is won"}}, "VALIDATION": {"fieldRequired": "Field is required", "maxPrizesCount": "Total prizes count can't be more than 1000", "onlyDigits": "Only digits", "onlyNumbers": "Only numbers", "maxDecimalAllowed": "Max 2 decimal digits are allowed", "invalidDigitsOnly": "Invalid value. Only digits are allowed", "invalidNumbersOnly": "Invalid value. Only numbers are allowed (max 2 decimal digits)", "maxLength": "Maximum length is {{requiredLength}}", "jackpotNameExists": "Jackpot name already exists", "lessThanMax": "less than '<PERSON>'", "daysOfWeekRequired": "At least one day must be selected", "min": "Minimum value is {{min}}", "max": "Maximum value is {{max}}", "dateMustBeLater": "Selected date must be later than the previous date", "noDecimals": "No decimals are allowed", "positiveNumbers": "Value should be positive and greater than 0", "spacesAreNotAllowed": "Spaces are not allowed", "fieldsHasErrors": "Following fields has errors", "dateGreaterThanNow": "Selected date should be in future", "startGreaterThanEnd": "Start date should be older than end date", "startLessThanEarlier": "Start date should be newer than earlier date", "nameExists": "Name already exists", "redeemAmountMaxGteMin": "Redeem cap should be greater or equal than Min. coins to Redeem", "redeemMinAmountGteAmount": "Min. coins to Redeem should be greater or equal than Number of Bonus Coins", "redeemMaxAmountGteAmount": "Redeem cap should be greater or equal than Number of Bonus Coins", "invalidColorHexFormat": "Color must be in HEX format", "JSON": "Invalid JSON data: {{value}}", "optionShouldBeSelected": "Option should be selected", "invalidJackpotName": "The Name should be alphanumeric Latin characters with underscore and dashes only. Spaces are not allowed.", "invalidSize": "File size is invalid", "requiredFileType": "Required file type: JPEG, PNG", "invalidImagePath": "Invalid image URL", "urlIsNotCorrect": "Url is not acceptable", "currenciesRequired": "At least 1 currency should be selected", "countriesRequired": "At least 1 country should be selected", "notAllowedToHavePromotion": "Selected Merchant is not allowed to have Freebet Promotion. Please contact customer support to enable \"Internal Promotions\"", "invalidCsv": "Your CSV file should have only PlayerID and BrandID columns", "maximumNumberOfRowsIsExceeded": "The maximum number of rows is exceeded.", "shouldNotBeEmpty": "Value should not be empty"}, "COMPONENTS": {"GAMES_SELECT_MANAGER": {"chooseTheGames": "Choose the games where this feature will run.", "selectedGamesTitle": "Selected Games", "availableGamesTitle": "Games", "availableLabelsTitle": "Labels", "searchGamesPlaceholder": "Search by game name, code or label", "searchLabelsPlaceholder": "Search by label name", "btnAddGames": "Add", "btnRemoveGames": "Remove", "btnIntersect": "Intersect", "selectAll": "Select All", "inlineGamesCount": "{{count}} games", "countGamesInIntersection": "{{count}} games in intersection", "countGamesInSelectedLabels": "{{count}} games in selected labels", "btnAddIntersection": "Add Intersection ({{count}} games)", "gamesNotFound": "Games Not Found", "noGamesToShow": "That's all folks! No more games to show.", "game": "Game", "itemNotAvailable": "Item {{ id }} is not available anymore", "validationMessage": "Selected games field should not be empty", "coinValue": "Coin value", "tbPerGame": "TB per game", "applyToAllGames": "Apply to all games", "PREVIEW": {"title": "Games in Category. Preview", "subtitle": "Games in Category {{count}}", "btnCancel": "Cancel", "btnApply": "Apply"}, "emptySelectedGames": "Add games to this category using search", "emptySelectedGamesAlter": "No selected games", "VIEW_ALL": "View all", "VIEW_SELECTED": "View selected"}, "MULTISELECT": {"multiple": "Multiple values", "search": "Search"}, "MENU_SELECT": {"clear": "Clear", "cancel": "Cancel", "apply": "Apply", "search": "Search"}, "COLUMNS_MANAGEMENT": {"tooltip": "Columns management", "menuTitle": "Show columns"}, "BULK_ACTIONS": {"messageConfirm": "Do you really want to make the action?", "messageLess": "Only {{availableRowsNumber}} of {{checkedRowsNumber}} rows are available to make this action. Do you really want to continue?", "messageData": "There are no available data to make this action.", "btnYes": "Yes", "btnNo": "No", "btnOk": "Ok"}, "GRID": {"EMPTY_LIST": "No items found", "TOTAL": "Total", "COLLAPSE": "Collapse", "RELOAD": "Reload", "LOADING": "Loading...", "RESULT_DOANLOAD_CSV": "Download CSV", "RESULT_PRINT": "Print current page", "RESULT_EXPORT": "Export current page", "RESULT_EXPORT_CSV": "Export CSV", "RESULT_OPEN_NEW_TAB": "Open in new tab", "RESULT_EXPORT_FILENAME": "Export {title} {date} (Page {page})", "GRID_OPEN_DETAILS_POPUP": "View", "GRID_TOGGLE_FILTER": "Filter", "GRID_COLUMNS_VISIBILITY": "Columns visibility", "GRID_ROW_ACTIONS": "Actions", "ITEMS_FOUND": "items found"}, "BRANDS_MANAGER": {"ownerEntity": "Owner entity"}, "DATERANGE": {"today": "Today", "yesterday": "Yesterday", "last3Days": "Last 3 days", "last7Days": "Last 7 days", "thisMonth": "This month", "lastMonth": "Last month", "customPeriod": "Custom period"}, "DATE_PICKER": {"clear": "Clear", "cancel": "Cancel", "apply": "Apply"}, "WIDGET": {"andMore": "and {{number}} more"}, "DATE_RANGE": {"today": "Today", "yesterday": "Yesterday", "last3Days": "Last 3 days", "last7Days": "Last 7 days", "last14Days": "Last 14 days", "last30Days": "Last 30 days", "last90Days": "Last 90 days", "next7Days": "Next 7 days", "next14Days": "Next 14 days", "next30Days": "Next 30 days", "next90Days": "Next 90 days", "thisMonth": "This month", "lastMonth": "Previous month", "nextMonth": "Next month", "customPeriod": "Custom period", "clear": "Clear", "cancel": "Cancel", "apply": "Apply"}}, "JACKPOT": {"noLimitType": "No Limit", "timeLimitType": "Time Limit", "amountLimitType": "Amount Limit", "splitPrizeNoLimitType": "Split Prize - No Limit", "splitPrizeTimeLimitType": "Split Prize - Time Limit", "splitPrizeAmountLimitType": "Split Prize - Amount <PERSON>", "NOTIFICATIONS": {"removeJackpot": "Are you sure you want to delete \"{{name}}\" jackpot?", "deleted": "Jackpot \"{{name}}\" successfully deleted!", "makeChanges": "You are about to make changes to a live Jackpot! Are you sure?", "makeChangesWillAffectAllPlayers": "You are about to make changes to a live Jackpot! This will affect all players for {{brands}} brand(s). Are you sure?", "statusChangedMessage": "Status was changed successfully", "forceRemove": "You are about to force remove jackpot {{ name }}! Are you sure?"}, "FORM": {"CONFIGURATION": {"jackpotConfiguration": "Jackpot configuration", "jackpotRtp": "Jackpot RTP", "jackpotRtpPlaceholder": "<PERSON><PERSON>", "jackpotPools": "Jackpot Pools", "jackpotPoolsPlaceholder": "Choose jackpot pools", "payoutOptions": "Payout options", "payoutOptionsDescription": "Choose one payment option for these Jackpot pools.", "jackpot": " Jackpot", "settings": "Settings", "baseCurrency": "Base currency", "betsThreshold": "Eligibility threshold", "contribution": "Jackpot contribution", "avgDailyBets": "Avg. Daily bets", "avgDailyBetsPlaceholder": "Choose one", "minQualifyingBet": "Min. qualifying bet", "maxEligibleStakeAmount": "Max. eligible stake amount", "dropAmount": "Drop amount", "dropFrequency": "Drop frequency", "dropTime": "Drop Time", "timeZone": "Time Zone", "jackpotSettings": "Jackpot settings", "jackpotType": "Jackpot Type", "main": "Main pot percentage", "period": "Eligibility period", "seedAmount": "Seed amount", "sharedEven": "Shared pot percentage - even", "sharedRelative": "Shared pot percentage - relative", "showSplitPrizeProgressBarLabel": "Show split prize progress bar", "winNotificationAmountLabel": "Show win notification toasters when drop amount is at least", "winNotificationToastersLabel": "Win notification toasters", "hours": "hours", "amountJackpotSettings": "Amount Jackpot settings", "noLimitJackpotSettings": "No Limit Jackpot settings", "dailyJackpotSettings": "Daily Jackpot settings", "hourlyJackpotSettings": "Hourly Jackpot settings", "sharedPrize": "Shared Prize", "dropAmountDisplay": "Drop amount display", "amount": "Amount", "noLimit": "No Limit", "daily": "Daily", "hourly": "Hourly", "seed": "Seed", "option": "Option ", "avgWin": "Avg. win /", "dailyDrops": "Daily Drops", "amountSettingsDescription": "Set the drop amount to show in game client for each currency (available in non-regulated markets only).", "currency": "<PERSON><PERSON><PERSON><PERSON>", "actualDropAmount": "Actual Drop amount", "dropAmountToShow": "Drop amount to show", "dropAtMidnight": "Drop at 00:00 midnight", "dropNotificationsTooltip": "Show notifications to online players when this pool is won."}, "GENERAL": {"DESCRIPTION": {"title": "Description"}, "NAME": {"title": "Name"}, "options": "Options", "showJackpotLabel": "Show jackpot in Engagement Bar", "allowExpandEngagementBar": "Allow Engagement bar expand/collapse", "regulation": "Regulation", "jurisdiction": "Juris<PERSON>"}, "INFO": {"jackpotStatus": "Jackpot Status", "active": "Active", "status": "Status", "activity": "Activity", "activated": "Activated", "drops": "Drops", "lastDrop": "Last Drop", "lastAmount": "Last Amount", "reports": "Reports", "jackpotReports": "Jackpot Reports", "noReportsToShow": "No reports to show yet", "info": "Info", "created": "Created", "modified": "Modified", "id": "Jackpot ID", "featureId": "Feature ID"}, "SEGMENTATION": {"currencies": "Currencies", "countries": "Countries*", "currenciesHint": "The Jackpot is visible to players with these currencies", "playersInFilterLabel": " players in Jackpot filter", "playersHintStart": "This jackpot is visible to", "importDescription": "Add players to the filter by uploading a CSV file with your player IDs. The Jackpot will be shown to the listed players only."}, "UI": {"tickupTitle": "Constant Jackpot Tickup", "tickupNote": "Advance jackpot tickers at a constant rate even when traffic is low", "decrease": "Initial Decrease", "interval": "Pool Interval"}}, "GRID": {"id": "Jackpot ID", "featureId": "Feature ID", "actions": "Actions", "active": "Active", "duplicate": "Duplicate", "edit": "Edit", "entities": "Entities", "games": "Games", "lastDrop": "Last Drop", "lastAmount": "Last Amount", "name": "Name", "no": "No", "status": "Status", "type": "Type", "yes": "Yes", "owner": "Owner", "operators": "Operators", "pools": "Pools", "created": "Created", "reports": "Reports", "playersSegmentation": "Players Segmentation"}, "LIST": {"jackpotTitleMultiple": "{{length}} Jackpots", "jackpotTitleSingle": "{{length}} Jackpot", "newJackpot": "New jackpot", "title": "Must-Win Jackpots"}, "TOOLTIP": {"baseCurrency": "Contribution is converted to base currency according to latest available conversion rate. JP tickers and JP wins are converted back to player currency according to latest rate.", "contribution": "The % of each bet counted as player contribution towards the jackpot.", "seed": "The initial jackpot amount, and guaranteed minimum prize. The Jackpot tickers start from this amount. The seed amount must be no greater than 1/4 the expected JP average win amount.", "dropFrequency": "The chance to win the jackpot, defined as 1:X for each 1 currency unit. EG: 1:1,000,000 for a EUR 1 bet.", "dropTime": "Times of the day, eg 00:00, 18:00. The jackpot is guaranteed to drop before this time every day.", "timeZone": "Time zone for drop time of day.", "dropAmount": "The jackpot is guaranteed to drop before this amount is reached.", "main": "The percentage of the JP that goes to the sole winner (eg 70%).", "sharedEven": "The percentage of the JP shared evenly among all eligible players.", "sharedRelative": "The percentage of the JP shared among all eligible players relative to their Total Bets during the eligibility period.", "period": "The time span during which player's Total Bets are counted towards shared pot eligibility.", "betsThreshold": "The minimum Total Bets required for shared pot eligibility (in base currency).", "owner": "Choose the entity who can manage this Jackpot"}, "UI": {"POOL": {"titleLogo": "Jackpot logo"}, "ENGAGEMENT": {"titleLogo": "Central image"}, "browse": "Browse", "imageURL": "Image URL", "uploadImage": "Upload image", "deleteImage": "Delete Image", "maxFileSize": "Max file size {{size}}Kb"}, "configurationTab": "Configuration", "createJackpot": "Create Jackpot", "editJackpot": "Edit Jackpot {{name}}", "gamesTab": "Games", "generalTab": "General", "scheduleTab": "Schedule", "segmentationTab": "Segmentation", "operatorsTab": "Operators", "createdJackpotMessage": "Jackpot was successfully created", "updatedJackpotMessage": "Jackpot \"{{name}}\" was successfully updated"}, "PROMOTION": {"created": "Promotion {{ title }} was successfully created", "updated": "Promotion {{ title }} was successfully updated", "create": "Create Promotion {{name}}", "edit": "Edit Promotion {{name}}", "statusChanged": "Status was successfully set to {{ status }}", "merchantPromotionsDisabled": "Promotions are not available for you, as your merchant doesn’t support promotions. Please contact customer support for more details.", "TYPES": {"freebet": "Free Bets", "bonus_coin": "Bonus Coins", "freebet_simple": "Simple Free Bets", "rebate": "Rebates", "virtual_money": "Virtual Money", "unknown": "Unknown"}, "STATE": {"new": "New", "pending": "Pending", "inProgress": "In Progress", "alt_in_progress": "Started", "finished": "Finished", "alt_finished": "Redeemed", "expired": "Expired"}, "STATUS": {"active": "Yes", "inactive": "No"}, "LIST": {"totalMultiple": "{{total}} Promotions", "totalSingle": "{{total}} Promotion", "newPromotion": "New promotion", "title": "Promotions"}, "GRID": {"name": "Name", "id": "Promo ID", "externalId": "External ID", "type": "Type", "startDate": "Start Date", "endDate": "End Date", "state": "Status", "status": "Active", "path": "Operator", "games": "Games", "players": "Players", "reports": "Reports", "created": "Created"}, "CSV": {"title": "Title", "id": "Id", "brandPath": "Operator", "type": "Type", "createdUserId": "Funding", "status": "Status", "state": "State", "totalParticipated": "Participants", "labels": "Labels", "startDate": "Start Date / Time (GMT +03:00)", "endDate": "End Date / Time (GMT +03:00)", "createdAt": "Creation Date / Time (GMT +03:00)", "actions": "Actions"}, "TABS": {"general": "General", "schedule": "Schedule", "rewards": "Rewards", "games": "Games", "operators": "Operators", "segmentation": "Segmentation"}, "FORM": {"GENERAL": {"EXTERNAL_ID": {"title": "External promo ID (optional)"}, "TITLE": {"title": "Name"}, "DESCRIPTION": {"title": "Description"}}, "SCHEDULE": {"startTime": "Start time", "endTime": "End time", "timezone": "Time zone"}, "REWARDS": {"type": "Reward Type", "BONUS_COIN": {"numberOfBonusCoins": "Bonus Coins per player", "expiration": "Expiration", "minCoinsToRedeem": "Min. BNS required to redeem", "maxCoinsToRedeem": "Redeem cap", "TOOLTIP": {"numberOfBonusCoins": "How many Bonus Coins will be awarded to each player", "minCoinsToRedeem": "A player is required to have at least this many Bonus Coins in order to redeem the coins into real money", "maxCoinsToRedeem": "Set the max amount of BNS each player is allowed to redeem. Any BNS winnings above this amount will be capped.", "expiration": {"false": "The player's BNS expiration will start when the player is added to a running promotion, or when the promotion is started with the player already added.", "true": "The player's BNS expiration will start when the player launches a game participating in the promotion."}}, "EXPIRATION_PERIOD": {"days": "days", "hours": "hours"}, "EXPIRATION_TYPE": {"promotion_started": "Start expiration when the player is added to the promotion", "game_launch": "Start expiration on player's 1st game launch"}}, "FREEBET": {"rewardAmount": "Reward amount", "numberOfFreeBets": "Number of Free Bets", "rewardProcessing": "Reward processing", "expiration": "Expiration", "in": "in", "on": "on", "hours": "hours", "days": "days", "weeks": "weeks", "months": "months", "expirationDate": "Expiration date", "TOOLTIP": {"numberOfFreeBets": "The # of free bets awarded to each player in the promotion.", "expiration": "After expiration, any unused rewards are lost and the promotion is over for the player."}}}, "SEGMENTATION": {"playersHintStart": "This promotion is visible to", "noPlayersInPromotion": "No players in this promotion yet. Click Add players to begin.", "playerCode": "Player ID", "status": "Status", "expiryDate": "Expiry date", "operator": "Operator", "new_players": "{{ length }} new players will be added", "removePlayer": "Are you sure you want remove player {{ playerCode }} from current promotion?", "forceRemovePlayer": "You are about to force remove {{ playerCode }} from promotion! Are you sure?", "invalidCsv": "Your CSV file should have only PlayerID column", "csvFileDescription": "Add players to the promotion by uploading a CSV file with your player IDs.", "csvStructureDescription": "Your CSV file should have PlayerID column only", "maximumNumberOfRowsIsExceeded": "The maximum number of rows is exceeded.", "addPlayers": "Add players", "PLAYER": {"status": {"new": "New", "started": "Started", "awarded": "Awarded", "finished": "Finished", "redeemed": "Redeemed", "expired": "Expired", "redeemable_expired": "Redeemable expired", "revoked": "Revoked", "confirmed": "Confirmed"}}}}}, "TITLE": "Skywind Back-office / Engagement", "LANGUAGES": {"en": "English (EN)", "zh": "中文 (ZH)", "zh-tw": "繁體中文 (ZH-TW)", "ja": "日本人 (JA)", "ms": "Bahasa Malaysia (MS)", "ko": "한국어 (KO)", "th": "ภาษาไทย (TH)", "vi": "<PERSON><PERSON><PERSON><PERSON> (VI)", "id": "Bahasa Indonesia (ID)", "da": "Danish (DA)", "sv": "Swedish (SV)", "de": "Deutsche (DE)", "fr": "Française (FR)", "ENGLISH": {"en": "English (EN)", "id": "Bahasa Indonesia (ID)", "ms": "Bahasa Malaysia (MS)", "da": "Danish (DA)", "it": "Italian (IT)", "ko": "Korean (KO)", "ro": "Romanian (RO)", "zh-cn": "Simplified Chinese (ZH-CN)", "es": "Spanish (ES)", "sv": "Swedish (SV)", "th": "Thai (TH)", "vi": "Vietnamese (VI)", "de": "German (DE)", "fr": "French (FR)", "sr": "Serbian (SR)", "el": "Greek (EL)", "ru": "Russian (RU)", "uk": "Ukrainian (UK)", "ka": "Georgian (KA)", "pt": "Portuguese Europe (PT)", "pt-br": "Portuguese Brazil (PT-BR)", "zh-tw": "Traditional Chinese (ZH-TW)", "nl": "Dutch (NL)", "ja": "Japanese (JA)"}}, "HEADER": {"Logout": "Logout"}, "HUBS": {"analytics": "Analytics", "casino": "Casino", "engagement": "Engagement", "studio": "Live Studio"}, "SETTINGS": {"title": "Settings", "timezone": "Timezone", "pageSize": "Page size", "theme": "Choose client theme", "dateFormat": "Choose date format", "timeFormat": "Choose time format", "currencyFormat": "Choose currency format", "currencyFormatDefault": "Use system's default", "reset": "Reset to default", "apply": "Apply", "decline": "Cancel", "pleaseProvideAappToken": "Please provide app token", "changeTwofa": "Change 2FA Methods", "enabledTwofaTypes": "Enabled Two-Factor Types", "btnAddNewTwofaType": "Add New Type", "defaultTwofaType": "Default Type", "markAsDefaultTwofaType": "<PERSON> as <PERSON><PERSON><PERSON>", "successTwofaMarkedAsDefault": "Auth type \"{{ name }}\" was marked as default", "successTwofaTypeAdded": "Auth type \"{{ name }}\" was added successfully", "notificationSuccess": "App settings saved"}, "LANGUAGE": {"Chinese": "中文", "English": "English"}, "ACCESS_DENIED": {"title": "Access Denied", "notEnoughPermissions": "Not enough permissions to view this page.", "pleaseContactCustomerSupport": "Please contact customer support"}}