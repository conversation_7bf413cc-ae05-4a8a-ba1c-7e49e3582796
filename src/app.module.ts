import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { APP_INITIALIZER, NgModule } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import {
  hub_config_init, SwBrowserTitleService, SwDexieModule,
  SwHubConfigService,
  SwHubInitModule,
  SwuiGridModule,
  SwuiTopMenuModule,
  WidgetAddTypes
} from '@skywind-group/lib-swui';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { ReportTdWidgetModule } from './common/grid/widgets/td/report/report.module';
import { ReportTdWidgetComponent } from './common/grid/widgets/td/report/report.widget';
import { CountriesService } from './common/services/countries.service';
import { EntityService } from './common/services/entity.service';
import { CommonServiceModule } from './common/services/common/common.service.module';

@NgModule({
  declarations: [
    AppComponent
  ],
  imports: [
    CommonModule,
    BrowserAnimationsModule,
    HttpClientModule,
    AppRoutingModule,
    SwHubInitModule.forRoot({
      name: 'engagement',
      langs: [
        {
          id: 'en',
          title: 'LANGUAGE.English',
          image: 'img/flags/gb.png',
        },
        {
          id: 'zh',
          dialect: 'zh-cn',
          title: 'LANGUAGE.Chinese',
          image: 'img/flags/zh.png',
        },
      ],
      defaultLang: 'en',
      logo: 'img/logo-skywind.png',
      logoSymbols: 'img/logo-white.png',
      auth: {
        blacklistedRoutes: [
          '/locale/.*',
          '/api/config'
        ]
      },
      lastLocationExceptions: [
        {
          url: '/pages/404',
          replaceTo: '/pages'
        }
      ],
      lastUnknownLocationExceptions: ['/', '/pages', '/pages/404']
    }),
    ReportTdWidgetModule,
    SwuiGridModule.forRoot({
      widgets: [
        { type: 'tdreport', component: ReportTdWidgetComponent },
      ],
      widgetAddType: WidgetAddTypes.EXPAND,
    }),
    CommonServiceModule,
    SwuiTopMenuModule,
    SwDexieModule.forRoot('SwUboHubEngagement'),
  ],
  providers: [
    SwBrowserTitleService,
    SwHubConfigService,
    EntityService,
    CountriesService,
    { provide: APP_INITIALIZER, useFactory: hub_config_init, deps: [SwHubConfigService], multi: true },
  ],
  bootstrap: [AppComponent]
})
export class AppModule {
}
