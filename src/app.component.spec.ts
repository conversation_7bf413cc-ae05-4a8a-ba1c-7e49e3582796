import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed, waitForAsync } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import {
  SwBrowserTitleService,
  SwHubAuthService, SwHubConfigService, SwHubInitService, SWUI_HUB_MESSAGE_CONFIG, SwuiTopMenuModule
} from '@skywind-group/lib-swui';
import { AppComponent } from './app.component';
import { MockAuthService } from './common/services/mock-auth.service';

describe('AppComponent', () => {
  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        RouterTestingModule,
        TranslateModule.forRoot(),
        SwuiTopMenuModule
      ],
      declarations: [
        AppComponent
      ],
      providers: [
        SwBrowserTitleService,
        { provide: SWUI_HUB_MESSAGE_CONFIG, useValue: {} },
        { provide: SwHubAuthService, useClass: MockAuthService },
        { provide: SwHubInitService, useValue: {} },
        { provide: SwHubConfigService, useValue: {} },
      ]
    }).compileComponents();
  }));

  it('should create the app', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.debugElement.componentInstance;
    expect(app).toBeTruthy();
  });
});
