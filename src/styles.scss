@import '~@skywind-group/lib-swui/styles/themes/theme';
@import '~@skywind-group/lib-swui/styles/fonts/fonts.scss';

@import "theme/theme";


@include attr-x('font-weight', 9, 100, ''); // Use: add class .font-weight500

@include attr-x('padding', 20, 4, 'px'); // Use: add class .padding16
@include attr-x('padding-right', 20, 4, 'px');
@include attr-x('padding-left', 20, 4, 'px');
@include attr-x('padding-top', 20, 4, 'px');
@include attr-x('padding-bottom', 20, 4, 'px');

@include attr-x('margin', 20, 4, 'px');
@include attr-x('margin-top', 20, 4, 'px'); // Use: add class .margin-top16
@include attr-x('margin-bottom', 20, 4, 'px');
@include attr-x('margin-right', 20, 4, 'px');
@include attr-x('margin-left', 20, 4, 'px');

// Global overrides

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

// TO DO move to library
.mat-card:not([class*=mat-elevation-z]) {
  box-shadow: none;
}

// Helpers
.vertical-form {
  display: flex;
  flex-direction: column;
}

.no-padding {
  padding: 0 !important;
}

.no-margin {
  margin: 0 !important;
}

.no-margin-top {
  margin-top: 0 !important;
}

.no-border-radius {
  border-radius: 0 !important;
}

.no-shadow {
  box-shadow: none !important;
}

.display-block {
  display: block;
}

.text-uppercase {
  text-transform: uppercase;
}

.bordered {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
}

.scroll {
  overflow: auto;
}

// End Helpers

.top-bar {
  padding: 12px 32px !important;
}

.link,
.widget-link {
  @extend .color-primary !optional;
  text-decoration: none;
}

// Table
.sw-mat-table {
  border-collapse: collapse;

  th {
    font-size: 14px;
    line-height: 18px;
    font-weight: 500;
    color: #2A2C44;
  }

  td, th {
    text-align: right !important;
    padding: 8px 0 8px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);

    &:first-child {
      padding-left: 0;
    }

  }
}

.table-sticky {
  position: relative;
  height: 496px;

  &__header {
    display: flex;
    align-items: center;
    height: 56px;
    padding: 0 24px;
    border-bottom: 1px solid rgba(0, 0, 0, .12);
    box-sizing: border-box;
  }

  &__body {
    height: calc(100% - 56px);
    overflow: auto;
  }

  &__search {
    width: calc(100% - 110px);
    max-width: 330px;
    margin-left: auto;

    &--full {
      width: 100%;
      max-width: 100%;
      margin-left: 0;
    }
  }

  table {
    width: 100%;
    background: transparent;

    th {
      background-color: #fff;

      &:first-child {
        border-top-left-radius: 4px;
      }

      &:last-child {
        border-top-right-radius: 4px;
      }
    }
  }
}

// Form-field
.mat-form-field-disabled {
  .mat-form-field-prefix {
    color: rgba(0, 0, 0, 0.38);
  }
}

.mat-form-field-prefix {
  color: #5B5B5B;
}

.no-field-padding {
  &.mat-form-field-appearance-outline {
    .mat-form-field-wrapper {
      padding-bottom: 0;
    }

    .mat-form-field-infix {
      padding: 1.2em 0 1em 0;
      border: 0;
      height: 44px;
      box-sizing: border-box;
    }

    .mat-select-arrow-wrapper {
      transform: translateY(0);
    }

    .mat-form-field-prefix {
      top: 0;
    }
  }

  mat-icon {
    position: relative;
    top: 6px;
  }


}


//Errors
.error-message {
  padding: 20px;
  border-radius: 4px;
  background-color: #ff8a80;
  font-size: 14px;
  line-height: 16px;

  &__heading {
    display: block;
    margin-bottom: 4px;
  }

  &__list {
    padding: 0;
    margin: 0;
    padding-left: 15px;

    li {
      margin-bottom: 8px;
    }

    &--nested {
      font-size: 12px;

      li {
        margin-bottom: 4px;
      }
    }
  }
}

.table-date {
  display: block;
  min-width: 100px;
}

.sidebar-list {
  &__icon {
    #Path_500 {
      stroke: #2196f3;
    }
  }
}

td {
  .actions {
    justify-content: flex-end;
  }
}

.help-icon {
  color: #69768E;
  cursor: help;
}

.card-help-icon {
  position: absolute;
  top: 20px;
  right: 20px;
}


.mat-tooltip {
  font-size: 14px !important;
  line-height: 18px !important;
}

textarea.mat-input-element {
  overflow: hidden !important;
}

.cdk-overlay-pane.import-dialog {
  position: relative !important;
}

.mat-dialog-close.mat-button {
  position: absolute;
  top: 0;
  right: 0;
  padding: 5px;
  line-height: 14px;
  min-width: auto;
}

.upload-players-dialog {
  .sw-grid__header {
    height: 20px !important;
  }

  .mat-sort-header-button {
    font-weight: bold !important;
  }

  tr {
    height: 38px !important;
  }
}

.uploaded-players-grid {
  .sw-grid__header {
    height: 15px !important;
  }

  .mat-sort-header-button {
    font-weight: bold !important;
  }

  .mat-column-row-actions-column {
    font-weight: bold !important;
    text-align: center;
  }

  tr {
    &:hover {
      .show-on-hover {
        opacity: 1;

        svg:hover {
          fill: #5591cd;
        }
      }
    }
  }

  td {
    width: 40px !important;
  }

  .actions {
    justify-content: center;
  }

  .sw-grid__pagination {
    .mat-paginator-container {
      justify-content: flex-start;
    }
  }
}

// Info sidebar
$sdColorGrey: #9CA4B4;

.sd {
  &__item {
    margin-bottom: 2px;
    padding: 24px;
    background: #fff;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__title {
    margin-bottom: 16px;
    font-size: 20px;
    font-weight: 500;
    line-height: 1;
  }
}

.sd-list {
  list-style: none;
  margin: 0;
  padding: 0;

  &__item {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__title {
    width: 106px;
    flex-shrink: 0;
    font-weight: 500;
  }

  &__content {
    width: calc(100% - 106px);
  }
}

.sd-icon-link {
  display: flex;
  font-weight: 500;
  color: #1468cf;
  text-decoration: none;

  &__icon {
    margin-right: 20px;
  }
}

.sd-schedule {
  width: 100%;

  &__list {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }

  &__item {
    width: 50%;
    flex-shrink: 0;
    font-size: 14px;
    white-space: nowrap;

    &:nth-child(2) {
      order: 3;
    }

    &:nth-child(3) {
      order: 5;
    }

    &:nth-child(4) {
      order: 2;
    }

    &:nth-child(5) {
      order: 4;
    }

    &:nth-child(6) {
      order: 6;
    }
  }

  &__hint {
    margin-top: 12px;
    font-size: 13px;
    line-height: 1;
    color: $sdColorGrey;
  }
}

.sd-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 16px 0;
  color: $sdColorGrey;

  &__icon {
    width: 32px !important;
    height: 32px !important;
    font-size: 32px !important;
  }

  &__label {
    font-size: 14px;
  }
}

// End Info sidebar

.sw-grid-layout {
  display: flex;

  &__filter {
    flex-shrink: 0;
    flex-grow: 0;
    width: 0;
    overflow: hidden;
    transition: width 300ms ease;

    &.visible {
      width: 320px;
      padding-left: 32px;
    }
  }

  &__table {
    flex-grow: 1;
    width: calc(100% - 320px)
  }

  @media (max-width: 1024px) {
    flex-direction: column;
    &__filter {
      width: 0;
      padding-left: 0;
      transition: width 300ms ease;

      &.visible {
        width: 100%;
        padding-top: 32px;
      }
    }
    &__table {
      width: 100%;
    }
  }
}

.report-disabled-cursor {
  cursor: not-allowed !important;
}

.cdk-global-scrollblock {
  overflow-y: hidden !important;
}

.games-checkbox {
  .mat-checkbox-layout {
    display: flex;
    overflow: hidden;
  }

  .mat-checkbox-label {
    overflow: hidden;
  }
}

.sw-jackpot-info-editor {
  .mat-dialog-container {
    padding: 0;
  }

  .angular-editor {
    display: flex;
    flex-direction: column;
    height: 100% !important;

    .angular-editor-wrapper {
      flex: 1;
      z-index: 0;
      height: calc(100% - 70px);

      &:focus {
        outline: none !important;
      }

      @media (max-width: 1024px) {
        height: calc(100% - 90px);
      }
      @media (max-width: 536px) {
        height: calc(100% - 126px);
      }
    }

    .angular-editor-textarea {
      margin-top: 0;

      &:focus {
        outline: none !important;
      }
    }

    .angular-editor-button {
      height: 34px;
      width: 34px;

      &#link- {
        display: none;
      }

      &#unlink- {
        display: none;
      }

      &#insertVideo- {
        display: none;
      }

      i {
        font-size: 14px;
      }
    }

    .angular-editor-toolbar-set {
      height: 36px !important;
      margin: 3px !important;
    }

    ae-select {
      display: flex !important;
      align-items: center;
      height: 36px !important;

      .ae-font {
        .ae-picker-options {
          border-bottom-right-radius: 4px;
          border-bottom-left-radius: 4px;
          overflow: hidden;
          border: none;
          box-shadow: rgba(0, 0, 0, .2) 0 4px 4px;
          margin-top: 3px;

          .ae-picker-item {
            background: #fff;
          }
        }
      }
    }

    .select-font {
      display: none !important;
    }

  }

  .angular-editor-toolbar {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    height: 100%;
    padding: 0 13px !important;
    border: none !important;
    background: #fff !important;

  }

  angular-editor-toolbar {
    display: block;
    height: 70px;
    @media (max-width: 1024px) {
      height: 90px;
    }
    @media (max-width: 536px) {
      height: 126px;
    }
  }
}

.sw-jackpot-info-editor {
  .mat-dialog-container {
    border-radius: 0;
  }
}

.angular-editor .angular-editor-wrapper .angular-editor-textarea {
  padding: 0 !important;
}

.ranges {
  .mat-form-field-appearance-outline {
    &.readonly {
      color: rgba(0, 0, 0, .26);

      * {
        cursor: not-allowed;
      }

      .mat-form-field-outline-thick {
        opacity: 0 !important;
      }

      .mat-form-field-outline {
        &:not(.mat-form-field-outline-thick) {
          opacity: 1 !important;
        }
      }
    }
  }
}

.sw-tab {
  &__header {
    display: flex;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    margin-bottom: 20px;
  }

  &__th {
    position: relative;
    display: flex;
    align-items: center;
    height: 48px;
    padding: 0 20px;
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    letter-spacing: 1.25px;
    text-align: center;
    text-transform: uppercase;
    color: #616161;

    &--active {
      color: #1468cf;

      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        display: block;
        width: 100%;
        height: 2px;
        background-color: #1468cf;
        transition: background-color 0.15s ease-in-out;
      }
    }

    &--error {
      color: #f44336;

      &:after {
        background-color: #f44336;
      }
    }
  }

  &__content {
    &--hidden {
      display: none;
    }
  }
}

.non-dropped-radio {
  .mat-radio-container {
    margin-bottom: 25px;
  }
}

.players-filter-dialog {
  .mat-dialog-container {
    height: 651px;
    padding: 0;
  }
  .mat-tab-body-content {
    padding: 24px 32px;
  }
  .mat-tab-list {
    display: flex;
    justify-content: center;
  }
}

.coin-prefix {
  &.mat-form-field-appearance-outline {
    &.mat-form-field-hide-placeholder {
      .mat-form-field-prefix {
        top: -.25em;
      }
    }

    .mat-form-field-prefix {
      top: 0;
    }
  }
}
