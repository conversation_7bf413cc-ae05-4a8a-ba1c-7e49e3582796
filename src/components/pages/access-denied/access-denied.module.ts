import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { PERMISSIONS_NAMES, SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { AccessDeniedComponent } from './access-denied.component';


const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: AccessDeniedComponent,
    data: {
      permissions: [PERMISSIONS_NAMES.GRANTED_ALL],
    },
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    MatCardModule,
    TranslateModule,
    SwuiPagePanelModule,
  ],
  declarations: [AccessDeniedComponent],
})
export class AccessDeniedModule {
}
