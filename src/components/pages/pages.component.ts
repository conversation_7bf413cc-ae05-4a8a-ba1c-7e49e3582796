import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SwHubAuthService, SwHubInitService, SwuiSidebarService } from '@skywind-group/lib-swui';
import { SwuiMenuItem } from '@skywind-group/lib-swui/swui-menu/swui-menu.interface';
import { Observable } from 'rxjs';
import { MENU } from './pages.menu';

@Component({
    encapsulation: ViewEncapsulation.None,
    styleUrls: ['./pages.component.scss'],
    templateUrl: 'pages.component.html'
  }
)
export class PagesComponent implements OnInit {
  readonly menu: SwuiMenuItem[];
  readonly isSidebarCollapsed$: Observable<boolean>;

  constructor( private readonly hubService: SwHubInitService,
               { isCollapsed }: SwuiSidebarService,
               { routeConfig }: ActivatedRoute,
               router: Router,
               authService: SwHubAuthService) {
    this.isSidebarCollapsed$ = isCollapsed;

    const menuMap = (routeConfig?.children || []).reduce((res: Record<string, any>, item) => {
      const path = `/pages/${item.path || ''}`;
      res[path] = item;

      return res;
    }, {});

    this.menu = MENU.filter(item => {
      const route = menuMap[item.url];

      if (route.data) {
        return authService.areGranted(route.data.permissions);
      }

      return true;
    });

    if (router.url === '/pages') {
      router.navigate([this.menu.length ? this.menu[0].url : 'pages/404']);
    }

  }

  ngOnInit(): void {
    this.hubService.initInactivityWatcher();
  }
}
