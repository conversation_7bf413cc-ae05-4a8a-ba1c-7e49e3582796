import { Feature } from './feature';

export const READONLY_JURISDICTION_ID = 'ph-must-win-multi-type-it';

export interface JackpotPollDataOption {
  id: string;
  title?: string;
  pool: string[];
  subType: string[];
}

export interface JackpotJurisdictionDataOption {
  id: string;
  type: string;
  title: string;
}

export const JACKPOT_NAMES = [
  { id: 'grand', title: 'No limit jackpot' },
  { id: 'hourly', title: 'Hourly jackpot' },
  { id: 'daily', title: 'Daily jackpot' },
  { id: 'minor', title: 'Minor jackpot' },
  { id: 'major', title: 'Major jackpot' },
  { id: 'mega', title: 'Mega jackpot' },
];

export interface PayoutItemOption {
  id?: string;
  type: string;
  subType?: string;
  seed?: number;
  avgWin?: number;
  dailyDrops?: number;
}

export interface PayoutItem {
  id: string;
  mathId: number;
  rtp: string;
  dailyAvgBets: number;
  title: string;
  option: PayoutItemOption[];
}

export interface DropAmountDisplayPerCurrencyItem {
  currency: string;
  value: number;
}

export interface WinNotification {
  amount?: number;
  type: 'animation' | 'toaster' | 'toaster_info';
}

export interface BaseSettings {
  type: string;
  splitPrize?: SplitPrizeDistribution;
  winNotification: WinNotification | null;
}

export interface AmountSettings extends BaseSettings {
  dropAmountDisplay: number;
  dropAmountDisplayPerCurrency: DropAmountDisplayPerCurrencyItem[];
}

export interface DailySettings extends BaseSettings {
  dropTime: number;
  timezone: string;
}

export interface JackpotConfiguration {
  poolId: string;
  baseCurrency: string;
  minQualifyingBet: number;
  maxEligibleStakeAmount: number;
  poolType: string;
  payout: PayoutItem;
  settings: BaseSettings[];
}

export interface SplitPrizeDistribution {
  potDistribution: {
    main: number;
    sharedEven: number;
    sharedRelative: number;
  };
  eligibility: {
    period: number;
    betsThreshold: number;
  };
  showProgressBar: boolean;
}

export interface JackpotUiPoolInfo {
  name: string;
  logo: string;
  text: string;
}

export interface JackpotUiPoolTickup {
  interval: number;
  decrease: number;
}

export interface JackpotUiPool {
  type: string;
  assets?: any;
  priority: number;
  color?: string;
  animationColor?: string;
  tickup?: JackpotUiPoolTickup;
  info: {
    [lang: string]: JackpotUiPoolInfo;
  };
}

export interface JackpotUiInfo {
  header?: string;
  footer?: string;
}

export interface JackpotUiInfoTemplate {
  main?: string;
  amount?: string;
  noLimit?: string;
  time?: string;
}

export interface JackpotUiInfoPreviewPool {
  name: string;
  priority: number;
  data: {
    pool_name: string;
    seed: number;
    avg_win: number;
    currency_symbol: string;
  };
}

export interface JackpotUiInfoPreviewItem {
  lang: string;
  rtp: string;
  pools: JackpotUiInfoPreviewPool[];
}

export interface JackpotUiLobbyGame {
  gameCode: string;
  isVisible: boolean;
}

export interface JackpotUiLobby {
  games: JackpotUiLobbyGame[];
  css?: string;
}

export interface JackpotUiFeatures {
  pools: JackpotUiPool[];
  engagement: {
    imageUrl: {
      [lang: string]: { url: string };
    },
    css?: string;
  };
  info: {
    [lang: string]: JackpotUiInfo;
  };
  infoUrl: string;
  lobby: JackpotUiLobby;
}

export interface JackpotUi {
  type: string;
  assets?: any;
  features: JackpotUiFeatures;
  poolId?: string;
}

export interface Jackpot extends Feature {
  type: 'jackpot';
  configuration: JackpotConfiguration;
  ui: JackpotUi;

  activated?: string;
  drops?: {
    pool: string;
    value: number;
  }[];

  lastDropAmount?: number;
  lastDropTs?: string;

  jpnId?: string;
}
