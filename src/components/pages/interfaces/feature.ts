import { SchemaFieldMapItem } from '../../../common/models/schema-field-map-item';
import { JackpotConfiguration } from './jackpot';
import { PrizeDropConfiguration } from './prize-drop';
import { TournamentConfiguration, TournamentSegmentation } from './tournament';

export const STATUS_MAP: SchemaFieldMapItem[] = [
  { id: 'enabled', title: 'Enabled', class: 'sw-chip sw-chip-blue' },
  { id: 'pending', title: 'Pending', class: 'sw-chip sw-chip-blue' },
  { id: 'scheduled', title: 'Scheduled', class: 'sw-chip sw-chip-blue' },

  { id: 'running', title: 'Running', class: 'sw-chip sw-chip-green' },

  { id: 'disabled', title: 'Disabled', class: 'sw-chip sw-chip-red-dark' },

  { id: 'expired', title: 'Expired', class: 'sw-chip' },
  { id: 'completed', title: 'Completed', class: 'sw-chip' },
];

export type FeaturePayoutPrizeType = 'fixed' | 'multiplier' | 'text';

export interface FeatureCurrency {
  code: string;
  rate: number;
  currentRate?: number;
}

export interface ApiCurrency {
  rate: number;
  currency: string;
}

export interface FeaturePayout {
  players: number;
  payoutsPerRange: number[] | string[];
  prizeType: FeaturePayoutPrizeType;
}

export interface FeatureQualify {
  minEnabled: boolean;
  totalEnabled?: boolean;
  multiplierEnabled: boolean;
  bets: {
    [currency: string]: FeatureQualifyingBet
  };
}

export interface FeatureQualifyingBet {
  order?: number;
  min?: number;
  total?: number;
  prizeMultiplier?: number;
}

export interface FeatureGeneral {
  name?: string;
  description: string;
  jurisdiction?: string;
  showFeature: boolean;
  optIn?: boolean;
}

export interface FeatureScheduleInterval {
  validFrom: string;
  validTo: string;
  daysOfWeek: number[];
}

export interface FeatureSchedule {
  duration: number;
  timeZone: string;
  repetitionMode: number;
  specificDates?: string[];
  interval?: FeatureScheduleInterval;
  triggerAt: number[];
  nextCheck?: string;
}

export interface FeatureAsset {
  type: string;
  value: string;
  lang: string;
  featureId: string;
}

export interface OperatorsOwner {
  id: string;
  title: string;
}

export interface OperatorsBrand {
  id: string;
  path?: string;
  title?: string;
  funding?: number;
  disabled?: boolean;
}

export interface FeatureOperators {
  owner: OperatorsOwner | null;
  brands: OperatorsBrand[];
}

export interface SegmentationBrand {
  id: string;
  decryptedBrand?: string;
  players: string[];
}

export interface FeatureSegmentation {
  currencies: string[];
  brands: SegmentationBrand[];
  restrictedCountries: string[];
}

export interface Feature {
  id?: string;
  type: 'tournament' | 'jackpot' | 'promotion' | 'prizeDrop';
  status?: string;
  active?: boolean;
  editable?: boolean;
  isSegmented?: boolean;

  modifiedBy?: string;
  activatedAt?: string;
  created?: string;
  updated?: string;
  lastExecution?: string;

  general: FeatureGeneral;
  schedule: FeatureSchedule;
  configuration: JackpotConfiguration | TournamentConfiguration | PrizeDropConfiguration;
  operators: FeatureOperators;
  segmentation: FeatureSegmentation | TournamentSegmentation;
  games: string[];
  assets?: FeatureAsset[];
}
