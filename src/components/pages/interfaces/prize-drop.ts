import * as moment from 'moment';
import 'moment-timezone';
import { RepetitionMode } from '../tournament/form/schedule/tournament-schedule-form.helpers';

import { Feature, FeaturePayout, FeatureSchedule, FeatureScheduleInterval, SegmentationBrand } from './feature';


export interface PrizeDropPayoutChance {
  duration: number;
  chance: number;
}

export interface PrizeDropPayout extends FeaturePayout {
  dropChances: PrizeDropPayoutChance[];
}

export interface PrizeDropRange {
  min: number;
  max: number;
  minQualifyAmount: number;
}

export interface PrizeDropConfiguration {
  payouts: PrizeDropPayout[];
  nonDroppedPrizes: 'reschedule' | 'remove';
  winNotificationAmount: number | null;
}

export interface PrizeDropSegmentationCurrency {
  code: string;
  rate: number;
  currentRate?: number;
}

export interface PrizeDropQualify {
  minEnabled: boolean;
  multiplierEnabled: boolean;
  bets: {
    [currency: string]: PrizeDropsQualifyingBet
  };
}

export interface PrizeDropsQualifyingBet {
  order?: number;
  min?: number;
  prizeMultiplier?: number;
}

export interface PrizeDropSegmentation {
  currencies: PrizeDropSegmentationCurrency[];
  brands: SegmentationBrand[];
}

export interface PrizeDropRanking {
  baseCurrency: string;
  qualifyingBets: PrizeDropQualify;
}

export interface PrizeDropUi {
  rules: {
    [lang: string]: {
      html: string;
    }
  };
}

export interface PrizeDrop extends Feature {
  type: 'prizeDrop';
  ranking: PrizeDropRanking;
  configuration: PrizeDropConfiguration;
  segmentation: PrizeDropSegmentation;
  assets: any[];
  activated?: string;
  nextExecution?: string;
  ui: PrizeDropUi;
}

const MIN_TIME = { 'hour': 0, 'minute': 0, 'second': 0, 'millisecond': 0 };
const MAX_TIME = { 'hour': 23, 'minute': 59, 'second': 59, 'millisecond': 999 };

export function getScheduleRuns( schedule: Partial<FeatureSchedule>, count: number ): string[] {
  const dates: string[] = [];
  let time = moment.utc();
  if (schedule.timeZone) {
    const offset = time.tz(schedule.timeZone).utcOffset();
    time.add(offset, 'minutes');
  }
  for (let i = 0; i < count; i++) {
    const next = calcSchedule(time, schedule);
    if (!next) {
      break;
    }
    dates.push(next.toISOString());
    time = next;
  }
  return dates;
}

export function calcSchedule( currentDate: moment.Moment, schedule?: Partial<FeatureSchedule> ): moment.Moment | null {
  if (!schedule) {
    return null;
  }
  const { triggerAt, interval, specificDates, repetitionMode } = schedule;

  if (triggerAt) {
    const timeTrigger = triggerAt.sort(( a, b ) => a - b);
    if (repetitionMode === RepetitionMode.Interval && (interval && interval.validFrom && interval.validTo)) {
      return calcDaily(currentDate, timeTrigger, interval);
    }
    if (repetitionMode === RepetitionMode.Dates && Array.isArray(specificDates)) {
      return calcDates(currentDate, timeTrigger, specificDates);
    }
  }
  return null;
}

function calcDaily( currentDate: moment.Moment, triggers: number[], interval: FeatureScheduleInterval ): moment.Moment | null {
  const { daysOfWeek, validFrom, validTo } = interval;
  const to = validTo ? moment.utc(validTo).clone().set(MAX_TIME) : null;
  if (to && to < currentDate) {
    return null;
  }
  const from = validFrom ? moment.utc(validFrom).clone().set(MIN_TIME) : null;
  const fromDate = from && from > currentDate ? from.clone() : currentDate.clone();
  const startDate = moment.utc(fromDate).clone().set(MIN_TIME);

  for (let day = 0; day < 8; day++) {
    if (daysOfWeek.includes(startDate.isoWeekday())) {
      for (const trigger of triggers) {
        const next = startDate.clone().add(trigger === 0 ? 1 : trigger, 'millisecond');

        if (next > fromDate) {
          if (!to) {
            return next;
          }
          if (next < to) {
            return next;
          }
        }
      }
    }
    startDate.add(1, 'day');
  }
  return null;
}

function calcDates( currentDate: moment.Moment, triggers: number[], dates: string[] ): moment.Moment | null {
  const runs: moment.Moment[] = [];
  for (const date of dates) {
    for (const trigger of triggers) {
      runs.push(moment.utc(date).set(MIN_TIME).add(trigger, 'millisecond'));
    }
  }
  return runs.find(date => date > currentDate) || null;
}
