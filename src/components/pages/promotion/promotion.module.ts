import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { PromotionService } from '../../../common/services/promotion.service';
import { PromotionListModule } from './list/promotion-list.module';
import { PromotionRoutingModule } from './promotion-routing.module';
import { PromotionComponent } from './promotion.component';
import { PromotionUpdateModule } from './update/promotion-update.module';

export const MODULES = [
  PromotionRoutingModule,
  PromotionListModule,
  PromotionUpdateModule,
];

@NgModule({
  imports: [
    CommonModule,
    ...MODULES,
  ],
  providers: [
    PromotionService
  ],
  declarations: [
    PromotionComponent,
  ],
})
export class PromotionModule {
}
