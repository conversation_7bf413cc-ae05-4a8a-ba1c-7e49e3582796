import { NgModule } from '@angular/core';
import { Route, RouterModule, Routes } from '@angular/router';
import { PERMISSIONS_LIST } from '@skywind-group/lib-swui';
import { ClonePromotionResolver } from '../../../common/services/resolvers/clone-promotion.resolver';
import { MerchantBriefResolver } from '../../../common/services/resolvers/merchant-brief.resolver';
import { PromotionLabelsResolver } from '../../../common/services/resolvers/promotion-labels.resolver';
import { PromotionResolver } from '../../../common/services/resolvers/promotion.resolver';
import { PromotionListComponent } from './list/promotion-list.component';

import { PromotionComponent } from './promotion.component';
import { PromotionUpdateComponent } from './update/promotion-update.component';

const update: Route = {
  component: PromotionUpdateComponent,
  resolve: {
    brief: MerchantBriefResolver,
    promotion: PromotionResolver,
    labels: PromotionLabelsResolver,
  },
  data: {
    permissions: PERMISSIONS_LIST.PROMOTION_EDIT,
    title: 'Promotions - Edit'
  }
};

const clone: Route = {
  component: PromotionUpdateComponent,
  resolve: {
    brief: MerchantBriefResolver,
    promotion: ClonePromotionResolver,
    labels: PromotionLabelsResolver,
  },
  data: {
    duplicate: true,
    permissions: PERMISSIONS_LIST.PROMOTION_EDIT,
    title: 'Promotions - Clone'
  },
};

export const routes: Routes = [
  {
    path: '',
    component: PromotionComponent,
    children: [
      {
        path: '',
        component: PromotionListComponent,
        resolve: {
          brief: MerchantBriefResolver,
        },
        data: {
          permissions: PERMISSIONS_LIST.PROMOTION_VIEW,
          title: 'Promotions'
        },
      },
      {
        path: 'create',
        component: PromotionUpdateComponent,
        resolve: {
          brief: MerchantBriefResolver,
          labels: PromotionLabelsResolver,
        },
        data: {
          permissions: PERMISSIONS_LIST.PROMOTION_CREATE,
          title: 'Promotions - Create'
        },
      },
      {
        path: 'edit/:id/path/:path',
        ...update
      },
      {
        path: 'edit/:id',
        ...update
      },
      {
        path: 'clone/:id/path/:path',
        ...clone
      },
      {
        path: 'clone/:id',
        ...clone
      },
    ]
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: [
    MerchantBriefResolver,
    PromotionResolver,
    ClonePromotionResolver,
    PromotionLabelsResolver,
  ]
})
export class PromotionRoutingModule {
}
