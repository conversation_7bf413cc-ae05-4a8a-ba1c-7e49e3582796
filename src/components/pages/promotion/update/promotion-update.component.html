<lib-swui-page-panel [title]="title$ | async" [back]="true" backUrl="/pages/promotion"></lib-swui-page-panel>

<div class="promotion-edit">
  <mat-tab-group #tabs class="promotion-edit__main" [formGroup]="form" animationDuration="0ms">

    <mat-tab>
      <ng-template mat-tab-label>{{ 'PROMOTION.TABS.general' | translate }}</ng-template>
      <sw-promotion-general-form formControlName="general"></sw-promotion-general-form>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>{{ 'PROMOTION.TABS.schedule' | translate }}</ng-template>
      <sw-promotion-schedule-form
        formControlName="schedule"
        [isEdit]="promotion && !isDuplicate"
        [state]="promotion?.state">
      </sw-promotion-schedule-form>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>{{ 'PROMOTION.TABS.rewards' | translate }}</ng-template>
      <sw-promotion-rewards-form formControlName="rewards"
                                 [entity]="entity"
                                 [brandPath]="brandPath">
      </sw-promotion-rewards-form>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>{{ 'PROMOTION.TABS.games' | translate }}</ng-template>
      <sw-promotion-games-form formControlName="games"
                               [defaultCurrency]="defaultCurrency"
                               [operatorAllowed]="operatorAllowed"
                               [isDuplicate]="isDuplicate"
                               [brandPath]="brandPath"
                               (switchToOperatorsTab)="onSwitchToOperatorsTab()">
      </sw-promotion-games-form>
    </mat-tab>

    <mat-tab *ngIf="operatorAllowed">
      <ng-template mat-tab-label>{{ 'PROMOTION.TABS.operators' | translate }}</ng-template>
      <sw-promotion-brand-form formControlName="brand"
                               [rewardType]="rewardType">
      </sw-promotion-brand-form>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>{{ 'PROMOTION.TABS.segmentation' | translate }}</ng-template>
      <sw-promotion-segmentation-from
        [schedule]="schedule$ | async"
        [formControl]="segmentation"
        [isDuplicate]="isDuplicate"
        [promotion]="promotion"
        [rewardType]="rewardType"
        [shouldPlayersBeUpdated]="shouldPlayersBeUpdated">
      </sw-promotion-segmentation-from>
    </mat-tab>

    <mat-tab label="Info" *ngIf="isSidebarHidden">
      <ng-template mat-tab-label>
        <span>Info</span>
      </ng-template>
      <ng-template matTabContent>
        <div class="mat-card">
          <sw-promotion-info [promotion]="promotion"
                             [isDuplicate]="isDuplicate"
                             (labels)="onLabelsChanged($event)"
                             (status)="onStatusChanged($event)">
          </sw-promotion-info>
        </div>
      </ng-template>
    </mat-tab>

  </mat-tab-group>

  <div class="promotion-edit__sidebar" *ngIf="!isSidebarHidden">
    <sw-promotion-info [promotion]="promotion"
                       [isDuplicate]="isDuplicate"
                       (labels)="onLabelsChanged($event)"
                       (status)="onStatusChanged($event)">
    </sw-promotion-info>
    <div class="padding-top32" [ngClass]="{'sidebar-hidden': isSidebarHidden}">
      <div class="controls">
        <button mat-flat-button class="controls__button link" routerLink="/pages/promotion">
          {{ 'COMMON.ACTIONS.cancel' | translate }}
        </button>
        <button mat-flat-button
                class="controls__button"
                [color]="'primary'"
                [disabled]="form.disabled"
                (click)="onSubmit()">
          {{ 'COMMON.ACTIONS.save' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>

<div *ngIf="isSidebarHidden" class="footer" [ngClass]="{'sidebar-hidden': isSidebarHidden}">
  <div class="controls">
    <button mat-flat-button class="controls__button link" routerLink="/pages/promotion">
      {{ 'COMMON.ACTIONS.cancel' | translate }}
    </button>
    <button mat-flat-button
            class="controls__button"
            [color]="'primary'"
            [disabled]="form.disabled"
            (click)="onSubmit()">
      {{ 'COMMON.ACTIONS.save' | translate }}
    </button>
  </div>
</div>
