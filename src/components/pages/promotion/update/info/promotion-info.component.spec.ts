import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { ActivatedRoute } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SettingsService, SwHubConfigService } from '@skywind-group/lib-swui';
import { LabelsService, MockLabelsService } from '../../../../../common/services/labels.service';
import { PromotionInfoComponent } from './promotion-info.component';
import { MODULES } from './promotion-info.module';
import { MatIconRegistry } from '@angular/material/icon';
import { FakeMatIconRegistry } from '@angular/material/icon/testing';


describe('PromotionInfoComponent', () => {
  let component: PromotionInfoComponent;
  let fixture: ComponentFixture<PromotionInfoComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        PromotionInfoComponent,
      ],
      imports: [
        NoopAnimationsModule,
        TranslateModule.forRoot(),
        ...MODULES,
      ],
      providers: [
        SettingsService,
        { provide: LabelsService, useClass: MockLabelsService },
        { provide: SwHubConfigService, useValue: {} },
        { provide: ActivatedRoute, useFactory: () => ({ snapshot: { data: {} } }) },
        { provide: MatIconRegistry, useClass: FakeMatIconRegistry }
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PromotionInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
