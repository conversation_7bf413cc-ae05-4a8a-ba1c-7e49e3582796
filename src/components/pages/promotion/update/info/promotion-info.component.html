<div class="sd">
  <div class="sd__item">
    <div class="sd__title">Promotion info</div>
    <ul class="sd__list sd-list">
      <li class="sd-list__item">
        <div class="sd-list__title">Active</div>
        <div class="sd-list__content">
          <mat-slide-toggle
            #toggle
            [disabled]="isDuplicate || stateDisabled"
            [checked]="toggleChecked"
            (change)="toggleStatus($event)">
          </mat-slide-toggle>
        </div>
      </li>

      <li class="sd-list__item">
        <div class="sd-list__title">State</div>
        <div class="sd-list__content">
          <div class="sw-chip" [ngClass]="state?.class">
            {{ state?.title | translate }}
          </div>
        </div>
      </li>
    </ul>
  </div>

  <div class="sd__item">
    <div class="sd__title">Info</div>
    <ul class="sd__list sd-list">
      <li class="sd-list__item">
        <div class="sd-list__title">Promotion ID</div>
        <div class="sd-list__content">
          {{ promotion?.id || '-'}}
        </div>
      </li>

      <li class="sd-list__item">
        <div fxLayout="row" fxLayoutAlign="start center" style="width: 100%;">
          <div class="sd-list__title">Labels</div>
          <div class="sd-list__content">
            <sw-labels [promotionLabels]="promotion?.labels"
                       [areLabelsDisabled]="areLabelsDisabled"
                       [availableLabels]="availableLabels"
                       [labelsSelectOptions]="labelsSelectOptions"
                       [groupId]="groupId"
                       [disabled]="promotion?.state === 'expired'"
                       (labelsChanged)="changedLabels($event)">
            </sw-labels>
          </div>
        </div>
      </li>
    </ul>
  </div>

<!--  <div class="sd__item">-->
<!--    <div class="sd__title">Reports</div>-->
<!--    <ul class="sd__list sd-list">-->
<!--      <ng-container *ngIf="promotion?.id; else tplEmptyReports">-->
<!--        <li class="sd-list__item">-->
<!--          <a [href]=promoReportUrl class="sd-icon-link">-->
<!--            <mat-icon svgIcon="icon_report" class="sd-icon-link__icon"></mat-icon>-->
<!--            <div class="sd-icon-link__label">-->
<!--              Promotion reports-->
<!--            </div>-->
<!--          </a>-->
<!--        </li>-->
<!--      </ng-container>-->
<!---->
<!--      <ng-template #tplEmptyReports>-->
<!--        <div class="sd-empty">-->
<!--          <mat-icon svgIcon="icon_report" class="sd-empty__icon"></mat-icon>-->
<!--          <div class="sd-empty__label">No reports to show yet</div>-->
<!--        </div>-->
<!--      </ng-template>-->
<!---->
<!--    </ul>-->
<!--  </div>-->

  <div class="sd__item">
    <div class="sd__title">Info</div>
    <ul class="sd__list sd-list">
      <li class="sd-list__item">
        <div class="sd-list__title">Created</div>
        <div class="sd-list__content">{{ created }}</div>
      </li>


      <li class="sd-list__item">
        <div class="sd-list__title">Activated</div>
        <div class="sd-list__content">
          {{ activatedAt }}
        </div>
      </li>

      <li class="sd-list__item">
        <div class="sd-list__title">Modified by</div>
        <div class="sd-list__content">
          {{ promotion?.modifiedBy || '-' }}
        </div>
      </li>

      <li class="sd-list__item">
        <div class="sd-list__title">Modified</div>
        <div class="sd-list__content">{{ updated }}</div>
      </li>
    </ul>
  </div>
</div>
