import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { SwuiChipsAutocompleteModule, SwuiNotificationsModule } from '@skywind-group/lib-swui';
import { LabelsService } from '../../../../../../common/services/labels.service';

import { LabelsComponent } from './labels.component';

@NgModule({
  imports: [
    CommonModule,
    MatFormFieldModule,
    SwuiNotificationsModule,
    ReactiveFormsModule,
    SwuiChipsAutocompleteModule,
  ],
  declarations: [
    LabelsComponent
  ],
  exports: [
    LabelsComponent
  ],
  providers: [
    LabelsService,
  ]
})
export class LabelsModule {
}
