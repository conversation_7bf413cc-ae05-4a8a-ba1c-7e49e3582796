import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Label } from '../../../../../../common/models/label';
import { LabelsService } from '../../../../../../common/services/labels.service';

@Component({
  selector: 'sw-labels',
  templateUrl: './labels.component.html',
  styleUrls: ['./labels.component.scss'],
})
export class LabelsComponent implements OnInit {
  @Output() labelsChanged = new EventEmitter<Label[]>();

  @Input() availableLabels: Label[] = [];
  @Input() labelsSelectOptions: SwuiSelectOption[] = [];
  @Input() groupId = '';
  @Input() disabled = false;

  @Input()
  set promotionLabels( val: Label[] | undefined ) {
    if (!val) {
      return;
    }

    const selectedLabels = val.reduce(( acc: string[], curr: Label ) => {
      acc.push(curr.id);
      return acc;
    }, []);

    this.labels.patchValue(selectedLabels, { emitEvent: false });
  }

  @Input() areLabelsDisabled?: boolean;

  labels: FormControl = new FormControl('');

  constructor( private labelsService: LabelsService ) {
  }

  ngOnInit() {
    if (this.areLabelsDisabled) {
      this.labels.disable();
    }

    this.labels.valueChanges.subscribe(data => {

      const processLabels = data.map(( item: { id: string } ) => {
        return this.availableLabels.find(label => label.id === item.id);
      });

      this.labelsChanged.emit(processLabels);
    });
  }


  mapFn( id: string ) {
    return { id };
  }

  addLabel = ( label: string ): Observable<any> => {
    return this.labelsService.addLabel({ title: label, groupId: this.groupId })
      .pipe(
        map(( item: any ) => {
          let selectOption = this.toOptionModel(item);
          this.labelsSelectOptions = [...this.labelsSelectOptions, selectOption];
          this.availableLabels.push({ id: item.id, title: item.title });

          return selectOption;
        }));
  };

  toOptionModel( item: any ): SwuiSelectOption {
    return {
      id: item.id,
      text: item.title
    };
  }
}
