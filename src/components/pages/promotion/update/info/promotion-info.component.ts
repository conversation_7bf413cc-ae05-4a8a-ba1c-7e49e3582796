import { Component, EventEmitter, Input, OnChanges, OnInit, Output, ViewChild } from '@angular/core';
import { MatSlideToggle, MatSlideToggleChange } from '@angular/material/slide-toggle';
import { ActivatedRoute } from '@angular/router';
import { SettingsService, SwHubConfigService, SwuiSelectOption } from '@skywind-group/lib-swui';
import 'moment-timezone';
import { take } from 'rxjs/operators';
import { REPORT_ID } from '../../../../../app.constants';
import { BaseInfoComponent } from '../../../../../common/components/base-info.component';
import { Label } from '../../../../../common/models/label';

import { PromotionData, PromotionStatus } from '../../../../../common/models/promotion';
import { LabelsService } from '../../../../../common/services/labels.service';
import { PROMOTION_STATE } from '../../list/schema';

@Component({
  selector: 'sw-promotion-info',
  templateUrl: './promotion-info.component.html',
})
export class PromotionInfoComponent extends BaseInfoComponent implements OnInit, OnChanges {

  @ViewChild('toggle') toggle: MatSlideToggle | undefined;

  @Input() promotion: PromotionData | undefined;
  @Input() isDuplicate = false;

  @Output() status = new EventEmitter<PromotionStatus>();
  @Output() labels = new EventEmitter<Label[]>();
  @Output() labelsChanged = new EventEmitter<Label[]>();

  areLabelsDisabled?: boolean;

  analyticsUrl = '';
  promoReportUrl = '';
  toggleChecked = false;

  availableLabels: Label[] = [];
  labelsSelectOptions: SwuiSelectOption[] = [];
  groupId = '';

  constructor( { hubs }: SwHubConfigService,
               settingsService: SettingsService,
               private route: ActivatedRoute,
               private labelsService: LabelsService,
  ) {
    super(settingsService);
    if (hubs && hubs.analytics) {
      const { url } = hubs.analytics;
      this.analyticsUrl = url;
    }

    this.setLabelsData();
  }

  ngOnInit(): void {
    this.areLabelsDisabled = !this.isDuplicate && this.promotion?.state &&
      ['in_progress', 'alt_in_progress', 'inProgress', 'finished', 'alt_finished'].includes(this.promotion.state);

    this.initDates({
      created: this.promotion ? this.promotion.createdAt : undefined,
      updated: this.promotion ? this.promotion.updatedAt : undefined
    });
    this.promoReportUrl = `${this.analyticsUrl}/${REPORT_ID.promotion}`;
  }

  ngOnChanges() {
    this.toggleChecked = !this.isDuplicate && this.promotion?.status === 'active';

    if (this.toggle) {
      this.toggle.checked = this.toggleChecked;
    }
  }

  setLabelsData() {
    this.availableLabels = this.route.snapshot.data.labels;
    if (Array.isArray(this.availableLabels) && this.availableLabels.length) {
      this.labelsSelectOptions = this.availableLabels.map(( label: Label ) => ({ id: label.id, text: label.title }));
    }

    this.labelsService.getLabelGroups('promotion')
      .pipe(
        take(1),
      )
      .subscribe(
        data => {
          if (Array.isArray(data) && data.length) {
            let promotionGroup = data.find(item => item.group === 'promotion');

            if (promotionGroup) {
              this.groupId = promotionGroup.id;
            }
          }
        }
      );
  }

  toggleStatus( { checked }: MatSlideToggleChange ) {
    this.status.emit(checked ? 'active' : 'inactive');
  }

  changedLabels( labels: Label[] ) {
    this.labels.emit(labels);
  }

  get stateDisabled(): boolean {
    return this.promotion && this.promotion.state ?
      ['finished', 'alt_finished', 'expired'].includes(this.promotion.state) :
      true;
  }

  get state(): { title: string; class: string } | undefined {
    return PROMOTION_STATE[this.promotion && this.promotion.state || 'new'];
  }
}
