import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { ReportTdWidgetModule } from '../../../../../common/grid/widgets/td/report/report.module';
import { LabelsModule } from './labels/labels.module';

import { PromotionInfoComponent } from './promotion-info.component';

export const MODULES = [
  FlexLayoutModule,
  MatSlideToggleModule,
  MatIconModule,
  LabelsModule,
  ReportTdWidgetModule
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...MODULES,
  ],
  declarations: [PromotionInfoComponent],
  exports: [PromotionInfoComponent],
})
export class PromotionInfoModule {
}
