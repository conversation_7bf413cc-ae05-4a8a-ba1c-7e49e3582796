import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { ValidationErrors } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { map, share, switchMap, takeUntil } from 'rxjs/operators';
import {
  AbstractControlValueAccessor, controlValueProviders
} from '../../../../../../common/lib/abstract-control-value-accessor';
import { GameInfo } from '../../../../../../common/models/game';
import { FreebetGameConfig, PromotionData } from '../../../../../../common/models/promotion';
import { GameService } from '../../../../../../common/services/game.service';
import { PromotionFormService } from '../../promotion-form-service/promotion-form.service';
import { TaggedItem } from './freebet-tagged-items/freebet-tagged-items.component';

function from( game: GameInfo ): TaggedItem | undefined {
  if (!game.labels.find(( { id } ) => id === game.providerCode)) {
    game.labels.push({
      id: game.providerCode,
      title: game.providerTitle,
      group: 'provider',
    });
  }
  if ('code' in game && 'providerCode' in game) {
    return {
      id: game.code,
      title: game.title || '',
      labels: game.labels,
      limits: game.limits,
      totalBetMultiplier: game.totalBetMultiplier,
      checked: false
    };
  }
}

function toGameCodes( promotion: PromotionData | undefined ): string[] {
  if (promotion) {
    if (promotion.type === 'freebet') {
      if (promotion.rewards && promotion.rewards.length) {
        return promotion.rewards[0].games.map(( { gameCode } ) => gameCode);
      }
    }
  }
  return [];
}

@Component({
  selector: 'sw-promotion-games-form',
  templateUrl: './promotion-games-form.component.html',
  styleUrls: ['./promotion-games-form.component.scss'],
  providers: controlValueProviders(PromotionGamesFormComponent)
})
export class PromotionGamesFormComponent extends AbstractControlValueAccessor implements OnDestroy {
  @Input() defaultCurrency?: string;
  @Input() operatorAllowed?: boolean;
  @Input() isDuplicate = false;

  @Input()
  set brandPath( value: string | undefined ) {
    if (typeof value === 'undefined') {
      return;
    }

    console.log('brand');

    this._brandPath = value;
    this.brandPathChanged$.next(value);
  }

  get brandPath(): string | undefined {
    return this._brandPath;
  }

  @Output() switchToOperatorsTab = new EventEmitter<boolean>();

  promotion: PromotionData;

  gameCodes: string[] = [];
  checkedOnly = false;

  selectedRewardType = 'bonus_coin';
  isSelectOperatorsDisplay = false;

  items$?: Observable<TaggedItem[]>;
  totalItems$?: Observable<number>;

  readonly toggleTexts = {
    true: 'COMPONENTS.GAMES_SELECT_MANAGER.VIEW_ALL',
    false: 'COMPONENTS.GAMES_SELECT_MANAGER.VIEW_SELECTED'
  };

  private _brandPath: string | undefined;

  private readonly brandPathChanged$: BehaviorSubject<string>;
  private readonly destroyed$ = new Subject<void>();

  constructor( { snapshot: { data: { promotion } } }: ActivatedRoute,
               private readonly service: GameService,
               private readonly formService: PromotionFormService,
  ) {
    super();
    this.promotion = promotion;
    this.brandPathChanged$ = new BehaviorSubject<string>(this.promotion?.brandPath ?? ':');

    this.items$ = this.brandPathChanged$
      .pipe(
        switchMap(brandPath => this.service.query(brandPath)),
        map(games => {
          const selectItems: TaggedItem[] = [];
          const availableGames = games.filter(game => {
            return game.type === 'slot' && game.features?.isFreebetSupported;
          });
          availableGames.forEach(game => {
            const item = from(game);
            if (item) {
              selectItems.push(item);
            }
          });
          return selectItems;
        }),
        share(),
        takeUntil(this.destroyed$),
      );

    this.totalItems$ = this.items$.pipe(
      map(( { length } ) => length)
    );

    this.gameCodes = toGameCodes(this.promotion);

    if (this.promotion) {
      this.selectedRewardType = this.promotion.type;
    } else {
      this.formService.rewardType$.subscribe(
        rewardType => {
          if (rewardType) {
            this.selectedRewardType = rewardType;
            this.gameCodes = [];
            this.isValid = true;

            if (this.onChange) {
              this.onChange(this.gameCodes);
            }
          }
        }
      );
    }
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get isErrorState(): boolean {
    if (!this.disabled && this.formService.formSubmitted) {
      return this.validate() !== null;
    }
    return false;
  }

  writeValue(): void {
    // Method not implemented.
  }

  validate(): ValidationErrors | null {
    return this.isValid && this.gameCodes?.length ? null : { invalidForm: { valid: false } };
  }

  onSelectedChange( gameCodes: string[] | FreebetGameConfig[] ) {
    if (this.selectedRewardType === 'bonus_coin') {
      this.gameCodes = gameCodes as string[];
    } else {
      this.gameCodes = (gameCodes as FreebetGameConfig[])?.map(( { gameCode } ) => gameCode);
    }
    if (!this.gameCodes?.length) {
      this.checkedOnly = false;
    }

    setTimeout(() => {
      if (this.onChange) {
        this.onChange(gameCodes);
      }
    }, 0);
  }

  onStatusChanges( { status }: { status: string; errors: ValidationErrors } ) {
    this.isValid = status === 'VALID';
  }

  toggleVisible() {
    this.checkedOnly = !this.checkedOnly;
  }

  setDisabledState( isDisabled: boolean ): void {
    this.disabled = isDisabled;

    if (isDisabled && (!this.promotion || this.isDuplicate)) {
      this.isSelectOperatorsDisplay = true;
      this.gameCodes = [];

      if (this.onChange) {
        this.onChange(this.gameCodes);
      }
    } else {
      this.isSelectOperatorsDisplay = false;
    }
  }

  onSwitchToOperatorsTab() {
    this.switchToOperatorsTab.emit(true);
  }
}
