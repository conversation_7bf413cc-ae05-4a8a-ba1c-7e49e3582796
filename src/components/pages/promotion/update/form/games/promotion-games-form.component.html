<div class="mat-card">
  <ng-container *ngIf="!isSelectOperatorsDisplay; else selectOperatorsFirst">
    <div class="error-message margin-bottom16" *ngIf="isErrorState">
      Selected games field should not be empty
    </div>
    <div class="margin-bottom12">
      Choose the games where this promotion will run.
    </div>
    <div class="games-label">
      <h4 class="margin-bottom12 no-margin-top">Selected: {{gameCodes?.length}} of {{(totalItems$ | async)}}</h4>
      <span *ngIf="gameCodes?.length" class="checked-toggle"
            (click)="toggleVisible()">{{ toggleTexts[checkedOnly] | translate }}</span>
    </div>
  </ng-container>

  <ng-template #selectOperatorsFirst>
    <div class="games-label">
      <h4 class="margin-bottom12 no-margin-top">Please select</h4>
      <span class="checked-toggle checked-toggle__operators" (click)="onSwitchToOperatorsTab()">operators</span>
      <h4 class="margin-bottom12 no-margin-top">first.</h4>
    </div>
  </ng-template>

  <div [ngSwitch]="selectedRewardType">
    <ng-template [ngSwitchCase]="'bonus_coin'">
      <sw-tagged-items
        [items]="items$ | async"
        [selectedItems]="gameCodes"
        [disabled]="disabled"
        [checkedOnly]="checkedOnly"
        (changed)="onSelectedChange($event)">
      </sw-tagged-items>
    </ng-template>

    <ng-template [ngSwitchCase]="'freebet'">
      <sw-freebet-tagged-items
        [items]="items$ | async"
        [selectedItems]="gameCodes"
        [promotionGames]="promotion?.rewards[0].games"
        [disabled]="disabled"
        [checkedOnly]="checkedOnly"
        [defaultCurrency]="defaultCurrency"
        [operatorAllowed]="operatorAllowed"
        [shouldReset]="isSelectOperatorsDisplay"
        [brandPath]="brandPath"
        (changed)="onSelectedChange($event)"
        (statusChanges)="onStatusChanges($event)">
      </sw-freebet-tagged-items>
    </ng-template>
  </div>
</div>
