import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import {
  AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnDestroy,
  OnInit, Output, SimpleChanges, ViewChild
} from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { BehaviorSubject, forkJoin, isObservable, Observable, of, Subject } from 'rxjs';
import {
  debounceTime, distinctUntilChanged, filter, map, startWith, switchMap, take, takeUntil, tap
} from 'rxjs/operators';
import { GameInfo, LimitsByCurrencyCode } from '../../../../../../../common/models/game';
import { FreebetGameConfig } from '../../../../../../../common/models/promotion';
import { GameService } from '../../../../../../../common/services/game.service';
import { PromotionFormService } from '../../../promotion-form-service/promotion-form.service';

export interface TaggedItem {
  checked: boolean;
  readonly id: string;
  readonly title: string;
  readonly labels: {
    readonly group: string;
    readonly title: string;
  }[];
  readonly limits: LimitsByCurrencyCode;
  readonly totalBetMultiplier?: number;
}

@Component({
  selector: 'sw-freebet-tagged-items',
  templateUrl: 'freebet-tagged-items.component.html',
  styleUrls: ['./freebet-tagged-items.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FreebetTaggedItemsComponent implements OnInit, OnChanges, OnDestroy, AfterViewInit {

  @ViewChild(CdkVirtualScrollViewport, { static: false }) virtualScroll?: CdkVirtualScrollViewport;

  @Input() items: TaggedItem[] | Observable<TaggedItem[]> = [];
  @Input() selectedItems: string[] = [];
  @Input() disabled = false;
  @Input() height = '500px';
  @Input() checkedOnly = false;
  @Input() operatorAllowed?: boolean;
  @Input() brandPath?: string;

  @Input() set shouldReset( value: boolean ) {
    if (value) {
      this.resetForm();
    }
  }

  @Input()
  set defaultCurrency( value: string | undefined ) {
    if (!value) {
      return;
    }

    this._defaultCurrency = value;

    this.shouldInit ? this.initForm() : this.resetForm();

    this.shouldInit = false;
  }

  get defaultCurrency(): string | undefined {
    return this._defaultCurrency;
  }

  @Input()
  set promotionGames( value: FreebetGameConfig[] ) {
    if (Array.isArray(value) && value.length) {
      this.promotionGames$.next(value);
    }
  }

  @Output() changed = new EventEmitter<string[]>();
  @Output() statusChanges = new EventEmitter<{ status: string; errors: ValidationErrors }>();

  shouldInit = true;

  form: FormArray = new FormArray([]);

  searchInput = new FormControl('');
  loading = false;
  availableItems: TaggedItem[] = [];

  coinBets: Map<string, SwuiSelectOption[] | undefined> = new Map<string, SwuiSelectOption[] | undefined>();
  applyToAllGamesVisibility: Map<string, boolean> = new Map<string, boolean>();

  promotionGames$ = new BehaviorSubject<FreebetGameConfig[]>([]);

  private cachedItems: TaggedItem[] = [];
  private _defaultCurrency: string | undefined;

  private readonly inputChanged$ = new Subject();
  private readonly destroyed$ = new Subject();

  constructor( private readonly cd: ChangeDetectorRef,
               private readonly gameService: GameService,
               private readonly formService: PromotionFormService,
               private readonly fb: FormBuilder,
  ) {
    this.inputChanged$.pipe(
      switchMap(() => isObservable(this.items) ? this.items : of(this.items)),
      filter(items => !!items),
      tap(() => {
        this.loading = true;
      }),
      map(items => items.map(item => {
          return {
            ...item,
            checked: this.selectedItems?.indexOf(item.id) !== -1,
          };
        }
      )),
      startWith([]),
      tap(() => {
        this.loading = false;
      }),
      takeUntil(this.destroyed$)
    ).subscribe(items => {
      this.cd.markForCheck();
      this.cachedItems = items;

      this.setSearchTerm(this.searchInput.value);
    });

    this.form.valueChanges.subscribe(
      value => {
        this.changed.emit(value);
      }
    );
    this.form.statusChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(value => {
      this.statusChanges.emit({ status: value, errors: this.form.errors || {} });
    });
  }

  ngOnInit() {
    this.formService.formSubmitted$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      this.cd.markForCheck();
    });

    if (this.searchInput) {
      this.searchInput.valueChanges.pipe(
        debounceTime(100),
        distinctUntilChanged(),
        takeUntil(this.destroyed$)
      ).subscribe(search => {
        this.setSearchTerm(search);
      });
    }
  }

  ngOnChanges( { selectedItems, items }: SimpleChanges ): void {
    if (items || selectedItems) {
      this.inputChanged$.next();
    } else {
      this.setSearchTerm(this.searchInput.value);
    }
  }

  ngAfterViewInit() {
    if (!this.virtualScroll) {
      return;
    }

    const observer = new IntersectionObserver(( entries ) => {
      entries.forEach(( { isIntersecting } ) => {
        if (isIntersecting) {
          this.virtualScroll?.checkViewportSize();
        }
      });
    });

    observer.observe(this.virtualScroll.elementRef.nativeElement);
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  trackByFn = ( _: number, { id }: TaggedItem ) => id;

  emitOnChanged( game: TaggedItem ) {
    if (game?.id) {
      if (game.checked) {
        this.gameService.getAggregatedItem(game.id, this.brandPath, this.defaultCurrency)
          .pipe(
            take(1),
          )
          .subscribe(
            data => {
              if (this.defaultCurrency) {
                this.coinBets.set(game.id, data?.limits[this.defaultCurrency]?.stakeAll?.map(coin => ({
                  id: coin.toString(),
                  text: coin.toString()
                })));
              }

              const control = this.getGameControl(game.id);
              if (!control) {
                this.form.push(
                  this.initFormArrayItem(game.id, this.defaultCurrency)
                );
              }
              this.setSearchTerm(this.searchInput.value);
              this.cd.markForCheck();
            }
          );
      } else {
        this.form.removeAt(this.form.value.findIndex(( data: FreebetGameConfig ) => data.gameCode === game.id));
        this.applyToAllGamesVisibility.set(game.id, false);
        this.cd.markForCheck();
      }
    }
  }

  getCoinControl( gameCode: string ): FormControl | undefined {
    const currency = this.defaultCurrency;
    if (currency) {
      const control = this.getGameControl(gameCode);
      const coins = control?.get('coins') as FormArray;
      const coin = coins?.controls?.find(
        ctrl => Object.keys((ctrl as FormGroup).controls).find(key => key === currency)) as FormGroup;
      return coin?.get(currency)?.get('coin') as FormControl;
    }
  }

  getGameControl( gameCode: string ): FormGroup | null {
    return this.form.controls.find(ctrl => ctrl?.value?.gameCode === gameCode) as FormGroup;
  }

  getLabelClass( group: string ): string {
    switch (group) {
      case 'platform':
        return 'sw-bg-purple';
      case 'class':
        return 'sw-bg-deep-orange';
      case 'feature':
        return 'sw-bg-red';
      default:
        return 'sw-bg-light-blue';
    }
  }

  clearSearch() {
    this.searchInput.setValue('');
  }

  applyCoinToAllGames( coinValue: string ) {
    this.form.value.forEach(
      ( item: FreebetGameConfig ) => {
        if (Array.isArray(this.coinBets.get(item.gameCode)) && this.coinBets.get(item.gameCode)?.length &&
          this.coinBets.get(item.gameCode)?.map(i => i.id).includes(coinValue)) {
          this.getCoinControl(item.gameCode)?.patchValue(coinValue, { emitEvent: false });
        } else {
          this.getCoinControl(item.gameCode)?.reset();
        }
      }
    );

    this.applyToAllGamesVisibility.clear();

    this.form.updateValueAndValidity();
    this.changed.emit(this.form.value);
  }

  private setSearchTerm( search: string ) {
    const needle = search.toLowerCase();
    this.availableItems = this.cachedItems.filter(( { id, title, labels, checked } ) => {
      return (this.checkedOnly ? checked : true)
        && (title.toLowerCase().indexOf(needle) > -1
          || id.toLowerCase().indexOf(needle) > -1
          || labels.map(label => label.title.toLowerCase()).filter(text => text.indexOf(needle) > -1).length > 0);
    });
    this.cd.markForCheck();
  }

  private initForm() {
    this.promotionGames$
      .pipe(
        switchMap(promotionGames => {
          return forkJoin(promotionGames.map(configItem => {
            return this.gameService.getAggregatedItem(configItem.gameCode, this.brandPath, this.defaultCurrency)
              .pipe(
                take(1),
                map(data => ({ data, configItem }))
              );
          }));
        })
      )
      .subscribe(data => {
        data.forEach(item => {
          this.setFormArrayControl(item.configItem, item.data, item.configItem.gameCode, this.defaultCurrency);
        });
      });
  }

  private resetForm() {
    this.selectedItems = [];
    this.form.clear();
    this.availableItems.forEach(game => game.checked = false);
    this.applyToAllGamesVisibility?.clear();
    this.changed.emit([]);
  }

  private setFormArrayControl( configItem: FreebetGameConfig, data: GameInfo, gameCode: string,
                               currency: string | undefined
  ) {
    if (currency) {
      this.coinBets.set(gameCode, data?.limits[currency]?.stakeAll?.map(item => ({
        id: item.toString(),
        text: item.toString()
      })));
    }

    const defaultCurrencyCoin = configItem.coins.find(item => Object.keys(item).find(key => key === currency));
    let coin = '';
    if (defaultCurrencyCoin && currency) {
      coin = defaultCurrencyCoin[currency].coin.toString();
    }

    const control = this.getGameControl(gameCode);
    if (!control) {
      this.form.push(this.initFormArrayItem(gameCode, currency, coin));
    }

    this.cd.markForCheck();
  }

  private initFormArrayItem( gameCode: string, currency: string | undefined, coin = '' ): FormGroup {
    const group = this.fb.group({
      gameCode: gameCode,
      coins: this.fb.array(
        currency ? [
          this.fb.group({
            [currency]: this.fb.group({
              coin: this.fb.control(coin, Validators.required),
            })
          })
        ] : []
      )
    });

    const coins = group?.get('coins') as FormArray;
    const coinControl = coins?.controls?.find(
      ctrl => Object.keys((ctrl as FormGroup).controls).find(key => key === currency)) as FormGroup;

    if (currency) {
      const select = coinControl?.get(currency)?.get('coin') as FormControl;
      select.valueChanges
        .pipe(
          takeUntil(this.destroyed$),
        )
        .subscribe(
          () => this.applyToAllGamesVisibility.set(gameCode, true)
        );
    }

    return group;
  }
}
