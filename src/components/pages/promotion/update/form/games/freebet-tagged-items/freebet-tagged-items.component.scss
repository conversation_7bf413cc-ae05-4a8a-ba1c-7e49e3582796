.table-games {
  overflow: auto;
}

.loading-overlay {
  text-align: center;
}

.table-sticky {
  &__scroll {
    height: 100%;
  }

  &__row {
    min-height: 48px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    display: flex;
    justify-content: space-between;
    align-items: center;

    &.selected {
      background: #FFF7E8;
    }
  }

  &__checkbox {
    padding: 4px 0 4px 24px;
    overflow: hidden;
    width: 250px;
    flex-shrink: 0;

    &_label {
      overflow: hidden;
      text-overflow: ellipsis;
    }

    mat-checkbox {
      display: flex;
      overflow: hidden;
    }
  }

  &__chips {
    min-width: 250px;
    max-width: 250px;
    width: 250px;
    padding: 4px 24px;
    flex: 1;
  }

  &__info {
    display: flex;
    align-items: center;
    flex: 0 0 200px;
    width: 200px;
  }

  &__tb-per-game {
    display: flex;
    align-items: center;
    flex: 0 0 150px;
    width: 150px;
  }

  &__coin-bets {
    min-width: 400px;
    max-width: 400px;
    width: 400px;
    padding: 16px 12px 0 24px;
    flex: 1;
  }

  &__search {
    width: 330px;
    margin-left: unset;
  }

  &__table {
    border-collapse: collapse;
  }
}

.selected {
  background: #FFF7E8;
}

.search-icon {
  color: rgba(0, 0, 0, 0.42);
  margin-right: 4px;
}

.chip {
  color: #fff;
}

.ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
}

.coin-input {
  width: 100%;
  max-width: 200px;
}

.mr-5 {
  margin-right: 5px;
}

.checked-toggle {
  color: #1468cf;
  transition: opacity .5s;
  cursor: pointer;
  margin-left: 10px;

  &:hover {
    opacity: 0.5;
  }
}
