import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiCurrencySymbolModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { FreebetTaggedItemsComponent } from './freebet-tagged-items.component';

@NgModule({
    imports: [
        CommonModule,
        TranslateModule.forChild(),
        ReactiveFormsModule,
        FormsModule,
        MatCheckboxModule,
        MatFormFieldModule,
        MatIconModule,
        MatChipsModule,
        MatInputModule,
        ScrollingModule,
        MatButtonModule,
        SwuiSelectModule,
        SwuiControlMessagesModule,
        SwuiCurrencySymbolModule,
        MatSelectModule,
    ],
  declarations: [FreebetTaggedItemsComponent],
  exports: [FreebetTaggedItemsComponent],
})
export class FreebetTaggedItemsModule {
}
