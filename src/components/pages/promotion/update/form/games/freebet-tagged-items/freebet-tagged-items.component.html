<div class="table-sticky bordered" [ngStyle]="{height:height}">
  <div class="table-sticky__header">
    <div class="table-sticky__info">
      <mat-form-field class="table-sticky__search no-field-padding" appearance="outline">
        <mat-icon matPrefix class="search-icon">search</mat-icon>
        <input
          matInput
          type="text"
          [formControl]="searchInput"
          [placeholder]="'Search by Name or Label'"
          [disabled]="disabled || (!defaultCurrency && operatorAllowed)">
        <button mat-button *ngIf="searchInput.value" matSuffix mat-icon-button aria-label="Clear"
                (click)="clearSearch()">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>
    </div>
  </div>

  <div class="table-sticky__body">
    <cdk-virtual-scroll-viewport
      class="table-sticky__scroll"
      [minBufferPx]="960"
      [maxBufferPx]="1008"
      [itemSize]="48">
      <div class="table-sticky__table">
        <div *ngIf="loading">
          <div class="loading-overlay"><i class="icon-spinner4 spinner"></i></div>
        </div>

        <div *ngIf="(!availableItems || availableItems.length === 0) && !loading">
          {{ 'COMPONENTS.GAMES_SELECT_MANAGER.noGamesToShow' | translate }}
        </div>

        <div class="table-sticky__row"
             *cdkVirtualFor="let game of availableItems; templateCacheSize: 0; trackBy: trackByFn"
             [ngClass]="{'selected': game.checked}">
          <div class="table-sticky__checkbox">
            <mat-checkbox
              class="games-checkbox"
              (change)="emitOnChanged(game)"
              [(ngModel)]="game.checked"
              [disabled]="disabled || (!defaultCurrency && operatorAllowed)">
              <div class="table-sticky__checkbox_label"
                   [title]="game.title">{{ game.title }}</div>
            </mat-checkbox>
          </div>
          <div class="table-sticky__info" [title]="game.id">
            <div class="ellipsis">
              {{ game.id }}
            </div>
          </div>
          <div class="table-sticky__tb-per-game">
            <div *ngIf="getCoinControl(game.id)?.value && game.totalBetMultiplier">
              {{ ('COMPONENTS.GAMES_SELECT_MANAGER.tbPerGame' | translate) + ': ' +
            (getCoinControl(game.id)?.value * game.totalBetMultiplier)?.toFixed(2) }}
            </div>
          </div>
          <div class="table-sticky__coin-bets">
            <mat-form-field *ngIf="getCoinControl(game.id)" class="coin-input coin-prefix" appearance="outline">
              <span matPrefix class="mr-5">{{ defaultCurrency | currencySymbol }}</span>
              <mat-label>{{ 'COMPONENTS.GAMES_SELECT_MANAGER.coinValue' | translate }}</mat-label>
              <mat-select
                [formControl]="getCoinControl(game.id)"
                [disabled]="disabled || (!defaultCurrency && operatorAllowed)"
                required>
                <mat-option *ngFor="let coin of coinBets.get(game.id)"
                            [value]="coin.id">
                  {{ coin.text }}
                </mat-option>
              </mat-select>
              <mat-error>
                <lib-swui-control-messages [control]="getCoinControl(game.id)"></lib-swui-control-messages>
              </mat-error>
            </mat-form-field>
            <span *ngIf="applyToAllGamesVisibility.get(game.id)"
                  (click)="applyCoinToAllGames(getCoinControl(game.id)?.value)"
                  class="checked-toggle">
              {{ 'COMPONENTS.GAMES_SELECT_MANAGER.applyToAllGames' | translate }}
            </span>
          </div>
          <div class="table-sticky__chips">
            <mat-chip-list>
              <mat-chip class="chip" *ngFor="let label of game.labels" [ngClass]="getLabelClass(label.group)">
                {{ label.title | titlecase }}
              </mat-chip>
            </mat-chip-list>
          </div>
        </div>
      </div>
    </cdk-virtual-scroll-viewport>
  </div>
</div>
