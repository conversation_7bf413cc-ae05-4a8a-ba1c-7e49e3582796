import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiChipsAutocompleteModule } from '@skywind-group/lib-swui';
import { TaggedItemsModule } from '../../../../../../common/components/tagged-items/tagged-items.module';
import { GameService } from '../../../../../../common/services/game.service';
import { FreebetTaggedItemsModule } from './freebet-tagged-items/freebet-tagged-items.module';
import { PromotionGamesFormComponent } from './promotion-games-form.component';


export const MODULES = [
  MatFormFieldModule,
  MatInputModule,
  MatSelectModule,
  MatSlideToggleModule,
  SwuiChipsAutocompleteModule,
  FreebetTaggedItemsModule,
  TaggedItemsModule,
  ReactiveFormsModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...MODULES,
  ],
  exports: [
    PromotionGamesFormComponent,
  ],
  declarations: [
    PromotionGamesFormComponent,
  ],
  providers: [
    GameService,
  ],
})
export class PromotionGamesFormModule {
}
