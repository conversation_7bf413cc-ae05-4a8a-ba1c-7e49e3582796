import { Component } from '@angular/core';
import { FormControl } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { ActivatedRoute } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { createHostFactory, SpectatorHost } from '@ngneat/spectator';
import { TranslateModule } from '@ngx-translate/core';
import { GameService, MockGameService } from '../../../../../../common/services/game.service';
import { PromotionFormService } from '../../promotion-form-service/promotion-form.service';
import { PromotionGamesFormComponent } from './promotion-games-form.component';
import { MODULES } from './promotion-games-form.module';

@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}

describe('PromotionGamesFormComponent', () => {
  let spectator: SpectatorHost<PromotionGamesFormComponent, CustomHostComponent>;

  const createHost = createHostFactory({
    component: PromotionGamesFormComponent,
    host: CustomHostComponent,
    imports: [
      NoopAnimationsModule,
      TranslateModule.forRoot(),
      RouterTestingModule,
      ...MODULES,
    ],
    providers: [
      { provide: ActivatedRoute, useFactory: () => ({ snapshot: { data: {} } }) },
      { provide: GameService, useClass: MockGameService },
      PromotionFormService,
    ],
  });

  const value = ['1', '2', '3'];

  beforeEach(() => {
    spectator = createHost(`<sw-promotion-games-form [formControl]="form"></sw-promotion-games-form>`);
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();
  });

  it('check initial state', () => {
    expect(spectator.component.disabled).toBeFalse();
    expect(spectator.component.gameCodes.length).toEqual(0);
  });

  it('form set disabled state', () => {
    spectator.component.setDisabledState(true);
    expect(spectator.component.disabled).toBeTrue();
  });

  it('writeValue has been called', () => {
    const onWriteValueSpy = spyOn(spectator.component, 'writeValue');
    spectator.hostComponent.form.patchValue(value);

    expect(onWriteValueSpy).toHaveBeenCalled();

    expect(spectator.hostComponent.form.value).toEqual(value);
  });

  it('form has not patched with an empty array', () => {
    spectator.hostComponent.form.patchValue([]);
    expect(spectator.component.gameCodes.length).toEqual(0);
  });
});
