import { Component, Input, OnInit } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { AppSettings, SettingsService, SwuiDatePickerConfig } from '@skywind-group/lib-swui';
import { SwuiDateTimepickerConfig } from '@skywind-group/lib-swui/swui-datetimepicker/swui-datetimepicker.interface';
import * as moment from 'moment';
import 'moment-timezone';
import { combineLatest, Observable } from 'rxjs';
import { delay, map, takeUntil } from 'rxjs/operators';
import { getTimeZones } from '../../../../../../app.constants';
import {
  AbstractFormValueAccessor, formValueProviders
} from '../../../../../../common/lib/abstract-form-value-accessor';
import { dateGreaterThanNowWithTimezone } from '../../../../../../common/lib/validators';
import { PromotionData } from '../../../../../../common/models/promotion';
import { TimeZone } from '../../../../../../common/models/time-zone';

export interface PromotionSchedule {
  startDate?: string;
  endDate?: string;
}

@Component({
  selector: 'sw-promotion-schedule-form',
  templateUrl: './promotion-schedule-form.component.html',
  styleUrls: ['./promotion-schedule-form.component.scss'],
  providers: formValueProviders(PromotionScheduleFormComponent)
})
export class PromotionScheduleFormComponent extends AbstractFormValueAccessor<PromotionSchedule> implements OnInit {

  @Input() set state( value: string ) {
    if (value) {
      this.handleStateChange(value);
    }
  }
  @Input() isEdit = false;

  readonly form: FormGroup;
  readonly config$: Observable<SwuiDateTimepickerConfig>;
  nowDate = moment().utc().startOf('day');

  maxStartDate?: moment.Moment;
  minEndDate?: moment.Moment;
  timezones: TimeZone[] = getTimeZones();

  constructor( { snapshot: { data } }: ActivatedRoute, { appSettings$ }: SettingsService ) {
    super();
    const promotion: PromotionData = data.promotion;
    this.form = this.initForm(promotion);

    this.config$ = appSettings$.pipe(
      map<AppSettings, SwuiDatePickerConfig>(( { dateFormat } ) => ({
        dateFormat,
        timePicker: true,
        timeFormat: 'HH:mm',
        timeDisableLevel: { second: false }
      }))
    );

    if (promotion) {
      this.handleStateChange(promotion.state);
    }
  }

  handleStateChange( state: string | undefined ) {
    if ([
      'inProgress',
      'in_progress',
      'alt_in_progress'
    ].includes(state || '')) {
      this.startDate.disable();
      this.timezone.disable();
    }
  }

  ngOnInit(): void {
    this.form.valueChanges.pipe(
      delay(0),
      takeUntil(this.destroyed)
    ).subscribe(value => {
      if (this.onChange) {
        this.onChange(this.transformForm(value));
      }
    });

    this.form.statusChanges.pipe(
      delay(0),
      takeUntil(this.destroyed)
    ).subscribe(() => {
      if (this.onValidatorChange) {
        this.onValidatorChange();
      }
    });

    this.startDate.valueChanges
      .pipe(takeUntil(this.destroyed))
      .subscribe(() => {
        if (this.endDate.dirty && this.startDate.enabled) {
          this.endDate.updateValueAndValidity();
        }
      });

    this.timezone.valueChanges
      .pipe(takeUntil(this.destroyed))
      .subscribe(() => {
        if (this.startDate.dirty && this.startDate.enabled) {
          this.startDate.updateValueAndValidity();
        }

        if (this.endDate.dirty && this.startDate.enabled) {
          this.endDate.updateValueAndValidity();
        }
      });

    combineLatest([
      this.startDate.valueChanges,
      this.endDate.valueChanges
    ])
      .pipe(
        delay(0),
        map(( [start, end] ) => {
          const startDate = moment.parseZone(start);
          let maxStartDate = moment.parseZone(end);
          let minEndDate = this.nowDate.isAfter(start) ? this.nowDate : startDate;
          return [
            maxStartDate,
            minEndDate
          ];
        }),
        takeUntil(this.destroyed)
      )
      .subscribe(( [maxStartDate, minEndDate] ) => {
        this.maxStartDate = maxStartDate;
        this.minEndDate = minEndDate;
      });
  }

  get startDate(): FormControl {
    return this.form.get('startDate') as FormControl;
  }

  get endDate(): FormControl {
    return this.form.get('endDate') as FormControl;
  }

  get timezone(): FormControl {
    return this.form.get('timezone') as FormControl;
  }

  get activateInstantly(): FormControl {
    return this.form.get('activateInstantly') as FormControl;
  }

  private startDateValidator() {
    return ( control: AbstractControl ): { [key: string]: any } | null => {
      const { root } = control;

      if (root.get('activateInstantly')?.value) {
        return null;
      }

      return Validators.required(control) || dateGreaterThanNowWithTimezone('timezone')(control);
    };
  }

  private initForm( promotion: PromotionData ) {
    const startDate = new FormControl(promotion ? promotion.startDate : undefined, this.startDateValidator());
    return new FormGroup({
      startDate,
      endDate: new FormControl(promotion ? promotion.endDate : undefined, [
        Validators.required,
        dateGreaterThanNowWithTimezone('timezone'),
        ( { value, root }: AbstractControl ) => {
          if (new Date(value).getTime() > new Date(startDate.value).getTime() || root.get('activateInstantly')?.value) {
            return null;
          }
          return { 'startGreaterThanEnd': true };
        }
      ]),
      timezone: new FormControl(promotion ? promotion.timezone : null, Validators.required),
      activateInstantly: new FormControl(false)
    });
  }
}
