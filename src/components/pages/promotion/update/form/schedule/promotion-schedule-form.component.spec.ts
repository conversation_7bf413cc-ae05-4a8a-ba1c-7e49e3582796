import { Component } from '@angular/core';
import { AbstractControl, FormControl } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { createHostFactory, SpectatorHost } from '@ngneat/spectator';
import { TranslateModule } from '@ngx-translate/core';
import { SettingsService } from '@skywind-group/lib-swui';
import { PromotionScheduleFormComponent } from './promotion-schedule-form.component';
import { MODULES } from './promotion-schedule-form.module';

@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}

describe('PromotionScheduleFormComponent', () => {
  let spectator: SpectatorHost<PromotionScheduleFormComponent, CustomHostComponent>;

  const createHost = createHostFactory({
    component: PromotionScheduleFormComponent,
    host: CustomHostComponent,
    imports: [
      ...MODULES,
      TranslateModule.forRoot(),
    ],
    providers: [
      SettingsService,
      { provide: ActivatedRoute, useFactory: () => ({ snapshot: { data: {} } }) },
    ]
  });

  const value = {
    startDate: '2025-10-10T00:00:00.000Z',
    endDate: '2025-12-12T00:00:00.000Z',
    timezone: 'Europe/Minsk',
    activateInstantly: false
  };

  const initialFormValue = {
    startDate: '',
    endDate: '',
    timezone: null,
    activateInstantly: false
  };

  beforeEach(() => {
    spectator = createHost(`<sw-promotion-schedule-form [formControl]="form"></sw-promotion-schedule-form>`);
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();
  });

  it('startDate and endDate fields validity', () => {
    const startDate: AbstractControl | null = spectator.component.form.get('startDate');
    const endDate: AbstractControl | null = spectator.component.form.get('endDate');

    expect(startDate?.valid).toBeFalse();
    expect(endDate?.valid).toBeFalse();

    startDate?.setValue('');
    expect(startDate?.hasError('required')).toBeTruthy();

    endDate?.setValue('');
    expect(endDate?.hasError('required')).toBeTruthy();

    startDate?.setValue('2025-10-10T00:00:00.000Z');
    expect(startDate?.valid).toBeTrue();

    endDate?.setValue('2025-12-12T00:00:00.000Z');
    expect(endDate?.valid).toBeTrue();
  });

  it('timezone field validity', () => {
    const timezone: AbstractControl | null = spectator.component.form.get('timezone');

    expect(timezone?.valid).toBeFalse();

    timezone?.setValue('');
    expect(timezone?.hasError('required')).toBeTruthy();

    timezone?.setValue('Europe/Minsk');
    expect(timezone?.valid).toBeTrue();
  });

  it('form field startDate setValue undefined validation should contain an error', () => {
    spectator.component.form.get('startDate')?.setValue(undefined);
    expect(spectator.component.validate()).toEqual({ invalidForm: { valid: false } });
  });

  it('form field endDate setValue undefined validation should contain an error', () => {
    spectator.component.form.get('endDate')?.setValue(undefined);
    expect(spectator.component.validate()).toEqual({ invalidForm: { valid: false } });
  });

  it('form field timezone setValue null validation should contain an error', () => {
    spectator.component.form.get('timezone')?.setValue(undefined);
    expect(spectator.component.validate()).toEqual({ invalidForm: { valid: false } });
  });

  it('startDate > endDate validity', () => {
    const startDate: AbstractControl | null = spectator.component.form.get('startDate');
    const endDate: AbstractControl | null = spectator.component.form.get('endDate');

    startDate?.setValue('2025-12-12T00:00:00.000Z');
    endDate?.setValue('2025-10-10T00:00:00.000Z');

    expect(endDate?.hasError('startGreaterThanEnd')).toBeTruthy();
  });

  it('form set disabled state', () => {
    spectator.component.setDisabledState(true);
    expect(spectator.component.form.disabled).toBeTrue();
  });

  it('writeValue has been called', () => {
    const onWriteValueSpy = spyOn(spectator.component, 'writeValue');
    spectator.hostComponent.form.patchValue(value);

    expect(onWriteValueSpy).toHaveBeenCalled();
  });

  it('form has been patched', () => {
    spectator.hostComponent.form.patchValue(value);
    expect(spectator.component.form.value).toEqual(value);
  });

  it('form has not patched with null', () => {
    spectator.hostComponent.form.patchValue(null);
    expect(spectator.component.form.value).toEqual(initialFormValue);
  });
});
