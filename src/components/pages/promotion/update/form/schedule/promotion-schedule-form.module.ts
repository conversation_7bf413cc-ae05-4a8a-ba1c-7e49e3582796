import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiDatePickerModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { PromotionScheduleFormComponent } from './promotion-schedule-form.component';

export const MODULES = [
  ReactiveFormsModule,
  FlexLayoutModule,
  MatTooltipModule,
  MatFormFieldModule,
  MatIconModule,
  MatButtonModule,
  SwuiDatePickerModule,
  SwuiControlMessagesModule,
  SwuiSelectModule,
  MatCheckboxModule
];

@NgModule({
    imports: [
        CommonModule,
        TranslateModule.forChild(),
        ...MODULES,
    ],
  exports: [
    PromotionScheduleFormComponent,
  ],
  declarations: [
    PromotionScheduleFormComponent
  ],
})
export class PromotionScheduleFormModule {
}
