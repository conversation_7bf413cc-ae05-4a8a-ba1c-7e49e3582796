<form class="vertical-form" [formGroup]="form">
  <div class="mat-card margin-bottom20">
    <h3 class="mat-title">Promotion schedule</h3>
    <div class="margin-bottom16">Set the start and end times for this promotion.</div>
    <div fxLayout="row" class="schedule">
      <mat-checkbox *ngIf="!isEdit" class="schedule__activator" [formControl]="activateInstantly">Activate instantly</mat-checkbox>
      <mat-form-field appearance="outline" class="schedule__start" *ngIf="!activateInstantly.value">
        <mat-label>{{ 'PROMOTION.FORM.SCHEDULE.startTime' | translate }}</mat-label>
        <lib-swui-date-picker
          [required]="!activateInstantly.value"
          [formControl]="startDate"
          [config]="config$ | async"
          [minDate]="nowDate"
          [maxDate]="maxStartDate">
        </lib-swui-date-picker>
        <button mat-icon-button matSuffix>
          <mat-icon>date_range</mat-icon>
        </button>
        <mat-error>
          <lib-swui-control-messages [control]="startDate"></lib-swui-control-messages>
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="schedule__end">
        <mat-label>{{ 'PROMOTION.FORM.SCHEDULE.endTime' | translate }}</mat-label>
        <lib-swui-date-picker
          required
          [formControl]="endDate"
          [config]="config$ | async"
          [minDate]="minEndDate">
        </lib-swui-date-picker>

        <button mat-icon-button matSuffix>
          <mat-icon>date_range</mat-icon>
        </button>
        <mat-error>
          <lib-swui-control-messages [control]="endDate"></lib-swui-control-messages>
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="schedule__timezone">
        <mat-label>{{ 'PROMOTION.FORM.SCHEDULE.timezone' | translate }}</mat-label>
        <lib-swui-select
          required
          [formControl]="timezone"
          [data]="timezones"
          [showSearch]="true"
          [disableEmptyOption]="true">
        </lib-swui-select>
        <mat-error>
          <lib-swui-control-messages [control]="timezone"></lib-swui-control-messages>
        </mat-error>
      </mat-form-field>
    </div>
  </div>
</form>
