<div class="mat-card">
  <div class="margin-bottom12" *swIsControlInvalid="brandControl">
    <mat-error *ngIf="brandControl.hasError('required')">{{ 'VALIDATION.fieldRequired' | translate }}</mat-error>
    <mat-error *ngIf="brandControl.hasError('isPromoInternal')">
      {{ 'VALIDATION.notAllowedToHavePromotion' | translate }}
    </mat-error>
    <mat-spinner *ngIf="brandControl.pending" [diameter]="18" style="float: right; margin-left: 8px"></mat-spinner>
  </div>
  <form class="vertical-form" [formGroup]="form">
    <sw-promotion-brands-control formControlName="brand"></sw-promotion-brands-control>
  </form>
</div>
