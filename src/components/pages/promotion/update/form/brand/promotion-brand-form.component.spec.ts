import { HttpClientTestingModule } from '@angular/common/http/testing';
import { Component } from '@angular/core';
import { AbstractControl, FormControl } from '@angular/forms';
import { MatIconRegistry } from '@angular/material/icon';
import { FakeMatIconRegistry } from '@angular/material/icon/testing';
import { ActivatedRoute } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { createHostFactory, createServiceFactory, SpectatorHost, SpectatorService } from '@ngneat/spectator';
import { TranslateModule } from '@ngx-translate/core';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { BrandsManagerModule } from '../../../../../../common/components/brands-manager/brands-manager.module';
import { EntityService, MockEntityService } from '../../../../../../common/services/entity.service';
import { MockAuthService } from '../../../../../../common/services/mock-auth.service';
import { MockPromotionService, PromotionService } from '../../../../../../common/services/promotion.service';
import { PromotionFormService } from '../../promotion-form-service/promotion-form.service';
import { PromotionBrandFormComponent } from './promotion-brand-form.component';
import { MODULES } from './promotion-brand-form.module';

@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}

describe('PromotionBrandFormComponent', () => {
  let spectator: SpectatorHost<PromotionBrandFormComponent, CustomHostComponent>;
  const createHost = createHostFactory({
    component: PromotionBrandFormComponent,
    host: CustomHostComponent,
    imports: [
      BrandsManagerModule,
      HttpClientTestingModule,
      RouterTestingModule,
      ...MODULES,
      TranslateModule.forRoot(),
    ],
    providers: [
      { provide: ActivatedRoute, useFactory: () => ({ snapshot: { data: {} } }) },
      { provide: SwHubAuthService, useClass: MockAuthService },
      { provide: EntityService, useClass: MockEntityService },
      { provide: MatIconRegistry, useClass: FakeMatIconRegistry },
      { provide: PromotionService, useClass: MockPromotionService },
    ],
  });

  let spectatorService: SpectatorService<PromotionFormService>;
  const createService = createServiceFactory({
    service: PromotionFormService,
  });

  const promotionBrand = {
    brandId: '1',
    brandPath: 'path',
    brandTitle: 'title',
  };

  const initialFormValue = {
    brand: {
      owner: {
        id: '',
        title: ''
      },
      brands: [
        {
          id: '',
          path: '',
          title: ''
        }
      ]
    }
  };

  beforeEach(() => {
    spectator = createHost(`<sw-promotion-brand-form [formControl]="form"></sw-promotion-brand-form>`);
    spectatorService = createService();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();

    expect(spectatorService.service).toBeTruthy();
    expect(spectatorService.service).toBeDefined();
  });

  it('brand control validity', () => {
    const brand: AbstractControl | null = spectator.component.form.get('brand');

    brand?.setValue('');
    expect(brand?.hasError('required')).toBeTruthy();

    brand?.setValue(promotionBrand);
    expect(brand?.value).toEqual(promotionBrand);
  });

  it('form field brand setValue null validation should contain an error', () => {
    spectator.component.form.get('brand')?.setValue(null);
    expect(spectator.component.validate()).toEqual({ invalidForm: { valid: false } });
  });

  it('form set disabled state', () => {
    spectator.component.setDisabledState(true);
    expect(spectator.component.form.disabled).toBeTrue();
  });

  it('writeValue has been called', () => {
    const onWriteValueSpy = spyOn(spectator.component, 'writeValue');
    spectator.hostComponent.form.patchValue(promotionBrand);

    expect(onWriteValueSpy).toHaveBeenCalled();
  });

  it('form has not patched with null', () => {
    spectator.hostComponent.form.patchValue(null);
    expect(spectator.component.form.value).toEqual(initialFormValue);
  });
});
