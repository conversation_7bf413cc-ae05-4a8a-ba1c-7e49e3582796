import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { BrandsManagerModule } from '../../../../../../../common/components/brands-manager/brands-manager.module';
import { PromotionBrandsControlComponent } from './promotion-brands-control.component';


export const MODULES = [
  BrandsManagerModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...MODULES,
  ],
  exports: [
    PromotionBrandsControlComponent,
  ],
  declarations: [
    PromotionBrandsControlComponent,
  ],
})
export class PromotionBrandsControlModule {
}
