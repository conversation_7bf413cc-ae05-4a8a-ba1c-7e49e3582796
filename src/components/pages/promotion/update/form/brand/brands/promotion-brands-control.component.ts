import { Component } from '@angular/core';
import {
  AbstractControlValueAccessor, controlValueProviders
} from '../../../../../../../common/lib/abstract-control-value-accessor';
import { FeatureOperators } from '../../../../../interfaces/feature';

@Component({
  selector: 'sw-promotion-brands-control',
  templateUrl: './promotion-brands-control.component.html',
  providers: controlValueProviders(PromotionBrandsControlComponent)
})
export class PromotionBrandsControlComponent extends AbstractControlValueAccessor {
  values: FeatureOperators | undefined;

  writeValue( val: FeatureOperators | undefined ): void {
    if (typeof val === 'undefined') {
      return;
    }
    this.values = val;
  }

  onSelectData( brands: FeatureOperators ) {
    setTimeout(() => {
      this.onChange(brands);
    }, 0);
  }
}
