import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { ActivatedRoute } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { EntityService, MockEntityService } from '../../../../../../../common/services/entity.service';
import { MockAuthService } from '../../../../../../../common/services/mock-auth.service';
import { PromotionBrandsControlComponent } from './promotion-brands-control.component';
import { MODULES } from './promotion-brands-control.module';
import { MatIconRegistry } from '@angular/material/icon';
import { FakeMatIconRegistry } from '@angular/material/icon/testing';


describe('PromotionBrandsControlComponent', () => {
  let component: PromotionBrandsControlComponent;
  let fixture: ComponentFixture<PromotionBrandsControlComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        PromotionBrandsControlComponent,
      ],
      imports: [
        NoopAnimationsModule,
        TranslateModule.forRoot(),
        RouterTestingModule,
        ...MODULES,
      ],
      providers: [
        { provide: ActivatedRoute, useFactory: () => ({ snapshot: { data: {} } }) },
        { provide: EntityService, useClass: MockEntityService },
        { provide: SwHubAuthService, useClass: MockAuthService },
        { provide: MatIconRegistry, useClass: FakeMatIconRegistry }
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PromotionBrandsControlComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
