import { Component, Input, } from '@angular/core';
import { AbstractControl, FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import {
  AbstractFormValueAccessor, formValueProviders
} from '../../../../../../common/lib/abstract-form-value-accessor';
import { PromotionData } from '../../../../../../common/models/promotion';
import { EntityService } from '../../../../../../common/services/entity.service';
import { FeatureOperators } from '../../../../interfaces/feature';

export interface PromotionBrand {
  brandId?: string;
  brandPath?: string;
  brandTitle?: string;
}

@Component({
  selector: 'sw-promotion-brand-form',
  templateUrl: './promotion-brand-form.component.html',
  providers: formValueProviders(PromotionBrandFormComponent)
})
export class PromotionBrandFormComponent extends AbstractFormValueAccessor<PromotionBrand> {

  @Input()
  set rewardType( value: string | undefined ) {
    this._rewardType = value;

    this.brandControl?.updateValueAndValidity();
  }

  get rewardType(): string | undefined {
    return this._rewardType;
  }

  readonly form: FormGroup;

  private _rewardType?: string;

  constructor( private readonly route: ActivatedRoute,
               private readonly entityService: EntityService,
  ) {
    super();
    this.form = new FormGroup({
      brand: new FormControl(this.transformValue(this.promotionBrand)?.brand, {
        validators: [
          ( { value }: AbstractControl ): { [key: string]: any } | null => {
            if (!value || !Array.isArray(value.brands) || value.brands.length < 1) {
              return { required: true };
            }
            return null;
          }
        ],
        asyncValidators: [
          ( { value }: AbstractControl ): Observable<{ [key: string]: any } | null> => {
            const operators = value as FeatureOperators;
            if (!operators || !Array.isArray(operators.brands) || operators.brands.length < 1) {
              return of(null);
            }
            const path = operators.brands[0].path;
            if (!path) {
              return of(null);
            }
            return this.entityService.getEntity(path).pipe(
              switchMap(entity => entity.isMerchant ? this.entityService.getMerchantEntity(path) : of(entity)),
              map(entity => {
                if (entity.isMerchant && entity.merchant) {
                  return this.rewardType === 'bonus_coin' || entity.merchant.params.isPromoInternal;
                }

                return true;
              }),
              catchError(() => {
                return of(false);
              }),
              map(val => val ? null : { isPromoInternal: true })
            );
          }
        ]
      }),
    });
  }

  get brandControl(): FormControl {
    return this.form?.get('brand') as FormControl;
  }

  private get promotionBrand(): PromotionBrand | undefined {
    if (!this.promotion) {
      return undefined;
    }
    return {
      brandId: this.promotion.brandId,
      brandPath: this.promotion.brandPath,
      brandTitle: this.promotion.title,
    };
  }

  private get promotion(): PromotionData | undefined {
    return this.route.snapshot.data.promotion;
  }

  protected transformValue( value: PromotionBrand | undefined ): { brand: FeatureOperators | undefined } | undefined {
    if (!value) {
      return undefined;
    }

    return {
      brand: {
        owner: {
          id: '',
          title: ''
        },
        brands: [
          {
            id: value.brandId || '',
            path: value.brandPath || '',
            title: value.brandTitle || ''
          }
        ]
      }
    };
  }

  protected transformForm( value: { brand: FeatureOperators | undefined } | undefined ): PromotionBrand | undefined {
    if (!value || !value.brand || !Array.isArray(value.brand.brands) || value.brand.brands.length < 1) {
      return undefined;
    }
    return {
      brandId: value.brand.brands[0].id,
      brandPath: value.brand.brands[0].path,
      brandTitle: value.brand.brands[0].title
    };
  }
}
