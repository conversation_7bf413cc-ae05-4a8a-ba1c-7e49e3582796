import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiIsControlInvalidModule } from '@skywind-group/lib-swui';
import { PromotionBrandsControlModule } from './brands/promotion-brands-control.module';
import { PromotionBrandFormComponent } from './promotion-brand-form.component';

export const MODULES = [
  ReactiveFormsModule,
  PromotionBrandsControlModule,
  MatProgressSpinnerModule,
  MatFormFieldModule,
  SwuiIsControlInvalidModule,
  MatButtonModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...MODULES,
  ],
  declarations: [
    PromotionBrandFormComponent,
  ],
  exports: [
    PromotionBrandFormComponent,
  ],
  providers: [],
})
export class PromotionBrandFormModule {
}
