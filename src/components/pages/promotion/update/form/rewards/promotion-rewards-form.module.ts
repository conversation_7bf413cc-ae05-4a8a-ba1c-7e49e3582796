import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { PromotionRewardsFormComponent } from './promotion-rewards-form.component';
import { PromotionRewardFreebetsModule } from './freebets/promotion-reward-freebets.module';
import { ReactiveFormsModule } from '@angular/forms';
import { FlexModule } from '@angular/flex-layout';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

export const MODULES = [
  ReactiveFormsModule,
  FlexModule,
  MatFormFieldModule,
  MatSelectModule,
  MatIconModule,
  MatTooltipModule,
  PromotionRewardFreebetsModule
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...MODULES,
  ],
  declarations: [
    PromotionRewardsFormComponent,
  ],
  exports: [
    PromotionRewardsFormComponent,
  ],
})
export class PromotionRewardsFormModule {
}
