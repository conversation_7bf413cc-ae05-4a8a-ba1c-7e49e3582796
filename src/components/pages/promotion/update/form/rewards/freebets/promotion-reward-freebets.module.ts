import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { PromotionRewardFreebetsComponent } from './promotion-reward-freebets.component';
import { FlexModule } from '@angular/flex-layout';
import { MatFormFieldModule } from '@angular/material/form-field';
import { SwuiControlMessagesModule, SwuiDatePickerModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatButtonModule } from '@angular/material/button';

export const MODULES = [
  ReactiveFormsModule,
  FlexModule,
  MatFormFieldModule,
  SwuiControlMessagesModule,
  MatIconModule,
  MatTooltipModule,
  MatInputModule,
  MatRadioModule,
  SwuiDatePickerModule,
  MatButtonModule,
  SwuiSelectModule,
];

@NgModule({
    imports: [
        CommonModule,
        TranslateModule.forChild(),
        ...MODULES,
    ],
  declarations: [
    PromotionRewardFreebetsComponent,
  ],
  exports: [
    PromotionRewardFreebetsComponent,
  ],
})
export class PromotionRewardFreebetsModule {
}
