import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { AppSettings, SettingsService, SwuiDatePickerConfig, SwuiSelectOption } from '@skywind-group/lib-swui';
import { SwuiDateTimepickerConfig } from '@skywind-group/lib-swui/swui-datetimepicker/swui-datetimepicker.interface';
import * as moment from 'moment';
import { Observable } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import {
  AbstractFormValueAccessor, formValueProviders
} from '../../../../../../../common/lib/abstract-form-value-accessor';

export interface FreebetRewardData {
  freebetAmount?: number;
  expirationType?: 'in' | 'on';
  expirationPeriodType?: string;
  expirationPeriod?: number;
  expirationDate?: string;
}

interface FormParams {
  freebetAmount: number | null;
  expirationType?: 'in' | 'on';
  expirationPeriodType?: string;
  expirationPeriod?: number | null;
  expirationDate?: string;
}

export const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';

@Component({
  selector: 'sw-promotion-rewards-freebets',
  templateUrl: './promotion-reward-freebets.component.html',
  styleUrls: ['../promotion-rewards-form.component.scss'],
  providers: formValueProviders(PromotionRewardFreebetsComponent)
})
export class PromotionRewardFreebetsComponent extends AbstractFormValueAccessor<any> implements OnInit {
  @Input() brandPath?: string;
  @Input() timezone?: string;

  isDisabled?: boolean;

  readonly form: FormGroup;
  readonly config$: Observable<SwuiDateTimepickerConfig>;
  readonly nowDate = moment().utc().startOf('day');
  readonly expirationPeriods: SwuiSelectOption[] = [
    {
      id: 'hourly',
      text: 'PROMOTION.FORM.REWARDS.FREEBET.hours'
    },
    {
      id: 'daily',
      text: 'PROMOTION.FORM.REWARDS.FREEBET.days'
    },
    {
      id: 'weekly',
      text: 'PROMOTION.FORM.REWARDS.FREEBET.weeks'
    },
    {
      id: 'monthly',
      text: 'PROMOTION.FORM.REWARDS.FREEBET.months'
    },
  ];

  constructor( { appSettings$ }: SettingsService ) {
    super();

    this.config$ = appSettings$.pipe(
      map<AppSettings, SwuiDatePickerConfig>(( { dateFormat } ) => ({
        dateFormat,
        timePicker: true,
        timeFormat: 'HH:mm',
        timeDisableLevel: { second: false }
      }))
    );

    this.form = new FormGroup({
      freebetAmount: new FormControl(null, [
          Validators.required,
          Validators.min(1),
          Validators.max(999),
        ]
      ),
      expirationType: new FormControl('in'),
      expirationPeriodType: new FormControl('daily'),
      expirationPeriod: new FormControl(null, [
        Validators.min(1),
        Validators.max(999),
      ]),
      expirationDate: new FormControl('')
    }, [
      ctrl => {
        const f = ctrl as FormGroup;
        const expirationType = f.get('expirationType');
        const expirationPeriod = f.get('expirationPeriod');
        const expirationPeriodErrors = expirationPeriod?.errors || {};

        if (expirationType) {
          if (expirationType.value === 'in' && !expirationPeriod?.value) {
            expirationPeriod?.setErrors(Object.assign(expirationPeriodErrors, { 'required': true }));
          } else {
            if ('required' in expirationPeriodErrors) {
              delete expirationPeriodErrors['required'];
              if (Object.keys(expirationPeriodErrors).length === 0) {
                expirationPeriod?.setErrors(null);
              }
            }
          }
        }

        return null;
      },
      ctrl => {
        const f = ctrl as FormGroup;
        const expirationType = f.get('expirationType');
        const expirationDate = f.get('expirationDate');

        if (expirationType) {
          if (expirationType.value === 'on' && !expirationDate?.value) {
            expirationDate?.setErrors({ 'required': true });
          } else {
            expirationDate?.setErrors(null);
          }
        }

        return null;
      },
    ]);
  }

  ngOnInit() {
    super.ngOnInit();

    this.expirationType.valueChanges.pipe(
      takeUntil(this.destroyed),
    ).subscribe(expirationType => {
      this.updatePeriods(expirationType);
    });
  }

  get freebetAmount(): FormControl {
    return this.form.get('freebetAmount') as FormControl;
  }

  get expirationType(): FormControl {
    return this.form.get('expirationType') as FormControl;
  }

  get expirationPeriod(): FormControl {
    return this.form.get('expirationPeriod') as FormControl;
  }

  get expirationPeriodType(): FormControl {
    return this.form.get('expirationPeriodType') as FormControl;
  }

  get expirationDate(): FormControl {
    return this.form.get('expirationDate') as FormControl;
  }

  setDisabledState( isDisabled: boolean ) {
    isDisabled ? this.form.disable() : this.form.enable();
    this.isDisabled = isDisabled;
  }

  writeValue( value: any | undefined ): void {
    super.writeValue(value);
    this.updatePeriods(this.expirationType.value);
  }

  protected transformValue( value: FreebetRewardData | undefined ): FormParams {
    let expirationDate = value?.expirationDate ?
      moment.tz(moment.utc(value?.expirationDate), this.timezone ? this.timezone : '').format(DATE_TIME_FORMAT) :
      '';

    const expirationType = value?.expirationDate ? 'on' : 'in';
    return {
      freebetAmount: value?.freebetAmount || null,
      expirationType,
      expirationPeriodType: value?.expirationPeriodType || 'daily',
      expirationPeriod: value?.expirationPeriod || null,
      expirationDate: expirationDate,
    };
  }

  protected transformForm( value: FormParams ): FreebetRewardData {
    return {
      freebetAmount: value.freebetAmount || 0,
      ...(value.expirationType === 'on' && {
        expirationDate: value.expirationDate || undefined
      }),
      ...(value.expirationType === 'in' && {
        expirationPeriodType: value.expirationPeriodType || undefined,
        expirationPeriod: value.expirationPeriod || undefined
      })
    };
  }

  private updatePeriods(expirationType: string) {
    if (expirationType === 'on') {
      this.expirationPeriod.setValue(null);
      this.expirationPeriod.disable({emitEvent: false});
      this.expirationPeriodType.disable({emitEvent: false});

      if (!this.isDisabled) {
        this.expirationDate.enable({ emitEvent: false });
      }
    } else {
      this.expirationDate.setValue(null);
      this.expirationDate.disable({emitEvent: false});

      if (!this.isDisabled) {
        this.expirationPeriod.enable({ emitEvent: false });
        this.expirationPeriodType.enable({ emitEvent: false });
      }
    }
  }
}
