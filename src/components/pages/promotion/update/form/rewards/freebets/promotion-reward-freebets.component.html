<form class="vertical-form" [formGroup]="form">
  <div fxLayout="column" fxFlex.sm="100" fxFlex="80">
    <mat-error>
      <lib-swui-control-messages [control]="form"></lib-swui-control-messages>
    </mat-error>

    <h4 class="row-title">{{ 'PROMOTION.FORM.REWARDS.FREEBET.rewardAmount' | translate }}</h4>
    <div fxLayout.lt-sm="column" fxLayout="row">
      <div fxLayout="row" fxFlex.lt-sm="100" fxFlex="280px" style="padding-top: 28px;">
        {{ 'PROMOTION.FORM.REWARDS.FREEBET.numberOfFreeBets' | translate }} *
        <mat-icon
          class="help-icon margin-left20"
          svgIcon="question_mark"
          matTooltip="{{ 'PROMOTION.FORM.REWARDS.FREEBET.TOOLTIP.numberOfFreeBets' | translate }}">
        </mat-icon>
      </div>
      <div class="form-field" fxFlex.lt-sm="100" fxFlex="180px">
        <div class="form-field__input">
          <mat-form-field class="form-field__mat-field" appearance="outline">
            <input type="number" placeholder="eg. 100" matInput formControlName="freebetAmount">
            <mat-error>
              <lib-swui-control-messages [control]="freebetAmount"></lib-swui-control-messages>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>

    <h4 class="row-title">{{ 'PROMOTION.FORM.REWARDS.FREEBET.rewardProcessing' | translate }}</h4>
    <div fxLayout.lt-sm="column" fxLayout="row">
      <div fxLayout="row" fxFlex.lt-sm="100" fxFlex="234px" style="padding-top: 28px;">
        {{ 'PROMOTION.FORM.REWARDS.FREEBET.expiration' | translate }} *
        <mat-icon
          class="help-icon margin-left20"
          svgIcon="question_mark"
          matTooltip="{{ 'PROMOTION.FORM.REWARDS.FREEBET.TOOLTIP.expiration' | translate }}">
        </mat-icon>
      </div>

      <mat-radio-group [formControl]="expirationType" fxLayout="column" class="margin-bottom12">
        <div fxLayout.lt-sm="column" fxLayout="row">
          <mat-radio-button [value]="'in'" class="margin-bottom8" fxFlexAlign="center">
            <div>{{ 'PROMOTION.FORM.REWARDS.FREEBET.in' | translate }}</div>
          </mat-radio-button>

          <div class="form-field" fxFlex.lt-sm="100" fxFlex="180px" style="margin-left: 5px;">
            <div class="form-field__input">
              <mat-form-field class="form-field__mat-field" appearance="outline">
                <input type="number" placeholder="eg. 100" matInput formControlName="expirationPeriod">
                <mat-error>
                  <lib-swui-control-messages [control]="expirationPeriod"></lib-swui-control-messages>
                </mat-error>
              </mat-form-field>
            </div>
          </div>
          <div class="form-field__input">
            <mat-form-field class="form-field__mat-field" appearance="outline">
              <lib-swui-select [disableEmptyOption]="true" [data]="expirationPeriods" formControlName="expirationPeriodType"></lib-swui-select>
            </mat-form-field>
          </div>
        </div>

        <div fxLayout.lt-sm="column" fxLayout="row">
          <mat-radio-button [value]="'on'" fxFlexAlign="center">
            <div>{{ 'PROMOTION.FORM.REWARDS.FREEBET.on' | translate }}</div>
          </mat-radio-button>

          <div class="form-field" fxFlex.lt-sm="100" fxFlex="180px">
            <div class="form-field__input">
              <mat-form-field appearance="outline">
                <mat-label>{{ 'PROMOTION.FORM.REWARDS.FREEBET.expirationDate' | translate }}</mat-label>
                <lib-swui-date-picker
                  [formControl]="expirationDate"
                  [config]="config$ | async"
                  [minDate]="nowDate">
                </lib-swui-date-picker>
                <button mat-icon-button matSuffix>
                  <mat-icon>date_range</mat-icon>
                </button>
                <mat-error>
                  <lib-swui-control-messages [control]="expirationDate"></lib-swui-control-messages>
                </mat-error>
              </mat-form-field>
            </div>
          </div>
        </div>
      </mat-radio-group>
    </div>
  </div>
</form>
