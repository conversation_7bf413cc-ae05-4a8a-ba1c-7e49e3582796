import { Component } from '@angular/core';
import { AbstractControl, FormControl } from '@angular/forms';
import { MatIconRegistry } from '@angular/material/icon';
import { FakeMatIconRegistry } from '@angular/material/icon/testing';
import { createHostFactory, SpectatorHost } from '@ngneat/spectator';
import { TranslateModule } from '@ngx-translate/core';
import { SettingsService } from '@skywind-group/lib-swui';
import { PromotionRewardFreebetsComponent } from './promotion-reward-freebets.component';
import { MODULES } from './promotion-reward-freebets.module';

@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}

describe('PromotionRewardFreebetsComponent', () => {
  let spectator: SpectatorHost<PromotionRewardFreebetsComponent, CustomHostComponent>;

  const createHost = createHostFactory({
    component: PromotionRewardFreebetsComponent,
    host: CustomHostComponent,
    imports: [
      ...MODULES,
      TranslateModule.forRoot(),
    ],
    providers: [
      SettingsService,
      { provide: MatIconRegistry, useClass: FakeMatIconRegistry },
    ],
  });

  const value: any = {
    freebetAmount: 100,
    expirationType: 'on',
    expirationPeriodType: 'daily',
    expirationPeriod: 10,
    expirationDate: '2025-12-12 00:00:00',
  };

  const initialFormValue: any = {
    freebetAmount: null,
    expirationType: 'in',
    expirationPeriodType: 'daily',
    expirationPeriod: null
  };

  beforeEach(() => {
    spectator = createHost(`<sw-promotion-rewards-freebets [formControl]="form"></sw-promotion-rewards-freebets>`);
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();
  });

  it('freebetAmount field validity', () => {
    const freebetAmount: AbstractControl | null = spectator.component.form.get('freebetAmount');

    expect(freebetAmount?.valid).toBeFalse();

    freebetAmount?.setValue('');
    expect(freebetAmount?.hasError('required')).toBeTruthy();

    freebetAmount?.setValue(-1);
    expect(freebetAmount?.hasError('min')).toBeTruthy();

    freebetAmount?.setValue(1000000);
    expect(freebetAmount?.hasError('max')).toBeTruthy();

    freebetAmount?.setValue(10);
    expect(freebetAmount?.valid).toBeTrue();
  });

  it('expirationPeriod field validity', () => {
    const expirationPeriod: AbstractControl | null = spectator.component.form.get('expirationPeriod');

    expect(expirationPeriod?.valid).toBeFalse();

    expirationPeriod?.setValue('');
    expect(expirationPeriod?.hasError('required')).toBeTruthy();

    expirationPeriod?.setValue(-1);
    expect(expirationPeriod?.hasError('min')).toBeTruthy();

    expirationPeriod?.setValue(1000000);
    expect(expirationPeriod?.hasError('max')).toBeTruthy();

    expirationPeriod?.setValue(10);
    expect(expirationPeriod?.valid).toBeTrue();
  });

  it('expirationType and expirationPeriod validation', () => {
    const expirationType: AbstractControl | null = spectator.component.form.get('expirationType');
    const expirationPeriod: AbstractControl | null = spectator.component.form.get('expirationPeriod');
    const expirationDate: AbstractControl | null = spectator.component.form.get('expirationDate');

    expirationType?.setValue('in');

    expect(expirationPeriod?.hasError('required')).toBeTruthy();
    expect(expirationDate?.invalid).toBeFalse();
  });

  it('expirationType and expirationDate validation', () => {
    const expirationType: AbstractControl | null = spectator.component.form.get('expirationType');
    const expirationDate: AbstractControl | null = spectator.component.form.get('expirationDate');
    const expirationPeriod: AbstractControl | null = spectator.component.form.get('expirationPeriod');

    expirationType?.setValue('on');

    expect(expirationDate?.hasError('required')).toBeTruthy();
    expect(expirationPeriod?.invalid).toBeFalse();
  });

  it('writeValue has been called', () => {
    const onWriteValueSpy = spyOn(spectator.component, 'writeValue');
    spectator.hostComponent.form.patchValue(value);

    expect(onWriteValueSpy).toHaveBeenCalled();
  });

  it('form has been patched', () => {
    spectator.hostComponent.form.patchValue(value);

    const newValue = {...value};

    delete newValue.expirationPeriodType;
    delete newValue.expirationPeriod;

    expect(spectator.component.form.value).toEqual(newValue);
  });

  it('form has not patched with null', () => {
    spectator.hostComponent.form.patchValue(null);
    expect(spectator.component.form.value).toEqual(initialFormValue);
  });
});
