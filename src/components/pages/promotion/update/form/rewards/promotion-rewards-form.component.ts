import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import {
  AbstractFormValueAccessor, formValueProviders
} from '../../../../../../common/lib/abstract-form-value-accessor';
import { Entity } from '../../../../../../common/models/entity';
import {
  FreebetRewardInfo, PromotionData, PromotionRewardInfo, RewardType
} from '../../../../../../common/models/promotion';
import { PromotionFormService } from '../../promotion-form-service/promotion-form.service';
import { FreebetRewardData } from './freebets/promotion-reward-freebets.component';

interface RewardTypeItem {
  id: string;
  title: string;
  disabled: boolean;
}

interface FormParams {
  type: RewardType;
  freebet?: FreebetRewardData;
}

@Component({
  selector: 'sw-promotion-rewards-form',
  templateUrl: './promotion-rewards-form.component.html',
  styleUrls: ['./promotion-rewards-form.component.scss'],
  providers: formValueProviders(PromotionRewardsFormComponent)
})
export class PromotionRewardsFormComponent extends AbstractFormValueAccessor<PromotionRewardInfo> implements OnInit {

  @Input() entity?: Entity;
  @Input() brandPath?: string;

  readonly promotion?: PromotionData;
  readonly form: FormGroup;

  types?: RewardTypeItem[];

  constructor( { snapshot: { data: { promotion } } }: ActivatedRoute,
               private readonly auth: SwHubAuthService,
               private readonly formService: PromotionFormService,
  ) {
    super();

    this.promotion = promotion;

    this.form = new FormGroup({
      type: new FormControl({
        value: 'freebet'
      }, Validators.required),
      freebet: new FormControl({})
    });
  }

  ngOnInit(): void {
    super.ngOnInit();

    this.types = Object.entries({
      'freebet': 'freebets'
    }).map(( [id, permission] ) => ({
      id,
      title: `PROMOTION.TYPES.${id}`,
      disabled: !this.auth.allowedTo([`keyentity:promotion:${permission}:create`]) ||
        id === 'freebet' && !!this.entity?.isMerchant && !this.entity?.params?.isPromoInternal,
    }));

    this.form.patchValue(this.transformValue(this.promotion));
    this.form.markAsPristine();
    this.form.markAsUntouched();

    this.formService.rewardType = 'freebet';
  }

  get typeControl(): FormControl {
    return this.form.get('type') as FormControl;
  }

  get freebetControl(): FormControl {
    return this.form.get('freebet') as FormControl;
  }

  protected transformValue( value: PromotionRewardInfo | undefined ): FormParams {
    const type = value?.type || 'freebet';
    const reward = Array.isArray(value?.rewards) && value?.rewards.length ? value.rewards[0] : undefined;
    const freebet: FreebetRewardData = type === 'freebet' && reward ? (reward as FreebetRewardInfo) : {};
    return { type, freebet };
  }

  protected transformForm( value: FormParams ): PromotionRewardInfo | undefined {
    const type = value.type;
    if (type === 'freebet' && value.freebet) {
      const reward: FreebetRewardInfo = {
        startRewardOnGameOpen: false,
        expirationDate: value.freebet.expirationDate,
        expirationPeriod: value.freebet.expirationPeriod,
        expirationPeriodType: value.freebet.expirationPeriodType,
        freebetAmount: value.freebet.freebetAmount || 0,
        games: []
      };
      return {
        type,
        rewards: [reward],
        startRewardOnGameOpen: reward.startRewardOnGameOpen
      };
    }
    return undefined;
  }
}
