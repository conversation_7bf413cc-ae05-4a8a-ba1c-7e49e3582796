<div class="mat-card mat-elevation-z0">
  <form class="vertical-form" [formGroup]="form">
    <div fxLayout.lt-sm="column" fxLayout="row">
      <mat-form-field appearance="outline" class="margin-top8" style="width: 300px;">
        <mat-label>{{ 'PROMOTION.FORM.REWARDS.type' | translate }}</mat-label>
        <mat-select required
                    formControlName="type"
                    [disabled]="promotion?.id">
          <mat-option *ngFor="let item of types"
                      [value]="item.id"
                      [disabled]="item.disabled">
            {{ item.title | translate }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="typeControl.hasError('required')">{{ 'VALIDATION.fieldRequired' | translate }}</mat-error>
      </mat-form-field>
      <div class="margin-right24 margin-left24" fxFlex="24px" fxFlexAlign="center">
        <mat-icon
          class="help-icon"
          svgIcon="question_mark"
          matTooltip="Free Bets">
        </mat-icon>
      </div>
    </div>

    <sw-promotion-rewards-freebets [formControl]="freebetControl"
                                   [brandPath]="brandPath"
                                   [timezone]="promotion?.timezone">
    </sw-promotion-rewards-freebets>
  </form>
</div>
