<div class="mat-card mat-elevation-z0 margin-bottom20">
  <h3 class="no-margin-top mat-title">{{ 'TOURNAMENT.FORM.SEGMENTATION.players' | translate }}</h3>
  <div class="margin-bottom16">
    <ng-container *ngIf="!isGridVisible; else gridVisible">
      <ng-container *ngIf="playersLength; else noPlayersInPromotion">
        {{ 'PROMOTION.FORM.SEGMENTATION.playersHintStart' | translate }}
        <b>{{ playersLength }}</b>
        {{ 'TOURNAMENT.FORM.SEGMENTATION.playersHintEnd' | translate }}
      </ng-container>
      <ng-template #noPlayersInPromotion>
        <span *ngIf="!newPlayersLength">{{ 'PROMOTION.FORM.SEGMENTATION.noPlayersInPromotion' | translate }}</span>
      </ng-template>
      <a *ngIf="playersLength"
         class="link"
         style="padding-left: 40px; cursor: pointer;"
         (click)="viewPlayers()">
        {{ 'TOURNAMENT.FORM.SEGMENTATION.viewPlayers' | translate }}
      </a>
    </ng-container>
    <ng-template #gridVisible>
      <div class="players">
        <div class="players-row players-row__header">
          <div class="players-column">{{ 'PROMOTION.FORM.SEGMENTATION.playerCode' | translate }}</div>
          <div class="players-column">{{ 'PROMOTION.FORM.SEGMENTATION.status' | translate }}</div>
          <div class="players-column">{{ 'PROMOTION.FORM.SEGMENTATION.expiryDate' | translate }}</div>
          <div class="players-column"></div>
        </div>
        <div class="players-row" *ngFor="let player of visiblePromoPlayers">
          <div class="players-column">{{ player.playerCode }}</div>
          <div class="players-column">
            <ng-container *ngIf="playerStatuses[player.status] as status">
              <span [class]="status.class">{{ status.title | translate }}</span>
            </ng-container>
          </div>
          <div
            class="players-column">{{ player.expireAt | formatDate : 'MMM DD, YYYY HH:mm' : timeZone : false }}</div>
          <div class="players-column">
            <button mat-icon-button *ngIf="player.status && player.status !=='revoked'" (click)="onRemoveItem(player)">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>
      </div>
      <mat-paginator
        [length]="playersLength"
        [pageIndex]="pageIndex"
        [hidePageSize]="true"
        [pageSize]="10"
        (page)="onPageChange($event)"
      ></mat-paginator>
    </ng-template>
  </div>
  <div>
    <button mat-stroked-button
            class="mat-button-md"
            [disabled]="!canChangePlayers"
            [color]="'primary'"
            (click)="openDialog()">
      <mat-icon>add</mat-icon>
      Add player filter
    </button>
  </div>

  <div *ngIf="csvPlayers?.length" class="players-banner margin-top16">
    <div *ngFor="let csv of csvPlayers">
      {{ csv.title }} with {{csv.players.length}} players
      <button mat-icon-button color="primary" (click)="onRemoveCsv(csv)">
        <mat-icon>delete_outline</mat-icon>
      </button>
    </div>
  </div>
</div>
