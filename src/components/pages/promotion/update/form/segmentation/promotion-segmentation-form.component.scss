.segments {
  display: flex;
  width: 100%;

  &__item {
    position: relative;
    display: block;
    width: calc(50% - 43px);

    &--middle {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 86px;
      padding: 0 20px;
      box-sizing: border-box;
    }
  }

  &__button {
    width: 46px;
    min-width: 46px;
    margin-top: 20px;
    padding: 0;
    text-transform: uppercase;
  }

  &__btn-icon {
    font-size: 18px;
    height: 18px;
    width: 18px;
    margin-bottom: 1px;
  }
}

.error-message {
  padding: 20px;
  border-radius: 4px;
  background-color: #ff8a80;
  margin-bottom: 20px;
}

.players {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;

  &-row {
    display: flex;
    align-items: center;
    padding: 0 24px;
    height: 45px;
    color: rgba(0, 0, 0, 0.87);

    &:not(&__header):nth-child(even) {
      background-color: #eef1f5;
    }

    &:not(&__header):nth-child(odd) {
      background-color: #f9f9fa;
    }

    & + & {
      border-top: 1px solid rgba(0, 0, 0, 0.12);
    }

    &__header {
      font-weight: 500;
    }
  }

  &-column {
    display: flex;
    justify-content: left;
    flex: 0 0 20%;

    &:first-child {
      flex: 0 0 40%;
    }

    &:last-child {
      flex: 0 0 10%;
    }
  }
}

.players-banner {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  padding: 10px 20px;
  color: #1E46A5;
  background-color: #E0EBFF;
  border: 1px solid #1E46A5;
  border-radius: 4px;
}
