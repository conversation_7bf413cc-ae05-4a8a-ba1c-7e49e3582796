import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule } from '@angular/material/paginator';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiChipsAutocompleteModule, SwuiGridModule } from '@skywind-group/lib-swui';
import { BrandsManagerModule } from '../../../../../../common/components/brands-manager/brands-manager.module';
import { PromotionSegmentationFormComponent } from './promotion-segmentation-form.component';
import { UploadPromoPlayerDialogModule } from './upload-promo-player-dialog/upload-promo-player-dialog.module';
import { PipesModule } from '../../../../../../common/pipes/pipes.module';
import { FlexModule } from '@angular/flex-layout';

export const MODULES = [
  MatCardModule,
  BrandsManagerModule,
  MatDialogModule,
  MatButtonModule,
  MatFormFieldModule,
  MatInputModule,
  MatIconModule,
  ReactiveFormsModule,
  MatFormFieldModule,
  SwuiChipsAutocompleteModule,
  SwuiGridModule,
  UploadPromoPlayerDialogModule,
  MatPaginatorModule,
  PipesModule,
  FlexModule
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...MODULES,
  ],
  exports: [
    PromotionSegmentationFormComponent,
  ],
  declarations: [
    PromotionSegmentationFormComponent,
  ],
})
export class PromotionSegmentationFormModule {
}
