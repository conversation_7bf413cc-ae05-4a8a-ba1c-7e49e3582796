import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { ActivatedRoute, Params } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ActionConfirmDialogComponent } from '@skywind-group/lib-swui';
import { BehaviorSubject, ReplaySubject, Subject } from 'rxjs';
import { filter, map, switchMap, take, takeUntil } from 'rxjs/operators';
import { PromotionPlayersService } from 'src/common/services/promotion-players.service';

import {
  AbstractControlValueAccessor, controlValueProviders
} from '../../../../../../common/lib/abstract-control-value-accessor';
import { PromoPlayer } from '../../../../../../common/models/promo-player.model';
import { PromotionData } from '../../../../../../common/models/promotion';
import { PLAYER_STATUS, SCHEMA } from './schema';
import {
  CsvPlayers, UploadPromoPlayerDialogComponent
} from './upload-promo-player-dialog/upload-promo-player-dialog.component';

const DISABLED_STATES: Record<string, boolean> = {
  finished: true,
  expired: true
};

@Component({
  selector: 'sw-promotion-segmentation-from',
  templateUrl: './promotion-segmentation-form.component.html',
  styleUrls: ['./promotion-segmentation-form.component.scss'],
  providers: [
    ...controlValueProviders(PromotionSegmentationFormComponent),
  ]
})
export class PromotionSegmentationFormComponent extends AbstractControlValueAccessor implements OnInit, OnDestroy {
  @Input() promotion: PromotionData | undefined;
  @Input() isDuplicate = false;
  @Input() rewardType?: string;

  @Input()
  set shouldPlayersBeUpdated( value: boolean | undefined ) {
    if (value) {
      this.pageIndex$.next(this.pageIndex);
    }
  }

  @Input()
  set schedule( val: PromotionData | undefined ) {
    if (!val) {
      return;
    }
    this.schedule$.next(val);
  }

  schema = SCHEMA;
  visiblePromoPlayers: PromoPlayer[] = [];
  playerStatuses = PLAYER_STATUS;
  isGridVisible = false;
  playersLength = 0;
  csvPlayers: CsvPlayers[] = [];
  playersList: PromoPlayer[] = [];
  newPlayersLength = 0;
  pageIndex = 0;
  readonly params: Params;

  private readonly pageIndex$ = new ReplaySubject<number>(1);
  private readonly destroy$ = new Subject();
  private readonly schedule$ = new BehaviorSubject<PromotionData | null>(null);

  constructor( private readonly dialog: MatDialog,
               private readonly promotionPlayersService: PromotionPlayersService,
               { snapshot: { params } }: ActivatedRoute,
               private readonly translate: TranslateService,
  ) {
    super();
    this.params = params;
  }

  get canChangePlayers(): boolean {
    if (!this.promotion?.state) {
      return true;
    }

    return !DISABLED_STATES[this.promotion.state];
  }

  ngOnInit() {
    this.pageIndex$
      .pipe(
        filter(() => this.params && this.params.id && !this.isDuplicate),
        switchMap(pageIndex => this.promotionPlayersService.getList(this.params.id, this.params.path, pageIndex)),
        map(resp => {
          this.playersLength = +(resp.headers.get('x-paging-total') || '0');
          return resp.body as PromoPlayer[];
        }),
        takeUntil(this.destroy$),
      )
      .subscribe(promoPlayers => {
        this.visiblePromoPlayers = promoPlayers;
      });
    this.resetPagination();
  }

  writeValue( value: string[] ): void {
    this.newPlayersLength = value?.length;
    this.csvPlayers = [];
    this.resetPagination();
  }

  openDialog() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.width = '750px';
    dialogConfig.panelClass = 'import-dialog';
    dialogConfig.position = {
      top: '0',
    };
    this.dialog.open(UploadPromoPlayerDialogComponent, dialogConfig).afterClosed()
      .subscribe(( data: CsvPlayers ) => {
        if (data) {
          const { players } = data;
          this.csvPlayers.push(data);
          this.isGridVisible = false;
          this.newPlayersLength = players.length;
          this.playersList = this.csvPlayers.reduce(( acc: PromoPlayer[], cur: CsvPlayers ) => {
            acc.push(...cur.players);
            return acc;
          }, []);

          this.updateCsvPlayers();
        }
      });
  }

  updateCsvPlayers() {
    const allPlayers = this.csvPlayers.reduce(( acc: string[], { players } ) => {
      const playerCodes = players.map(( { playerCode } ) => playerCode);
      acc = [...acc, ...playerCodes];
      return acc;
    }, []);
    this.onChange(allPlayers);
  }

  viewPlayers() {
    this.isGridVisible = true;
  }

  onRemoveItem( { playerCode }: PromoPlayer ) {
    let deleteConfirmText = 'PROMOTION.FORM.SEGMENTATION.removePlayer';
    let forceDelete = 'false';

    if (this.promotion?.state !== 'pending') {
      deleteConfirmText = 'PROMOTION.FORM.SEGMENTATION.forceRemovePlayer';
      forceDelete = 'true';
    }

    this.dialog.open(ActionConfirmDialogComponent, {
      data: {
        action: {
          confirmText: this.translate.instant(deleteConfirmText, { playerCode }),
        }
      }
    }).afterClosed()
      .pipe(
        filter(( { confirmed } ) => confirmed),
        switchMap(() => this.promotionPlayersService.delete(this.params.id, this.params.path, playerCode, forceDelete)),
        switchMap(() => this.pageIndex$),
        take(1),
      )
      .subscribe(index => {
        this.pageIndex$.next(index);
      });
  }

  onPageChange( { pageIndex }: { pageIndex: number } ) {
    this.pageIndex$.next(pageIndex);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onRemoveCsv( csv: CsvPlayers ) {
    const removeIndex = this.csvPlayers.map(csvPlayer => csvPlayer.title).indexOf(csv.title);
    this.csvPlayers.splice(removeIndex, 1);
    this.updateCsvPlayers();
  }

  get timeZone(): string | undefined {
    const value = this.schedule$.value;
    return value ? value.timezone : undefined;
  }

  private resetPagination() {
    this.pageIndex = 0;
    this.pageIndex$.next(0);
  }
}
