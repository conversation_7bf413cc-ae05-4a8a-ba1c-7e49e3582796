import { SwuiGridField } from '@skywind-group/lib-swui';
import { PromoPlayer } from '../../../../../../common/models/promo-player.model';

export const PLAYER_STATUS: { [key: string]: { title: string; class: string } } = {
  alt_finished: { title: 'PROMOTION.STATE.alt_finished', class: 'sw-chip' },
  alt_in_progress: { title: 'PROMOTION.STATE.alt_in_progress', class: 'sw-chip sw-chip-green' },
  alt_pending: { title: 'PROMOTION.STATE.new', class: 'sw-chip sw-chip-blue' },
  awarded: { title: 'PROMOTION.FORM.SEGMENTATION.PLAYER.status.awarded', class: 'sw-chip sw-chip-green' },
  confirmed: { title: 'PROMOTION.FORM.SEGMENTATION.PLAYER.status.confirmed', class: 'sw-chip sw-chip-blue' },
  expired: { title: 'PROMOTION.FORM.SEGMENTATION.PLAYER.status.expired', class: 'sw-chip' },
  finished: { title: 'PROMOTION.FORM.SEGMENTATION.PLAYER.status.finished', class: 'sw-chip' },
  inProgress: { title: 'PROMOTION.STATE.inProgress', class: 'sw-chip sw-chip-green' },
  in_progress: { title: 'PROMOTION.STATE.inProgress', class: 'sw-chip sw-chip-green' },
  not_started: { title: 'PROMOTION.FORM.SEGMENTATION.PLAYER.status.new', class: 'sw-chip sw-chip-blue' },
  pending: { title: 'PROMOTION.STATE.pending', class: 'sw-chip sw-chip-blue' },
  redeemable_expired: { title: 'PROMOTION.FORM.SEGMENTATION.PLAYER.status.redeemable_expired', class: 'sw-chip' },
  redeemed: { title: 'PROMOTION.FORM.SEGMENTATION.PLAYER.status.redeemed', class: 'sw-chip' },
  revoked: { title: 'PROMOTION.FORM.SEGMENTATION.PLAYER.status.revoked', class: 'sw-chip' },
  started: { title: 'PROMOTION.FORM.SEGMENTATION.PLAYER.status.started', class: 'sw-chip sw-chip-green' },
};

export const SCHEMA = [
  {
    field: 'playerCode',
    title: 'PROMOTION.FORM.SEGMENTATION.playerCode',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    isEditable: true,
  },
  {
    field: 'status',
    title: 'PROMOTION.FORM.SEGMENTATION.status',
    isList: true,
    isListVisible: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      titleFn: ( { status }: PromoPlayer ) => {
        const value = status && PLAYER_STATUS[status];
        return value && value.title;
      },
      classFn: ( { status }: PromoPlayer ) => {
        const value = status && PLAYER_STATUS[status];
        return value && value.class;
      }
    },
    alignment: {
      td: 'center',
      th: 'center'
    }
  },
  {
    field: 'expireAt',
    title: 'PROMOTION.FORM.SEGMENTATION.expiryDate',
    type: 'date',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    isEditable: true,
  },
] as SwuiGridField[];
