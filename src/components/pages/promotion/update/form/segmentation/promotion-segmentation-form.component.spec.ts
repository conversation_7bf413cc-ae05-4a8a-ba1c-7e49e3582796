import { Component } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatIconRegistry } from '@angular/material/icon';
import { FakeMatIconRegistry } from '@angular/material/icon/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { ActivatedRoute } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { createHostFactory, createServiceFactory, SpectatorHost, SpectatorService } from '@ngneat/spectator';
import { TranslateModule } from '@ngx-translate/core';
import { EntityService, MockEntityService } from '../../../../../../common/services/entity.service';
import {
  MockPromotionPlayersService, PromotionPlayersService
} from '../../../../../../common/services/promotion-players.service';
import { PromotionFormService } from '../../promotion-form-service/promotion-form.service';
import { PromotionSegmentationFormComponent } from './promotion-segmentation-form.component';
import { MODULES } from './promotion-segmentation-form.module';

@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}

describe('PromotionSegmentationFormComponent', () => {
  let spectator: SpectatorHost<PromotionSegmentationFormComponent, CustomHostComponent>;

  const createHost = createHostFactory({
    component: PromotionSegmentationFormComponent,
    host: CustomHostComponent,
    imports: [
      NoopAnimationsModule,
      TranslateModule.forRoot(),
      RouterTestingModule,
      ...MODULES,
    ],
    declarations: [PromotionSegmentationFormComponent],
    providers: [
      {
        provide: ActivatedRoute,
        useValue: { snapshot: { params: {} } }
      },
      { provide: EntityService, useClass: MockEntityService },
      { provide: PromotionPlayersService, useClass: MockPromotionPlayersService },
      { provide: MatIconRegistry, useClass: FakeMatIconRegistry }
    ]
  });

  let spectatorService: SpectatorService<PromotionFormService>;
  const createService = createServiceFactory({
    service: PromotionFormService,
  });

  const value = ['1', '2', '3'];

  beforeEach(() => {
    spectator = createHost(`<sw-promotion-segmentation-from [formControl]="form"></sw-promotion-segmentation-from>`);
    spectatorService = createService();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();

    expect(spectatorService.service).toBeTruthy();
    expect(spectatorService.service).toBeDefined();
  });

  it('check initial state', () => {
    expect(spectator.component.canChangePlayers).toBeTrue();
    expect(spectator.component.disabled).toBeFalse();
    expect(spectator.component.isGridVisible).toBeFalse();
    expect(spectator.component.playersLength).toEqual(0);
  });

  it('form set disabled state', () => {
    spectator.component.setDisabledState(true);
    expect(spectator.component.disabled).toBeTrue();
  });

  it('writeValue has been called', () => {
    const onWriteValueSpy = spyOn(spectator.component, 'writeValue');
    spectator.hostComponent.form.patchValue(value);

    expect(onWriteValueSpy).toHaveBeenCalled();
  });

  it('form has not patched with an empty array', () => {
    spectator.hostComponent.form.patchValue([]);
    expect(spectator.component.isGridVisible).toBeFalse();
    expect(spectator.component.playersLength).toEqual(0);
  });
});
