import { Component, Inject, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SwuiGridField } from '@skywind-group/lib-swui';
import { PromoPlayer } from '../../../../../../../common/models/promo-player.model';
import { SCHEMA } from './schema';

export interface CsvPlayers {
  title: string;
  players: PromoPlayer[];
}

@Component({
  selector: 'sw-upload-promo-player-dialog',
  templateUrl: './upload-promo-player-dialog.component.html',
  styleUrls: ['./upload-promo-player-dialog.component.scss'],
})
export class UploadPromoPlayerDialogComponent {

  @ViewChild('fileImportInput') fileImportInput: any;

  schema: SwuiGridField[] = SCHEMA;
  readonly MAXIMUM_ROWS = 10000;

  img = 'img/promo-example.png';
  fileName = '';
  csvRecords: any[] = [];
  playerIdIndex = 0;

  uploaded = false;

  isCsvValid = true;
  isMaximumNumberOfRowsExceeded = false;

  maximumRowsLabel = this.translate.instant('TOURNAMENT.FORM.SEGMENTATION.maximumRows',
    { maximumRows: this.MAXIMUM_ROWS.toLocaleString() });

  constructor( @Inject(MAT_DIALOG_DATA) public data: { addPlayersDescription: string, operators: Map<string, string> },
               public dialogRef: MatDialogRef<UploadPromoPlayerDialogComponent>,
               private translate: TranslateService,
  ) {
  }

  onSelectTheSameFile( event: any ) {
    event.target.value = null;
  }

  fileChangeListener( $event: any ) {
    this.isCsvValid = true;
    this.isMaximumNumberOfRowsExceeded = false;

    let files = $event.target.files;

    if (files && files[0] && this.isCSVFile(files[0])) {
      this.fileName = files[0].name;

      let input = $event.target;
      let reader = new FileReader();
      reader.readAsText(input.files[0]);

      reader.onload = () => {
        let csvData = reader.result;
        let csvRecordsArray = (csvData as string).split(/\r\n|\n/);

        let headersRow = this.getHeaderArray(csvRecordsArray);

        if (Array.isArray(headersRow)) {
          let playerIdCell = headersRow.find(item => item.replace(/\s/g, '')?.toLowerCase() === 'playerid');
          this.isCsvValid = !!playerIdCell;
          this.playerIdIndex = headersRow.indexOf(playerIdCell);
        } else {
          this.isCsvValid = false;
        }

        if (this.isCsvValid) {
          this.csvRecords = this.getDataRecordsArrayFromCSVFile(csvRecordsArray, headersRow.length);
          this.isMaximumNumberOfRowsExceeded = this.csvRecords?.length > this.MAXIMUM_ROWS;
        }
      };

      reader.onerror = () => {
        alert(this.translate.instant('TOURNAMENT.FORM.SEGMENTATION.unableToRead') + input.files[0]);
      };
    } else {
      alert(this.translate.instant('TOURNAMENT.FORM.SEGMENTATION.importValidFile'));
      this.fileReset();
    }
  }

  getDataRecordsArrayFromCSVFile( csvRecordsArray: any, headerLength: any ) {
    let dataArr: any[] = [];

    if (csvRecordsArray) {
      csvRecordsArray.forEach(( record: any ) => {
        let data = (record as string).split(',');

        if (Array.isArray(data) && data.length === headerLength) {
          let csvRecord = {
            playerCode: data[this.playerIdIndex].trim().replace(/"/g, ''),
          };

          if (csvRecord.playerCode) {
            dataArr.push(csvRecord);
          }
        } else {
          this.isCsvValid = false;
        }
      });

      dataArr.shift();
    }

    return dataArr;
  }

  isCSVFile( file: any ) {
    return file.name.endsWith('.csv');
  }

  getHeaderArray( csvRecordsArr: any ) {
    let headerArray: any[] = [];

    if (Array.isArray(csvRecordsArr)) {
      let headers = (csvRecordsArr[0] as string).split(',');
      headers.forEach(header => headerArray.push(header.replace(/"/g, '')));
    }

    return headerArray;
  }

  fileReset() {
    this.fileImportInput.nativeElement.value = '';
    this.csvRecords = [];
  }

  save() {
    if (!this.uploaded) {
      this.uploaded = true;
    } else {
      this.dialogRef.close({
        title: this.fileName,
        players: this.csvRecords
      });
    }
  }

  close() {
    if (!this.uploaded) {
      this.dialogRef.close();
    } else {
      this.uploaded = false;
    }
  }
}
