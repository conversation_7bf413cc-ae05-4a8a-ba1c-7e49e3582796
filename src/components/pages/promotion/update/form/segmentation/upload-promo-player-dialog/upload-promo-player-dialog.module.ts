import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule } from '@skywind-group/lib-swui';
import { UploadPromoPlayerDialogComponent } from './upload-promo-player-dialog.component';

export const MODULES = [
  MatButtonModule,
  MatDialogModule,
  MatFormFieldModule,
  MatInputModule,
  MatIconModule,
  SwuiGridModule
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...MODULES,
  ],
  declarations: [
    UploadPromoPlayerDialogComponent,
  ],
  exports: [
    UploadPromoPlayerDialogComponent,
  ],
  entryComponents: [
    UploadPromoPlayerDialogComponent,
  ]
})
export class UploadPromoPlayerDialogModule {
}
