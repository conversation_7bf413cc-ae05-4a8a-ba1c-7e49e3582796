<button class="mat-dialog-close" mat-button (click)="dialogRef.close()">
  <mat-icon svgIcon="clear" style="color: grey;"></mat-icon>
</button>
<h2 mat-dialog-title style="padding-left: 20px;">
  {{ !uploaded ? ('TOURNAMENT.FORM.SEGMENTATION.uploadPlayerList' | translate) :
  ('TOURNAMENT.FORM.SEGMENTATION.previewPlayerList' | translate) }}
</h2>

<mat-dialog-content class="upload-players-dialog">
  <ng-container *ngIf="!uploaded; else previewPlayerList">
    <div *ngIf="data?.addPlayersDescription" style="padding-top: 20px; padding-left: 20px;">
      {{ data?.addPlayersDescription | translate }}
    </div>

    <div style="padding-left: 20px;">
      {{ 'PROMOTION.FORM.SEGMENTATION.csvFileDescription' | translate }}
    </div>

    <div style="padding: 12px 0 0 20px;">
      {{ 'PROMOTION.FORM.SEGMENTATION.csvStructureDescription' | translate }}
    </div>

    <div style="padding-left: 20px;">
      {{ 'TOURNAMENT.FORM.SEGMENTATION.example' | translate }}:
    </div>

    <img [src]="img" alt="example" class="example-img">

    <div class="button-row" [ngClass]="{'without-padding-btm': !isCsvValid}">
      <input #fileImportInput
             hidden
             type="file"
             accept=".csv"
             (click)="onSelectTheSameFile($event)"
             (change)="fileChangeListener($event)"/>
      <mat-form-field appearance="outline" style="padding-top: 20px; width: 300px;">
        <input matInput [disabled]="true" [value]="fileName"/>
      </mat-form-field>
      <button class="mat-flat-button" style="background-color: #d9dbdf;" (click)="fileImportInput.click()">
        {{ 'DIALOG.chooseFile' | translate }}
      </button>
      <div style="padding-left: 20px;">
        <div>{{ 'TOURNAMENT.FORM.SEGMENTATION.fileType' | translate }}</div>
        <div>{{ maximumRowsLabel }}</div>
      </div>
    </div>
    <div *ngIf="!isCsvValid" class="invalid-message">
      {{ 'PROMOTION.FORM.SEGMENTATION.invalidCsv' | translate }}
    </div>
    <div *ngIf="isMaximumNumberOfRowsExceeded" class="invalid-message">
      {{ 'PROMOTION.FORM.SEGMENTATION.maximumNumberOfRowsIsExceeded' | translate }}
    </div>
  </ng-container>

  <ng-template #previewPlayerList>
    <span style="padding-left: 20px;">
      {{ csvRecords.length + ('TOURNAMENT.FORM.SEGMENTATION.playersInCSV' | translate) }}
    </span>
    <div class="height-grid">
      <lib-swui-grid
        [stickyHeader]="true"
        [data]="csvRecords"
        [schema]="schema"
        [columnsManagement]="false"
        [ignoreQueryParams]="true"
        [disableRefreshAction]="true"
        [pageSize]="50"
        [showTotalItems]="false">
      </lib-swui-grid>
    </div>
  </ng-template>
</mat-dialog-content>

<mat-dialog-actions class="upload-dialog-actions">
  <button mat-flat-button
          class="mat-button-md link"
          (click)="close()">
    {{ uploaded ? ('< ' + ('DIALOG.previous' | translate)) : ('DIALOG.cancel' | translate) }}
  </button>
  <button mat-flat-button
          class="mat-button-md"
          color="primary"
          style="margin-left: 20px;"
          [disabled]="!isCsvValid || !csvRecords.length"
          (click)="save()">
    {{ !uploaded ? (('DIALOG.next' | translate) + ' >') : ('DIALOG.savePlayers' | translate) }}
  </button>
</mat-dialog-actions>
