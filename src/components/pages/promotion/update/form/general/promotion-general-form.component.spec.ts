import { Component } from '@angular/core';
import { AbstractControl, FormControl } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { createHostFactory, SpectatorHost } from '@ngneat/spectator';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MockPromotionService, PromotionService } from '../../../../../../common/services/promotion.service';
import { PromotionGeneralFormComponent } from './promotion-general-form.component';
import { MODULES } from './promotion-general-form.module';

@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}

describe('PromotionGeneralFormComponent', () => {
  let spectator: SpectatorHost<PromotionGeneralFormComponent, CustomHostComponent>;

  const createHost = createHostFactory({
    component: PromotionGeneralFormComponent,
    host: CustomHostComponent,
    imports: [
      ...MODULES,
      TranslateModule.forRoot(),
    ],
    providers: [
      TranslateService,
      { provide: ActivatedRoute, useFactory: () => ({ snapshot: { data: {} } }) },
      { provide: PromotionService, useClass: MockPromotionService },
    ]
  });

  const value = {
    title: 'Promo',
    description: 'Description',
    externalId: '123',
  };

  const initialFormValue = {
    title: '',
    description: '',
    externalId: '',
  };

  beforeEach(() => {
    spectator = createHost(`<sw-promotion-general-form [formControl]="form"></sw-promotion-general-form>`);
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();
  });

  it('title field validity', () => {
    const title: AbstractControl | null = spectator.component.form.get('title');

    expect(title?.valid).toBeFalse();

    title?.setValue('');
    expect(title?.hasError('required')).toBeTruthy();

    title?.setValue('Promo');
    expect(title?.valid).toBeTrue();
  });

  it('form field Name setValue null validation should contain an error', () => {
    spectator.component.form.get('title')?.setValue(null);
    expect(spectator.component.validate()).toEqual({ invalidForm: { valid: false } });
  });

  it('form set disabled state', () => {
    spectator.component.setDisabledState(true);
    expect(spectator.component.form.disabled).toBeTrue();
  });

  it('writeValue has been called', () => {
    const onWriteValueSpy = spyOn(spectator.component, 'writeValue');
    spectator.hostComponent.form.patchValue(value);

    expect(onWriteValueSpy).toHaveBeenCalled();
  });

  it('form has been patched', () => {
    spectator.hostComponent.form.patchValue(value);
    expect(spectator.component.form.value).toEqual(value);
  });

  it('form has not patched with null', () => {
    spectator.hostComponent.form.patchValue(null);
    expect(spectator.component.form.value).toEqual(initialFormValue);
  });

  it('form field validity', () => {
    spectator.component.form.get('title')?.setValue('');

    expect(spectator.component.form.valid).toBeFalse();
    expect(spectator.hostComponent.form?.hasError('invalidForm')).toBeTrue();
  });
});
