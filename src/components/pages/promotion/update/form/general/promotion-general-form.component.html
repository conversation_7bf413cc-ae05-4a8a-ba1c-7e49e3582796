<div class="mat-card mat-elevation-z0">
  <form class="vertical-form" [formGroup]="form">
    <mat-form-field appearance="outline">
      <mat-label>{{'PROMOTION.FORM.GENERAL.TITLE.title' | translate}}</mat-label>
      <input formControlName="title" matInput required>
      <mat-spinner *ngIf="titleControl.pending" matSuffix [diameter]="18"
                   style="float: right; margin-left: 8px"></mat-spinner>
      <mat-error>
        <lib-swui-control-messages [control]="titleControl"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="margin-top12">
      <mat-label>{{'PROMOTION.FORM.GENERAL.DESCRIPTION.title' | translate}}</mat-label>
      <textarea
        formControlName="description"
        matInput
        matTextareaAutosize
        matAutosizeMinRows="3"
        matAutosizeMaxRows="5">
      </textarea>
    </mat-form-field>

    <mat-form-field appearance="outline" class="sd-schedule__item">
      <mat-label>{{'PROMOTION.FORM.GENERAL.EXTERNAL_ID.title' | translate}}</mat-label>
      <input formControlName="externalId" matInput>
      <mat-error>
        <lib-swui-control-messages [control]="externalIdControl"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </form>
</div>
