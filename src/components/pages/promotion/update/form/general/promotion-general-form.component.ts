import { Component, } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import {
  AbstractFormValueAccessor,
  formValueProviders
} from '../../../../../../common/lib/abstract-form-value-accessor';
import { PromotionData } from '../../../../../../common/models/promotion';
import { PromotionService } from '../../../../../../common/services/promotion.service';

export interface PromotionGeneral {
  title: string;
  description: string;
  externalId?: string;
}

function isNameDisabled( promotion: PromotionData ): boolean {
  if (typeof promotion.title !== 'undefined') {
    return true;
  }
  return ['inProgress', 'in_progress', 'alt_in_progress'].includes(promotion.state || '');
}

@Component({
  selector: 'sw-promotion-general-form',
  templateUrl: './promotion-general-form.component.html',
  providers: formValueProviders(PromotionGeneralFormComponent)
})
export class PromotionGeneralFormComponent extends AbstractFormValueAccessor<PromotionGeneral> {
  readonly form: FormGroup;

  constructor( { snapshot: { data } }: ActivatedRoute, service: PromotionService ) {
    super();
    const promotion: PromotionData = data.promotion;
    this.form = new FormGroup({
      title: new FormControl(promotion ? promotion.title : '', {
        updateOn: 'blur',
        validators: [
          Validators.required,
          Validators.maxLength(50)
        ],
        asyncValidators: [
          ( { value }: AbstractControl ): Observable<{ [key: string]: any } | null> => {
            if (value) {
              return service.query({ title: value }).pipe(
                catchError(() => of([])),
                map(( { length } ) => length ? { 'nameExists': true } : null),
              );
            }
            return of(null);
          }
        ]
      }),
      description: new FormControl(promotion ? promotion.description : ''),
      externalId: new FormControl(promotion ? promotion.externalId : '', {
        validators: [
          Validators.maxLength(50)
        ]
      }),
    });
    if (promotion && isNameDisabled(promotion)) {
      this.titleControl.disable();
    }
  }

  get titleControl(): FormControl {
    return this.form.get('title') as FormControl;
  }

  get externalIdControl(): FormControl {
    return this.form.get('externalId') as FormControl;
  }

  protected transformForm( value: any ): PromotionGeneral | undefined {
    if (value && !value.externalId) {
      delete value.externalId;
    }
    return super.transformForm(value);
  }
}
