import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule } from '@skywind-group/lib-swui';
import { PromotionGeneralFormComponent } from './promotion-general-form.component';


export const MODULES = [
  MatFormFieldModule,
  MatInputModule,
  MatProgressSpinnerModule,
  MatIconModule,
  FlexModule,
  ReactiveFormsModule,
  SwuiControlMessagesModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...MODULES,
  ],
  declarations: [
    PromotionGeneralFormComponent,
  ],
  exports: [
    PromotionGeneralFormComponent,
  ],
  providers: [],
})
export class PromotionGeneralFormModule {
}
