import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { FormSubmitted } from '../../../../../common/lib/form.submitted';

@Injectable()
export class PromotionFormService extends FormSubmitted {
  private _rewardType$ = new BehaviorSubject<string>('');

  set rewardType( val: string ) {
    this._rewardType$.next(val);
  }

  get rewardType$(): Observable<string> {
    return this._rewardType$ as Observable<string>;
  }
}
