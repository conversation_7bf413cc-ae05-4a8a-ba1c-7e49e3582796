import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MatIconRegistry } from '@angular/material/icon';
import { FakeMatIconRegistry } from '@angular/material/icon/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { ActivatedRoute } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { SettingsService, SwBrowserTitleService, SwHubAuthService, SwHubConfigService } from '@skywind-group/lib-swui';
import { of } from 'rxjs';
import { EntitySettingsService } from '../../../../common/services/entity-settings.service';
import { EntityService, MockEntityService } from '../../../../common/services/entity.service';
import { GameService, MockGameService } from '../../../../common/services/game.service';
import { MockAuthService } from '../../../../common/services/mock-auth.service';
import {
  MockPromotionLabelsService,
  PromotionLabelsService
} from '../../../../common/services/promotion-labels.service';
import {
  MockPromotionPlayersService,
  PromotionPlayersService
} from '../../../../common/services/promotion-players.service';
import { MockPromotionService, PromotionService } from '../../../../common/services/promotion.service';
import { PromotionFormService } from './promotion-form-service/promotion-form.service';
import { PromotionUpdateComponent } from './promotion-update.component';
import { MODULES } from './promotion-update.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';


describe('PromotionUpdateComponent', () => {
  let component: PromotionUpdateComponent;
  let fixture: ComponentFixture<PromotionUpdateComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        PromotionUpdateComponent,
      ],
      imports: [
        NoopAnimationsModule,
        TranslateModule.forRoot(),
        RouterTestingModule,
        HttpClientTestingModule,
        ...MODULES,
      ],
      providers: [
        SettingsService,
        PromotionFormService,
        SwHubConfigService,
        { provide: SwBrowserTitleService, useClass: SwBrowserTitleService },
        { provide: ActivatedRoute, useFactory: () => ({ snapshot: { data: {}, params: {} } }) },
        { provide: PromotionService, useClass: MockPromotionService },
        { provide: EntityService, useClass: MockEntityService },
        { provide: GameService, useClass: MockGameService },
        { provide: SwHubAuthService, useClass: MockAuthService },
        { provide: PromotionPlayersService, useClass: MockPromotionPlayersService },
        {
          provide: EntitySettingsService, useValue: {
            getSettings() {
              return of({});
            }
          }
        },
        { provide: PromotionLabelsService, useClass: MockPromotionLabelsService },
        { provide: MatIconRegistry, useClass: FakeMatIconRegistry }
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PromotionUpdateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
