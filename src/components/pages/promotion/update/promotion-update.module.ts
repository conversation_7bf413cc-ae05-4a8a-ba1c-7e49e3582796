import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatTabsModule } from '@angular/material/tabs';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { PromotionLabelsService } from '../../../../common/services/promotion-labels.service';
import { PromotionPlayersService } from '../../../../common/services/promotion-players.service';
import { PromotionBrandFormModule } from './form/brand/promotion-brand-form.module';
import { PromotionGamesFormModule } from './form/games/promotion-games-form.module';
import { PromotionGeneralFormModule } from './form/general/promotion-general-form.module';
import { PromotionRewardsFormModule } from './form/rewards/promotion-rewards-form.module';
import { PromotionScheduleFormModule } from './form/schedule/promotion-schedule-form.module';
import { PromotionSegmentationFormModule } from './form/segmentation/promotion-segmentation-form.module';
import { PromotionInfoModule } from './info/promotion-info.module';
import { PromotionUpdateComponent } from './promotion-update.component';

export const MODULES = [
  MatTabsModule,
  MatButtonModule,
  SwuiPagePanelModule,
  PromotionInfoModule,
  PromotionGeneralFormModule,
  PromotionGamesFormModule,
  PromotionScheduleFormModule,
  PromotionBrandFormModule,
  PromotionRewardsFormModule,
  PromotionSegmentationFormModule,
  ReactiveFormsModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    RouterModule,
    ...MODULES,
  ],
  declarations: [
    PromotionUpdateComponent,
  ],
  providers: [
    PromotionPlayersService,
    PromotionLabelsService,
  ],
})
export class PromotionUpdateModule {
}
