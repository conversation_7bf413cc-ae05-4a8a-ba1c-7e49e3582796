import { Component, forwardRef, ViewChild } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import { MatTabGroup } from '@angular/material/tabs';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  SwBrowserTitleService, SWUI_CONTROL_MESSAGES, SwuiIsControlInvalidService, SwuiNotificationsService
} from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { merge, Observable, of, Subscription, throwError, timer } from 'rxjs';
import {
  catchError, distinctUntilChanged, filter, finalize, map, mapTo, switchMap, take, takeUntil, tap
} from 'rxjs/operators';

import { BaseComponent } from '../../../../common/components/base.component';
import { SW_FORM_SERVICE } from '../../../../common/components/games-form/form-service.model';
import { MESSAGE_ERRORS } from '../../../../common/constants/errors-message-list';
import { Entity, isResellerType } from '../../../../common/models/entity';
import { Label } from '../../../../common/models/label';
import {
  FreebetGameConfig, FreebetRewardInfo, Promotion, PromotionData, PromotionRewardInfo,
  PromotionState, PromotionStatus
} from '../../../../common/models/promotion';
import { EntityService } from '../../../../common/services/entity.service';
import { PromotionLabelsService } from '../../../../common/services/promotion-labels.service';
import { PromotionPlayersService } from '../../../../common/services/promotion-players.service';
import { PromotionService } from '../../../../common/services/promotion.service';
import { PromotionFormService } from './promotion-form-service/promotion-form.service';

export const DEFAULT_CURRENCY = 'USD';

const MILLISECONDS_PER_DAY = 86400000;

@Component({
  selector: 'sw-promotion-update',
  templateUrl: 'promotion-update.component.html',
  styleUrls: ['promotion-update.component.scss'],
  providers: [
    PromotionFormService,
    { provide: SW_FORM_SERVICE, useExisting: forwardRef(() => PromotionFormService) },
    { provide: ErrorStateMatcher, useExisting: forwardRef(() => PromotionFormService) },
    { provide: SwuiIsControlInvalidService, useExisting: forwardRef(() => PromotionFormService) },
    { provide: SWUI_CONTROL_MESSAGES, useValue: MESSAGE_ERRORS }
  ]
})
export class PromotionUpdateComponent extends BaseComponent {
  readonly form: FormGroup;
  readonly segmentation = new FormControl([]);
  readonly title$: Observable<string>;

  readonly entity?: Entity;
  readonly operatorAllowed: boolean;
  readonly isDuplicate: boolean;

  @ViewChild('tabs') tabs?: MatTabGroup;

  state: PromotionState = 'new';
  status?: PromotionStatus;
  schedule$?: Observable<PromotionData | undefined>;
  labels: Label[] = [];

  defaultCurrency?: string;

  promotion?: PromotionData;

  rewardType?: string;

  shouldPlayersBeUpdated = false;

  private statusUpdateSubscription = new Subscription();

  constructor( { snapshot: { data: { brief, promotion, duplicate } } }: ActivatedRoute,
               private readonly translate: TranslateService,
               private readonly service: PromotionService,
               private readonly promotionPlayersService: PromotionPlayersService,
               private readonly router: Router,
               private readonly promotionLabelsService: PromotionLabelsService,
               private readonly entityService: EntityService,
               private readonly formService: PromotionFormService,
               private readonly notifications: SwuiNotificationsService,
               protected readonly browserTitleService: SwBrowserTitleService,
  ) {
    super();

    this.entity = brief;
    this.promotion = promotion;
    this.isDuplicate = duplicate;

    this.state = this.promotion?.state || 'new';
    this.status = this.promotion?.status;

    this.form = new FormGroup({
      general: new FormControl(),
      schedule: new FormControl(),
      rewards: new FormControl(),
      games: new FormControl(),
      brand: new FormControl()
    });

    if (this.isDuplicate && this.promotion) {
      this.promotion.activatedAt = undefined;
      this.promotion.modifiedBy = undefined;
    }

    if (['finished', 'alt_finished', 'expired'].includes(this.state)) {
      this.form.disable();
    }

    if (!this.rewardsEnabled) {
      this.rewardsControl.disable();
    }

    if (!this.gamesEnabled) {
      this.gamesControl.disable();
    }

    if (!this.isDuplicate && this.promotion) {
      this.brandControl.disable();
    }

    this.title$ = merge(
      this.generalControl.valueChanges,
      of({ name: this.promotion && this.promotion.title })
    ).pipe(
      map(value => value && value.name ? value.name : ''),
      map(name => ({
        key: this.promotion && !this.isDuplicate ? 'PROMOTION.edit' : 'PROMOTION.create',
        name
      })),
      switchMap(( { key, name } ) => {
        this.browserTitleService.setupTitles('Engagement', name);
        return this.translate.stream(key, { name });
      }),
      takeUntil(this.destroyed)
    );

    this.operatorAllowed = isResellerType(this.entity);

    if (!this.operatorAllowed) {
      this.defaultCurrency = this.entity?.defaultCurrency || DEFAULT_CURRENCY;
    }

    this.schedule$ = this.scheduleControl.valueChanges.pipe(
      filter(data => !!data),
      map(( value ) =>
        ({
          startDate: this.promotion?.startDate,
          endDate: this.promotion?.endDate,
          timezone: this.promotion?.timezone,
          ...value,
        })
      ),
    );

    this.brandControl.valueChanges
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.destroyed),
        tap(brand => {
          if (brand && this.gamesEnabled) {
            this.gamesControl.enable();
          } else if (this.rewardType === 'freebet') {
            this.gamesControl.disable();
          }
        }),
        filter(brand => !!brand),
        switchMap(brand =>
          this.entityService.getEntity(brand.brandPath)
            .pipe(
              catchError(() => of(null)),
            )),
      )
      .subscribe(
        entity => {
          this.defaultCurrency = entity?.defaultCurrency || DEFAULT_CURRENCY;
        }
      );

    this.formService.rewardType$
      .pipe(
        takeUntil(this.destroyed),
        filter(rewardType => !!rewardType),
      )
      .subscribe(rewardType => {
          this.rewardType = rewardType;
          !this.entity || (this.rewardType === 'freebet' && this.operatorAllowed) || !this.gamesEnabled
            ? this.gamesControl.disable()
            : this.gamesControl.enable();
        }
      );

    this.schedule$
      .pipe(
        takeUntil(this.destroyed),
      )
      .subscribe(value => {
        if (value?.startDate || value?.timezone) {
          this.listenStatus();
        }
      });
  }

  get rewardsControl(): FormGroup {
    return this.form.get('rewards') as FormGroup;
  }

  get brandControl(): FormControl {
    return this.form.get('brand') as FormControl;
  }

  get gamesControl(): FormControl {
    return this.form.get('games') as FormControl;
  }

  get scheduleControl(): FormControl {
    return this.form.get('schedule') as FormControl;
  }

  get isSidebarHidden(): boolean {
    return window.innerWidth <= 1300;
  }

  get brandPath(): string {
    const value = this.form.getRawValue();
    if (value.brand && value.brand.brandPath) {
      return value.brand.brandPath;
    }
    if (value.promotion && value.promotion.brandPath) {
      return value.promotion.brandPath;
    }
    return ':';
  }

  listenStatus() {
    this.statusUpdateSubscription.unsubscribe();

    if (!this.promotion?.id) {
      return;
    }

    let nextUpdate = this.scheduleControl.value?.startDate || this.promotion?.startDate;
    let timezone = this.scheduleControl.value?.timezone || this.promotion?.timezone;

    if (!nextUpdate) {
      return;
    }

    const userTimezoneOffset = moment.tz(nextUpdate, timezone).utcOffset() * 60000;
    const startDate = moment(nextUpdate).valueOf() - userTimezoneOffset;

    const time = moment(startDate).valueOf() - moment.utc().valueOf() + 10000;

    if (time < 0 || time > MILLISECONDS_PER_DAY) {
      return;
    }

    this.statusUpdateSubscription = timer(time)
      .pipe(
        switchMap(() => {
          this.shouldPlayersBeUpdated = false;

          return this.service.get(this.promotion?.id as string, this.brandPath);
        })
      )
      .subscribe(promotion => {
        this.promotion = { ...promotion, brandPath: this.brandPath };
        this.shouldPlayersBeUpdated = true;
      });
  }

  onStatusChanged( status: PromotionStatus ): void {
    this.shouldPlayersBeUpdated = false;
    this.status = status;
    if (!this.rewardsEnabled) {
      this.rewardsControl.disable();
    } else {
      this.rewardsControl.enable();
    }
    if (!this.gamesEnabled) {
      this.gamesControl.disable();
    } else {
      this.gamesControl.enable();
    }
    if (this.promotion && this.promotion.id) {
      const { brandPath } = this.promotion;
      const path = brandPath && brandPath !== ':' ? brandPath : undefined;
      this.service.changeStatus(this.promotion.id, status, path).pipe(
        take(1),
        catchError(error => {
          if (this.promotion) {
            this.promotion = { ...this.promotion };
          }
          return throwError(error);
        }),
        tap(() => {
          this.notifications.success(this.translate.instant('PROMOTION.statusChanged', { status: status }));
        })
      ).subscribe(() => {
        this.shouldPlayersBeUpdated = true;
        this.listenStatus();
        if (this.promotion) {
          this.promotion = { ...this.promotion, status };
        }
      });
    }
  }

  onLabelsChanged( labels: Label[] ) {
    this.labels = labels;
  }

  onSubmit() {
    if (!this.form.valid) {
      this.formService.formSubmitted = true;
      if (this.tabs) {
        const index = Object.values(this.form.controls).findIndex(( { invalid } ) => invalid);
        if (index !== -1) {
          this.tabs.selectedIndex = index;
        }
      }
      return;
    }

    const id = this.promotion ? this.promotion.id : undefined;
    const value = this.form.getRawValue();
    const promo: Partial<PromotionData> = this.promotion || {};

    const status = id ? {} : { status: !this.isDuplicate && this.status ? this.status : 'inactive' };

    const general = value.general || {};
    const schedule = value.schedule || {};

    const promoRewards = Array.isArray(promo.rewards) ? promo.rewards : [];
    const rewards: PromotionRewardInfo = value.rewards || { rewards: promoRewards, type: promo.type };

    if (Array.isArray(rewards.rewards) && rewards.rewards.length) {
      const reward: FreebetRewardInfo = rewards.rewards[0];
      if (Array.isArray(value.games) && value.games.length) {
        reward.games = value.games;
      } else if (Array.isArray(promo.rewards) && promo.rewards.length) {
        reward.games = promo.rewards[0].games;
      }

      if (rewards.type === 'freebet' && Array.isArray(reward.games) && reward.games.length) {
        (reward.games as FreebetGameConfig[]).map(( item: any ) => {
            item.coins.map(( coin: any ) => {
              if (this.defaultCurrency) {
                coin[this.defaultCurrency].coin = parseFloat(coin[this.defaultCurrency].coin);
              }
              return coin;
            });
            return item;
          }
        );

        if (reward.expirationDate) {
          const timezoneOffset = moment().tz(schedule.timezone || this.promotion?.timezone).format('Z');
          const expirationDate = reward.expirationDate.split('+')[0];

          reward.expirationDate = expirationDate.indexOf('.000Z') !== -1 ?
            expirationDate.replace('.000Z', timezoneOffset) :
            expirationDate + timezoneOffset;
        }
      }
    }

    if (!this.rewardsEnabled) {
      delete rewards.rewards;
    }

    if (schedule.activateInstantly) {
      delete schedule.startDate;

      status.status = 'active';
    }

    delete schedule.activateInstantly;

    const data: Partial<PromotionData> = {
      id,
      title: promo.title,
      startDate: promo.startDate,
      endDate: promo.endDate,
      labels: promo.labels,

      ...status,
      ...general,
      ...schedule,
      ...rewards
    };

    const path = this.brandPath;

    const updatePlayers = ( promoId: string ) => {
      return this.segmentation.value.length
        ? this.promotionPlayersService.update(promoId, path, this.segmentation.value)
        : of(null);
    };

    this.service.modify(data, path)
      .pipe(
        switchMap(( promotion: Promotion ) => updatePlayers(promotion.id)
          .pipe(
            mapTo(promotion),
          )),
        switchMap(( promotion: Promotion ) => {
          const promotionLabels = Array.isArray(this.labels) && this.labels.length ?
            this.labels.map(( label: Label ) => {
              return { id: label.id };
            }) : [];

          return this.promotionLabelsService.updatePromotionLabels(path, promotion.id, promotionLabels)
            .pipe(
              map((( labels: Label[] ) => ({ result: promotion, promoLabels: labels }))),
            );
        }),
        take(1),
        finalize(() => this.segmentation.patchValue([])),
      )
      .subscribe(( { result, promoLabels } ) => {
        this.onLabelsChanged(promoLabels);

        if (!data.id) {
          path ?
            this.router.navigate(['./pages/promotion/edit', result.id, 'path', path]) :
            this.router.navigate(['./pages/promotion/edit', result.id]);
        } else {
          this.state = result.state || 'new';
          this.status = result.status;
          if (!this.rewardsEnabled) {
            this.rewardsControl.disable();
          }
        }
      });
  }

  onSwitchToOperatorsTab() {
    if (this.tabs) {
      this.tabs.selectedIndex = 4;
    }
  }

  private get generalControl(): FormControl {
    return this.form.get('general') as FormControl;
  }

  private get rewardsEnabled(): boolean {
    return (['in_progress', 'expired', 'finished'].indexOf(this.state) === -1
      && (this.state === 'pending' && this.status !== 'active')) || this.state === 'new';
  }

  private get gamesEnabled(): boolean {
    return (['in_progress', 'expired', 'finished'].indexOf(this.state) === -1
      && (this.state === 'pending' && this.status !== 'active')) || this.state === 'new';
  }
}
