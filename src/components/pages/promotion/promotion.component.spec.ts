import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { MockPromotionService, PromotionService } from '../../../common/services/promotion.service';
import { PromotionComponent } from './promotion.component';
import { MODULES } from './promotion.module';
import { MatIconRegistry } from '@angular/material/icon';
import { FakeMatIconRegistry } from '@angular/material/icon/testing';


describe('PromotionComponent', () => {
  let component: PromotionComponent;
  let fixture: ComponentFixture<PromotionComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        PromotionComponent,
      ],
      imports: [
        NoopAnimationsModule,
        RouterTestingModule,
        ...MODULES,
      ],
      providers: [
        { provide: PromotionService, useClass: MockPromotionService },
        { provide: MatIconRegistry, useClass: FakeMatIconRegistry }
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PromotionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
