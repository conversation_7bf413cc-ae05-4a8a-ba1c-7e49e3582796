import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import {
  SettingsService,
  SwBrowserTitleService, SwDexieService,
  SwHubConfigService,
  SwuiGridModule,
  SwuiNotificationsModule,
  SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import { of } from 'rxjs';
import { EntityService } from '../../../../common/services/entity.service';
import { MockPromotionService, PromotionService } from '../../../../common/services/promotion.service';
import { PromotionListComponent } from './promotion-list.component';
import { MODULES } from './promotion-list.module';


describe('PromotionListComponent', () => {
  let component: PromotionListComponent;
  let fixture: ComponentFixture<PromotionListComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        PromotionListComponent,
      ],
      imports: [
        NoopAnimationsModule,
        RouterTestingModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        SwuiGridModule.forRoot(),
        SwuiNotificationsModule.forRoot(),
        ...MODULES,
      ],
      providers: [
        SwHubConfigService,
        { provide: SwBrowserTitleService, useClass: SwBrowserTitleService },
        { provide: PromotionService, useClass: MockPromotionService },
        { provide: SwuiTopFilterDataService, useValue: {} },
        SettingsService,
        {
          provide: EntityService, useValue: {
            structure$: of([])
          }
        },
        {
          provide: SwDexieService, useValue: {
            getFilterState() {
              return Promise.resolve({});
            }
          }
        }
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PromotionListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
