import { Schema<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SwuiGridField } from '@skywind-group/lib-swui';
import { REPORT_ID } from '../../../../app.constants';
import { formatDate } from '../../../../common/lib/format-date';
import { Promotion, PromotionData } from '../../../../common/models/promotion';

export const PROMOTION_STATE: { [key: string]: { title: string; class: string, id: string } } = {
  new: { id: 'new', title: 'PROMOTION.STATE.new', class: 'sw-chip sw-chip-blue' },
  pending: { id: 'pending', title: 'PROMOTION.STATE.pending', class: 'sw-chip sw-chip-blue' },
  'alt_pending': { id: 'alt_pending', title: 'PROMOTION.STATE.new', class: 'sw-chip sw-chip-blue' },

  inProgress: { id: 'inProgress', title: 'PROMOTION.STATE.inProgress', class: 'sw-chip sw-chip-green' },
  'in_progress': { id: 'in_progress', title: 'PROMOTION.STATE.inProgress', class: 'sw-chip sw-chip-green' },
  'alt_in_progress': {
    id: 'alt_in_progress',
    title: 'PROMOTION.STATE.alt_in_progress',
    class: 'sw-chip sw-chip-green'
  },

  finished: { id: 'finished', title: 'PROMOTION.STATE.finished', class: 'sw-chip' },
  'alt_finished': { id: 'alt_finished', title: 'PROMOTION.STATE.alt_finished', class: 'sw-chip' },
  expired: { id: 'expired', title: 'PROMOTION.STATE.expired', class: 'sw-chip' }
};

export const PROMOTION_STATUS: { title: string, id: string }[] = [
  { title: 'Yes', id: 'active' },
  { title: 'No', id: 'inactive' },
];

export const SCHEMA: SwuiGridField[] = [
  {
    field: 'title',
    title: 'PROMOTION.GRID.name',
    type: 'string',
    isList: true,
    isListVisible: true,
    isSortable: true,
    isFilterable: true,
    td: {
      type: 'link',
      titleFn: ( { title }: Promotion ) => title,
      isDisabled: ( { type }: Promotion ) => type !== 'freebet',
      linkFn: ( { id, brandPath }: Promotion ) => {
        const link = ['./pages/promotion/edit', id];
        if ((brandPath && brandPath !== '' && brandPath !== ':')) {
          return [...link, 'path', brandPath];
        }
        return link;
      }
    },
    filterMatch: SchemaFilterMatchEnum.Contains
  },
  {
    field: 'id',
    title: 'PROMOTION.GRID.id',
    type: 'string',
    isList: true,
    isListVisible: false,
    isSortable: false,
    isFilterable: true
  },
  {
    field: 'type',
    title: 'PROMOTION.GRID.type',
    type: 'select',
    td: {
      type: 'calc',
      titleFn: ( { type }: Promotion ) => `PROMOTION.TYPES.${type}`
    },
    isList: true,
    isListVisible: true,
    isSortable: false,
    isFilterable: false,
  },
  {
    field: 'state',
    title: 'PROMOTION.GRID.state',
    type: 'select',
    isList: true,
    isListVisible: true,
    isSortable: false,
    isFilterable: true,
    td: {
      type: 'calc',
      titleFn: ( { state }: Promotion ) => {
        const value = state && PROMOTION_STATE[state];
        return value && value.title;
      },
      classFn: ( { state }: Promotion ) => {
        const value = state && PROMOTION_STATE[state];
        return value && value.class;
      }
    },
    emptyOption: {
      show: true,
      placeholder: '- All -'
    },
    alignment: {
      td: 'center',
      th: 'center'
    },
    data: Object.values(PROMOTION_STATE).filter(( { id } ) => ['pending', 'in_progress', 'finished', 'expired'].includes(id)),
  },
  {
    field: 'status',
    title: 'PROMOTION.GRID.status',
    type: 'select',
    isList: true,
    isListVisible: true,
    isSortable: false,
    isFilterable: true,
    td: {
      type: 'inactivity',
      valueFn: ( row: any, schema: SwuiGridField ) => row[schema.field] === 'active',
    },
    alignment: {
      td: 'center',
      th: 'center'
    },
    data: PROMOTION_STATUS
  },
  {
    field: 'startDate',
    title: 'PROMOTION.GRID.startDate',
    type: 'datetimerange',
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    },
    td: {
      type: 'calc',
      titleFn: ( row: Promotion ) => {
        return formatDate(row.startDate, 'DD.MM.YYYY HH:mm:ss', row.timezone, true, true);
      },
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
    isList: true,
    isListVisible: true,
    isSortable: true,
    sortStartFrom: 'desc',
    isFilterable: true,
  },
  {
    field: 'endDate',
    title: 'PROMOTION.GRID.endDate',
    type: 'datetimerange',
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    },
    td: {
      type: 'calc',
      titleFn: ( row: Promotion ) => {
        return formatDate(row.endDate, 'DD.MM.YYYY HH:mm:ss', row.timezone, true, true);
      },
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
    isList: true,
    isListVisible: true,
    isSortable: true,
    sortStartFrom: 'desc',
    isFilterable: false,
  },
  {
    field: 'createdAt',
    title: 'PROMOTION.GRID.created',
    type: 'datetimerange',
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    },
    td: {
      type: 'calc',
      titleFn: ( row: Promotion ) => {
        return formatDate(row.createdAt, 'DD.MM.YYYY HH:mm:ss', row.timezone, true, true);
      },
      classFn: () => 'table-date',
      useTranslate: false
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
    isList: true,
    isListVisible: false,
    isSortable: true,
    isFilterable: false,
  },
  {
    field: 'path',
    title: 'PROMOTION.GRID.path',
    type: 'select',
    td: {
      type: 'calc',
      titleFn: ( { brandPath }: Promotion ) => {
        let operatorsPath: string[] | string = brandPath ? brandPath.split(':') : '-';
        if (Array.isArray(operatorsPath)) {
          const operators: string[] = operatorsPath.filter(operator => !!operator);
          operatorsPath = operators[operators.length - 1];
        }
        return operatorsPath;
      }
    },
    isList: true,
    isListVisible: true,
    isSortable: false,
    isFilterable: true,
    search: {
      placeholder: 'Search',
      show: true
    },
    emptyOption: {
      show: true,
      placeholder: 'MASTER - All'
    }
  },
  {
    field: 'totalParticipated',
    title: 'PROMOTION.GRID.players',
    type: 'string',
    isList: true,
    isListVisible: true,
    isSortable: false,
    isFilterable: false,
    alignment: {
      td: 'right',
      th: 'right'
    }
  },
  {
    field: 'externalId',
    title: 'PROMOTION.GRID.externalId',
    type: 'string',
    isList: false,
    isListVisible: true,
    isSortable: false,
    isFilterable: false,
  },
  {
    field: 'games',
    title: 'PROMOTION.GRID.games',
    type: 'string',
    isList: true,
    isListVisible: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      titleFn: ( row: PromotionData ) => {
        if (Array.isArray(row.rewards)) {
          const reward = row.rewards[0];
          if (reward && Array.isArray(reward.games)) {
            return reward.games.length.toString();
          }
        }
        return undefined;
      },
      classFn: () => {
      },
      useTranslate: false
    },
    alignment: {
      td: 'right',
      th: 'right'
    }
  },
  {
    field: 'reports',
    title: 'PROMOTION.GRID.reports',
    type: 'string',
    isList: false,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'report',
      svgIcon: 'icon_report',
      titleFn: () => 'Promotion results',
      urlParamsFn: () => REPORT_ID.promotion,
      canActivateFn: ( row: PromotionData ) => row.state !== 'pending',
      classFn: ( row: PromotionData ) => row.state !== 'pending' ? 'sw-color-blue' : 'sw-color-gray report-disabled-cursor'
    },
    alignment: {
      th: 'center',
      td: 'center'
    },
  },
];

export const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);
