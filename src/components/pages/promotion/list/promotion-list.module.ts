import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule, } from '@skywind-group/lib-swui';
import { PromotionListComponent } from './promotion-list.component';


export const MODULES = [
  SwuiPagePanelModule,
  SwuiGridModule,
  MatIconModule,
  MatTooltipModule,
  MatButtonModule,
  SwuiSchemaTopFilterModule
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule.forChild(),
    ...MODULES,
  ],
  declarations: [PromotionListComponent]
})
export class PromotionListModule {
}
