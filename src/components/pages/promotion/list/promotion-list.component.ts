import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Child } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
  PanelAction,
  RowAction,
  SchemaTopFilterField,
  SelectInputOptionData, SwBrowserTitleService,
  SwuiGridComponent,
  SwuiGridDataService,
  SwuiGridField,
  SwuiTopFilterDataService,
} from '@skywind-group/lib-swui';
import { map } from 'rxjs/operators';

import { BaseComponent } from '../../../../common/components/base.component';
import { entitiesStructureToSelectOptions, isResellerType } from '../../../../common/models/entity';
import { Promotion } from '../../../../common/models/promotion';
import { EntityService } from '../../../../common/services/entity.service';
import { PromotionService } from '../../../../common/services/promotion.service';
import { SCHEMA, SCHEMA_FILTER } from './schema';


@Component({
  selector: 'sw-promotion-list',
  templateUrl: './promotion-list.component.html',
  styleUrls: ['./promotion-list.component.scss'],
  providers: [
    SwuiTopFilterDataService,
    { provide: SwuiGridDataService, useExisting: PromotionService }
  ]
})
export class PromotionListComponent extends BaseComponent implements OnDestroy {
  readonly schema = SCHEMA.filter(( { isList } ) => isList);
  readonly panelActions: PanelAction[] = [{
    title: 'PROMOTION.LIST.newPromotion',
    icon: 'add',
    color: 'primary',
    actionUrl: '/pages/promotion/create'
  }];
  readonly rowActions: RowAction[];
  readonly filterSchema: SchemaTopFilterField[];

  @ViewChild('promotionGrid') grid?: SwuiGridComponent<Promotion>;

  constructor( router: Router,
               { snapshot: { data: { brief } } }: ActivatedRoute,
               private readonly entityService: EntityService,
               private readonly promotionService: PromotionService,
               protected readonly browserTitleService: SwBrowserTitleService,
  ) {
    super(browserTitleService);
    this.filterSchema = this.initOperatorsFilterSchema(isResellerType(brief));
    this.rowActions = [
      {
        title: 'COMMON.ACTIONS.duplicate',
        fn: ( { id, brandPath }: Promotion ) => {
          let link = ['./pages/promotion/clone', id];
          if ((brandPath && brandPath !== '' && brandPath !== ':')) {
            link = [...link, 'path', brandPath];
          }
          router.navigate(link);
        },
        inMenu: true,
        canActivateFn: () => true,
        availableFn: ({type}: Promotion) => type === 'freebet',
      }
    ];
  }

  downloadCsv() {
    this.promotionService.downloadCsv();
  }

  private initOperatorsFilterSchema( isEntity: boolean ): SwuiGridField[] {
    if (isEntity) {
      return SCHEMA_FILTER.map(item => {
        if (item.field === 'path') {
          (item as SelectInputOptionData).data = this.entityService.structure$.pipe(
            map(entity => entity ? entitiesStructureToSelectOptions(entity, 0, [], true) : []),
          );
        }
        return item;
      });
    }
    return SCHEMA_FILTER.filter(( { field } ) => field !== 'path');
  }
}
