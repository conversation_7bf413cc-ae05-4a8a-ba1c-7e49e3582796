import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PERMISSIONS_NAMES, SwHubAuthGuard } from '@skywind-group/lib-swui';
import { PagesComponent } from './pages.component';

const routes: Routes = [
  {
    path: '',
    component: PagesComponent,
    canActivate: [SwHubAuthGuard],
    canActivateChild: [SwHubAuthGuard],
    children: [
      {
        path: 'tournament',
        loadChildren: () => import('./tournament/tournament.module').then(m => m.TournamentModule),
        data: {
          permissions: [PERMISSIONS_NAMES.HUB_ENGAGEMENT_TOURNAMENTS],
        },
      },
      {
        path: 'jackpot',
        loadChildren: () => import('./jackpot/jackpot.module').then(m => m.JackpotModule),
        data: {
          permissions: [PERMISSIONS_NAMES.HUB_ENGAGEMENT_MUST_WIN_JACKPOTS],
        },
      },
      {
        path: 'promotion',
        loadChildren: () => import('./promotion/promotion.module').then(m => m.PromotionModule),
        data: {
          permissions: [PERMISSIONS_NAMES.GRANTED_ALL],
        },
      },
      {
        path: 'prize-drop',
        loadChildren: () => import('./prize-drop/prize-drop.module').then(m => m.PrizeDropModule),
        data: {
          permissions: [PERMISSIONS_NAMES.HUB_ENGAGEMENT_PRIZE_DROPS],
        },
      },
      {
        path: '404',
        loadChildren: () => import('./access-denied/access-denied.module').then(m => m.AccessDeniedModule),
        data: {
          permissions: [PERMISSIONS_NAMES.GRANTED_ALL],
        },
      }
    ]
  },
];

@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [RouterModule]
})
export class PagesRoutingModule {
}
