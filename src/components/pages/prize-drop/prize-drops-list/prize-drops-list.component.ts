import { Component, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  ActionConfirmDialogComponent,
  PanelAction,
  RowAction,
  SchemaTopFilterField,
  SelectInputOptionData,
  SwBrowserTitleService,
  SwHubAuthService,
  SwHubConfigService,
  SwuiGridComponent,
  SwuiGridDataService,
  SwuiGridField,
  SwuiNotificationsService,
  SwuiTopFilterDataService,
} from '@skywind-group/lib-swui';
import { filter, finalize, map, switchMap, takeUntil, tap } from 'rxjs/operators';

import { BaseComponent } from '../../../../common/components/base.component';
import { entitiesToSelectOptions, Entity, isBrandType, isResellerType } from '../../../../common/models/entity';
import { EntityService } from '../../../../common/services/entity.service';
import { GameService } from '../../../../common/services/game.service';
import { PrizeDropService } from '../../../../common/services/prize-drop.service';
import { PrizeDrop } from '../../interfaces/prize-drop';
import { SCHEMA_FILTER, SCHEMA_LIST } from './schema';

@Component({
  selector: 'sw-prize-drops-list',
  templateUrl: './prize-drops-list.component.html',
  styleUrls: ['./prize-drops-list.component.scss'],
  providers: [
    SwuiTopFilterDataService,
    { provide: SwuiGridDataService, useExisting: PrizeDropService }
  ]
})
export class PrizeDropsListComponent extends BaseComponent {
  readonly schema = SCHEMA_LIST;
  readonly filterSchema: SchemaTopFilterField[];
  readonly panelActions: PanelAction[];
  readonly rowActions: RowAction[];

  allowDeleteRunningFeature = true;

  @ViewChild('grid') grid?: SwuiGridComponent<PrizeDrop>;
  private isSuperAdmin: boolean;
  private isBrand: boolean;
  private brief: Entity;

  constructor( private readonly service: PrizeDropService,
               private readonly router: Router,
               private readonly translate: TranslateService,
               private readonly dialog: MatDialog,
               private readonly notifications: SwuiNotificationsService,
               private readonly entityService: EntityService,
               private readonly gameService: GameService,
               { snapshot: { data: { brief } } }: ActivatedRoute,
               { isSuperAdmin }: SwHubAuthService,
               { envName }: SwHubConfigService,
               protected readonly browserTitleService: SwBrowserTitleService,
  ) {
    super(browserTitleService);
    this.isBrand = isBrandType(brief);
    this.panelActions = isBrandType(brief) ? [] : this.initPanelActions();
    this.rowActions = this.initRowActions();
    this.filterSchema = this.initFilterSchema(isResellerType(brief));
    this.isSuperAdmin = isSuperAdmin;
    this.allowDeleteRunningFeature = envName !== 'prod';
    this.brief = brief;
  }

  private isOwner( prizeDrop: PrizeDrop ): boolean {
    return this.isSuperAdmin || this.brief.id === prizeDrop.operators.owner?.id;
  }

  private initPanelActions(): PanelAction[] {
    return [
      {
        title: 'PRIZE_DROP.LIST.newPrizeDrop',
        icon: 'add',
        color: 'primary',
        actionUrl: '/pages/prize-drop/create'
      }
    ];
  }

  private initRowActions(): RowAction[] {
    return [
      {
        title: 'COMMON.ACTIONS.viewEdit',
        fn: ( item: PrizeDrop ) => {
          this.router.navigate(['./pages/prize-drop/edit', item.id]);
        },
        canActivateFn: () => true,
      },
      {
        title: 'COMMON.ACTIONS.duplicate',
        fn: ( item: PrizeDrop ) => {
          this.router.navigate(['./pages/prize-drop/clone', item.id]);
        },
        canActivateFn: () => !this.isBrand,
      },
      {
        title: 'COMMON.ACTIONS.delete',
        fn: ( prizeDrop: PrizeDrop ) => {
          let deleteConfirmText = 'PRIZE_DROP.NOTIFICATIONS.removePrizeDrop';
          let forceDelete = false;

          if (this.isSuperAdmin && prizeDrop.active === false) {
            deleteConfirmText = 'PRIZE_DROP.NOTIFICATIONS.forceRemove';
            forceDelete = true;
          }

          this.dialog.open(ActionConfirmDialogComponent, {
            width: '350px',
            data: { action: { confirmText: this.translate.instant(deleteConfirmText, { name: prizeDrop.general.name }) } }
          }).afterClosed()
            .pipe(
              finalize(() => this.grid && this.grid.dataSource.loadData()),
              filter(( { confirmed } ) => confirmed),
              switchMap(() => forceDelete ? this.service.forceDelete(prizeDrop.id) : this.service.delete(prizeDrop.id)),
              switchMap(() => this.translate.get('PRIZE_DROP.NOTIFICATIONS.deleted', prizeDrop.general)),
              tap(message => this.notifications.success(message, '')),
              takeUntil(this.destroyed)
            )
            .subscribe();
        },
        canActivateFn: ( item: PrizeDrop ) =>
          this.isOwner(item) &&
          ((!item.active && item.status === 'disabled' && !this.isBrand) ||
            (!item.active && this.isSuperAdmin && this.allowDeleteRunningFeature)),
      }
    ];
  }

  private initFilterSchema( isEntity: boolean ): SwuiGridField[] {
    return this.initOperatorsFilterSchema(isEntity).map(item => {
      if (item.field === 'games') {
        (item as SelectInputOptionData).data = this.gameService.query();
      }
      return item;
    });
  }

  private initOperatorsFilterSchema( isEntity: boolean ): SwuiGridField[] {
    if (isEntity) {
      return SCHEMA_FILTER.map(item => {
        if (item.field === 'operators') {
          (item as SelectInputOptionData).data = this.entityService.structure$.pipe(
            map(entity => entity ? entitiesToSelectOptions(entity) : [])
          );
        }
        return item;
      });
    }
    return SCHEMA_FILTER.filter(( { field } ) => field !== 'operators');
  }
}

