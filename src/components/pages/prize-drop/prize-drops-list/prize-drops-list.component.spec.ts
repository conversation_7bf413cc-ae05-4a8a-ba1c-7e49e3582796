import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import {
  SettingsService, SwBrowserTitleService, SwDexieService,
  SwHubAuthService,
  SwHubConfigService,
  SwuiGridModule,
  SwuiNotificationsModule
} from '@skywind-group/lib-swui';

import { CurrencyService, MockCurrencyService } from '../../../../common/services/currency.service';
import { EntityService, MockEntityService } from '../../../../common/services/entity.service';
import { GameService, MockGameService } from '../../../../common/services/game.service';
import { MockPrizeDropService, PrizeDropService } from '../../../../common/services/prize-drop.service';
import { PrizeDropsListComponent } from './prize-drops-list.component';
import { MODULES } from './prize-drops-list.module';


describe('PrizeDropsListComponent', () => {
  let component: PrizeDropsListComponent;
  let fixture: ComponentFixture<PrizeDropsListComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        PrizeDropsListComponent,
      ],
      imports: [
        NoopAnimationsModule,
        HttpClientTestingModule,
        RouterTestingModule,
        TranslateModule.forRoot(),
        SwuiGridModule.forRoot(),
        SwuiNotificationsModule.forRoot(),
        ...MODULES
      ],
      providers: [
        SettingsService,
        SwHubAuthService,
        { provide: SwBrowserTitleService, useClass: SwBrowserTitleService },
        { provide: CurrencyService, useClass: MockCurrencyService },
        { provide: PrizeDropService, useClass: MockPrizeDropService },
        { provide: EntityService, useClass: MockEntityService },
        { provide: GameService, useClass: MockGameService },
        { provide: SwHubConfigService, useValue: {} },
        {
          provide: SwDexieService, useValue: {
            getFilterState() {
              return Promise.resolve({});
            }
          }
        }
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PrizeDropsListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
