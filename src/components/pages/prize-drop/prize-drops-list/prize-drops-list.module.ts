import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiChipsAutocompleteModule, SwuiGridModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule, } from '@skywind-group/lib-swui';

import { PrizeDropsListComponent } from './prize-drops-list.component';


export const MODULES = [
  MatCardModule,
  MatTableModule,
  MatMenuModule,
  MatIconModule,
  MatFormFieldModule,
  MatInputModule,
  MatSelectModule,
  MatButtonModule,
  MatDialogModule,
  MatSlideToggleModule,
  MatTooltipModule,
  FlexLayoutModule,
  SwuiChipsAutocompleteModule,
  SwuiPagePanelModule,
  SwuiGridModule,
  SwuiSchemaTopFilterModule,
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule.forChild(),
    ...MODULES,
  ],
  declarations: [PrizeDropsListComponent],
})
export class PrizeDropsListModule {
}
