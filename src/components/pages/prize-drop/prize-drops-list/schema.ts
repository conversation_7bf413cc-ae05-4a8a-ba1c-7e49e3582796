import { SchemaFilterMatchEnum, SwuiConstantsService, SwuiGridField } from '@skywind-group/lib-swui';

import { formatCurrency, REPORT_ID } from '../../../../app.constants';
import { formatDate } from '../../../../common/lib/format-date';
import { SchemaFieldMapItem } from '../../../../common/models/schema-field-map-item';
import { STATUS_MAP } from '../../interfaces/feature';
import { Jackpot } from '../../interfaces/jackpot';
import { PrizeDrop } from '../../interfaces/prize-drop';


function calculateTotalPayout( row: PrizeDrop ) {
  return row.configuration.payouts.reduce(( accumulator, currentValue ) => {
    const { players, payoutsPerRange, prizeType } = currentValue;
    if (players && payoutsPerRange.length && prizeType !== 'text') {
      const ppr = payoutsPerRange as number[];
      accumulator += ppr.reduce(( acc: any, curr: any ) => {
        acc += curr;
        return acc;
      }, 0) * currentValue.players;
    }
    return accumulator;
  }, 0).toString();
}


const SCHEMA: SwuiGridField[] = [
  {
    field: 'search',
    type: 'search',
    fields: 'id,name,operator,instanceId',
    title: 'Search by id, name, operator',
    isViewable: false,
    isFilterable: true,
    isSortable: false,
    filterMatch: {
      fields: SchemaFilterMatchEnum.Fields,
      text: SchemaFilterMatchEnum.Text
    },
    isFilterableAlways: true
  },
  {
    field: 'name',
    title: 'PRIZE_DROP.GRID.name',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: false,
    td: {
      type: 'link',
      titleFn: ( row: PrizeDrop ) => row.general.name,
      linkFn: ( row: PrizeDrop ) => {
        return ['./pages/prize-drop/edit', row.id];
      }
    },
    classFn: () => {
    }
  },
  {
    field: 'id',
    title: 'PRIZE_DROP.GRID.featureId',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: false,
    isListVisible: false
  },
  {
    field: 'lastExecution',
    title: 'PRIZE_DROP.GRID.lastRun',
    td: {
      type: 'calc',
      titleFn: ( row: PrizeDrop ) => {
        const timeZone = 'timeZone' in row.schedule ? row.schedule.timeZone : '';
        return formatDate(row.lastExecution, 'MMM DD, YYYY HH:mm', timeZone, false);
      },
      classFn: () => 'table-date',
      useTranslate: false
    },
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',
    type: 'datetimerange',
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    }
  },
  {
    field: 'nextExecution',
    title: 'PRIZE_DROP.GRID.nextRun',
    td: {
      type: 'calc',
      titleFn: ( row: PrizeDrop ) => {
        const timeZone = 'timeZone' in row.schedule ? row.schedule.timeZone : '';
        return formatDate(row.nextExecution, 'MMM DD, YYYY HH:mm', timeZone, false);
      },
      classFn: () => 'table-date',
      useTranslate: false
    },
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: false,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',
    type: 'datetimerange',
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    }
  },
  {
    field: 'status',
    title: 'PRIZE_DROP.GRID.status',
    type: 'select',
    td: {
      type: 'calc',
      titleFn: ( row: PrizeDrop ) => {
        const item: SchemaFieldMapItem | undefined =
          STATUS_MAP.find(( el: SchemaFieldMapItem ) => el.id === row.status);
        return item ? item.title : row.status;
      },
      classFn: ( row: PrizeDrop ) => {
        const item: SchemaFieldMapItem | undefined =
          STATUS_MAP.find(( el: SchemaFieldMapItem ) => el.id === row.status);
        return item ? item.class : 'sw-chip';
      },
      useTranslate: false
    },
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    isFilterableAlways: true,
    data: STATUS_MAP.filter(( { id } ) => ['scheduled', 'running', 'expired', 'disabled'].includes(id)),
    alignment: {
      td: 'center',
      th: 'center'
    },
    emptyOption: {
      show: true,
      placeholder: '- All -'
    },
  },
  {
    field: 'active',
    title: 'PRIZE_DROP.GRID.active',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: false,
    td: {
      type: 'inactivity',
    },
    alignment: {
      th: 'center',
      td: 'center'
    },
    isFilterable: true,
    isFilterableAlways: true,
    data: [{ id: 'true', title: 'Yes' }, { id: 'false', title: 'No' }],
  },
  {
    field: 'payouts',
    title: 'Payout',
    type: 'string',
    td: {
      type: 'calc',
      titleFn: ( row: PrizeDrop ) => {
        const payout = calculateTotalPayout(row);
        if (row.configuration && row.ranking.baseCurrency) {
          return SwuiConstantsService.currencySymbol(row.ranking.baseCurrency as string)
            + formatCurrency(payout);
        }
      },
      classFn: () => {
      },
      useTranslate: false
    },
    isList: false,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    alignment: {
      th: 'right',
      td: 'right'
    }
  },
  {
    field: 'created',
    title: 'PRIZE_DROP.GRID.created',
    type: 'datetimerange',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    },
    td: {
      type: 'calc',
      titleFn: ( row: Jackpot ) => {
        return formatDate(row.created, 'MMM DD, YYYY', '', false, false);
      },
      classFn: () => 'table-date',
      useTranslate: false
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
  },
  {
    field: 'operators',
    title: 'PRIZE_DROP.GRID.operators',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    isEditable: true,
    search: {
      placeholder: 'Search',
      show: true
    },
    emptyOption: {
      show: true,
      placeholder: 'MASTER - All'
    },
    td: {
      type: 'list',
      arrayKey: 'title',
      valueFn: ( row: PrizeDrop ) => {
        return row && row.operators.brands ? row.operators.brands : [];
      },
      useTranslate: false
    },
    data: [],
    filter: {
      title: 'FILTER.resellerOperator',
    },
    alignment: {
      td: 'left',
      th: 'center'
    }
  },
  {
    field: 'isSegmented',
    title: 'JACKPOT.GRID.playersSegmentation',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: false,
    isEditable: true,
    td: {
      type: 'calc',
      titleFn: ( row: Jackpot ) => {
        return row.isSegmented ? 'COMMON.yes' : 'COMMON.no';
      },
      useTranslate: true
    },
    alignment: {
      th: 'center',
      td: 'center'
    },
    isFilterable: true,
    data: [{ id: 'true', title: 'COMMON.yes' }, { id: 'false', title: 'COMMON.no' }],
  },
  {
    field: 'reports',
    title: 'PRIZE_DROP.GRID.reports',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'report',
      svgIcon: 'icon_report',
      titleFn: () => 'Prize drop results',
      urlParamsFn: ( row: PrizeDrop ) => row && row.id ? `${REPORT_ID.prizeDrops}/${row.id}` : '',
      canActivateFn: ( row: PrizeDrop ) => row && !!row.lastExecution,
      classFn: ( row: PrizeDrop ) => row && row.lastExecution ? 'sw-color-blue' : 'sw-color-gray report-disabled-cursor'
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  {
    field: 'games',
    title: 'PRIZE_DROP.GRID.games',
    type: 'multiselect',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    isListVisible: false,
    alignment: {
      th: 'right',
      td: 'right'
    },
    data: [],
  }
];

export const SCHEMA_LIST = SCHEMA.filter(( { isList } ) => isList);

export const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);
