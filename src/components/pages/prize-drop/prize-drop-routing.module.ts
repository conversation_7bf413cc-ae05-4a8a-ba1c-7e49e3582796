import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PERMISSIONS_NAMES } from '@skywind-group/lib-swui';
import { CurrenciesResolver } from '../../../common/services/currency.service';
import { GamesResolver } from '../../../common/services/game.service';
import { BriefResolver } from '../../../common/services/resolvers/brief.resolver';
import { PrizeDropResolver } from '../../../common/services/resolvers/prize-drop.resolver';
import { PrizeDropUpdateComponent } from './prize-drop-update/prize-drop-update.component';

import { PrizeDropComponent } from './prize-drop.component';
import { PrizeDropsListComponent } from './prize-drops-list/prize-drops-list.component';

export const routes: Routes = [
  {
    path: '',
    component: PrizeDropComponent,
    children: [
      {
        path: '',
        component: PrizeDropsListComponent,
        resolve: {
          brief: BriefResolver,
        },
        data: {
          permissions: [PERMISSIONS_NAMES.GRANTED_ALL],
          title: 'Prize Drops'
        },
      },
      {
        path: 'create',
        component: PrizeDropUpdateComponent,
        resolve: {
          currencies: CurrenciesResolver,
          games: GamesResolver,
        },
        data: {
          permissions: [PERMISSIONS_NAMES.GRANTED_ALL],
          title: 'Prize Drops - Create'
        },
      },
      {
        path: 'clone/:id',
        component: PrizeDropUpdateComponent,
        resolve: {
          currencies: CurrenciesResolver,
          prizeDrop: PrizeDropResolver,
          brief: BriefResolver,
          games: GamesResolver,
        },
        data: {
          duplicate: true,
          permissions: [PERMISSIONS_NAMES.GRANTED_ALL],
          title: 'Prize Drops - Clone'
        },
      },
      {
        path: 'edit/:id',
        component: PrizeDropUpdateComponent,
        resolve: {
          currencies: CurrenciesResolver,
          prizeDrop: PrizeDropResolver,
          brief: BriefResolver,
          games: GamesResolver,
        },
        data: {
          permissions: [PERMISSIONS_NAMES.GRANTED_ALL],
          title: 'Prize Drops - Edit'
        },
      },
    ]
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: [
    BriefResolver,
    PrizeDropResolver,
    CurrenciesResolver,
    GamesResolver
  ],
})
export class PrizeDropRoutingModule {
}
