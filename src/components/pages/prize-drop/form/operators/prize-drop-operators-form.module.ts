import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';

import { BrandsManagerModule } from '../../../../../common/components/brands-manager/brands-manager.module';
import { PrizeDropOperatorsFormComponent } from './prize-drop-operators-form.component';


@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    BrandsManagerModule,
    MatCardModule,
  ],
  exports: [
    PrizeDropOperatorsFormComponent,
  ],
  declarations: [
    PrizeDropOperatorsFormComponent,
  ],
})
export class PrizeDropOperatorsFormModule {
}
