import { Component, forwardRef, HostBinding, HostL<PERSON>ener, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ControlValueAccessor, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors } from '@angular/forms';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { FeatureOperators, SegmentationBrand } from '../../../interfaces/feature';
import { PrizeDropFormService } from '../../prize-drop-update/prize-drop-form-service/prize-drop-form.service';


@Component({
  selector: 'sw-prize-drop-operators-form',
  templateUrl: './prize-drop-operators-form.component.html',
  styleUrls: ['./prize-drop-operators-form.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PrizeDropOperatorsFormComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => PrizeDropOperatorsFormComponent),
      multi: true
    },
  ]
})
export class PrizeDropOperatorsFormComponent implements ControlValueAccessor, OnInit, OnDestroy {
  onChange: ( _: any ) => void = (() => {
  });
  disabled = false;
  isValid = true;
  submitted = false;
  operators?: FeatureOperators;
  segmentationBrands: SegmentationBrand[] = [];
  @HostBinding('attr.tabindex') tabindex = 0;

  private readonly destroyed$ = new Subject<void>();
  private fundingValid$ = new BehaviorSubject<boolean>(true);
  private value$ = new BehaviorSubject<FeatureOperators | undefined>(undefined);

  constructor( private readonly formService: PrizeDropFormService ) {
  }

  @HostListener('blur') onblur() {
    this.onTouched();
  }

  ngOnInit() {
    this.formService.formSubmitted$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe((submitted: boolean) => {
      this.submitted = submitted;
    });

    combineLatest([this.fundingValid$, this.value$]).pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( [valid, operators] ) => {
      this.operators = Object.assign({}, operators);
      this.isValid = valid && !!operators && !!operators.brands && !!operators.brands.length;
      this.onChange(operators);
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  writeValue( val: FeatureOperators ): void {
    this.value$.next(val);
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState?( isDisabled: boolean ): void {
    this.disabled = isDisabled;
  }

  validate(): ValidationErrors | null {
    return this.isValid ? null : { invalidForm: { valid: false } };
  }

  onSelectData( featureOperators: FeatureOperators ) {
    this.value$.next(featureOperators);
  }

  onValidationCheck( val: boolean ) {
    this.fundingValid$.next(val);
  }
}
