import { Component } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';

import { AbstractFormValueAccessor, formValueProviders } from '../../../../../common/lib/abstract-form-value-accessor';
import { ArrayValidator, JSONValidator } from '../../../../../common/lib/validators';


@Component({
  selector: 'sw-prize-drop-ui',
  templateUrl: './prize-drop-ui.component.html',
  providers: formValueProviders(PrizeDropUiComponent),
})
export class PrizeDropUiComponent extends AbstractFormValueAccessor<any> {
  onChange: ( _: any ) => void = (() => {
  });
  form = new FormGroup({});

  constructor( private fb: FormBuilder ) {
    super();
    this.form = this.initForm();
  }

  onBlur() {
    this.onTouched();
  }

  get assetsControl(): FormControl {
    return this.form.get('assets') as FormControl;
  }

  protected transformValue( value: any ): any {
    return { assets: value ? JSON.stringify(value, null, '\t') : [] };
  }

  protected transformForm( value: any ): any | undefined {
    let isJSON = true;
    try {
      JSON.parse(value.assets);
    } catch (e) {
      isJSON = false;
    }

    return isJSON ? JSON.parse(value.assets) : [];
  }

  private initForm(): FormGroup {
    return this.fb.group({
      assets: ['', Validators.compose([ArrayValidator, JSONValidator])]
    });
  }
}
