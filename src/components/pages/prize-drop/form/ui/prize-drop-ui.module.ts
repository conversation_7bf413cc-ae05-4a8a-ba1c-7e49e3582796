import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';

import { PrizeDropUiComponent } from './prize-drop-ui.component';


@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    TranslateModule.forChild(),
  ],
  exports: [
    PrizeDropUiComponent,
  ],
  declarations: [
    PrizeDropUiComponent,
  ],
})
export class PrizeDropUiModule {
}
