import { Component, forwardRef, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  ControlValueAccessor, FormBuilder, FormControl, FormGroup, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors,
  Validator, Validators
} from '@angular/forms';
import { SWUI_CONTROL_MESSAGES } from '@skywind-group/lib-swui';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { BaseComponent } from '../../../../../common/components/base.component';
import { playersLengthValidator } from '../../../../../common/lib/validators';
import { CurrencyModel } from '../../../../../common/models/currency.model';
import { FeatureCurrency, FeatureSegmentation } from '../../../interfaces/feature';

import { PrizeDropFormService } from '../../prize-drop-update/prize-drop-form-service/prize-drop-form.service';

@Component({
  selector: 'sw-prize-drop-segmentation-from',
  templateUrl: './prize-drop-segmentation-form.component.html',
  styleUrls: ['./prize-drop-segmentation-form.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PrizeDropSegmentationFormComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => PrizeDropSegmentationFormComponent),
      multi: true
    },
    {
      provide: SWUI_CONTROL_MESSAGES,
      useValue: {
        required: 'VALIDATION.shouldNotBeEmpty',
        positiveNumbers: 'VALIDATION.positiveNumbers',
        numbersOnly: 'VALIDATION.invalidNumbersOnly',
      }
    },
  ]
})
export class PrizeDropSegmentationFormComponent extends BaseComponent implements OnDestroy, OnInit,
  ControlValueAccessor, Validator {

  @Input() currencies: CurrencyModel[] = [];

  @Input()
  set canPlayersEdit( val: boolean ) {
    this._canPlayersEdit.next(val);
  }

  @Input()
  set status( val: string | undefined ) {
    this._status.next(val);
  }

  @Input() initialCurrencies?: FeatureCurrency[];
  @Input() isDuplicate = false;

  importDescription = 'PRIZE_DROP.FORM.SEGMENTATION.importDescription';

  form: FormGroup = new FormGroup({});
  baseCurrency?: string;
  onChange: ( _: any ) => void = (() => {
  });

  private _canPlayersEdit = new BehaviorSubject<boolean>(true);
  private _status = new BehaviorSubject<string | undefined>(undefined);
  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly formService: PrizeDropFormService,
               private readonly fb: FormBuilder,
  ) {
    super();
    this.initForm();
  }

  ngOnInit() {
    combineLatest([this._canPlayersEdit, this._status])
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe(( [canEdit, status] ) => {
        status === 'expired' || !canEdit ? this.brandsControl.disable() : this.brandsControl.enable();
      });

    this.formService.baseCurrency$
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: string ) => {
        this.baseCurrency = val || '';
      });

    this.form.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(
        () => {
          const formValue = this.form.getRawValue();
          this.onChange(
            {
              currencies: formValue['currencies'],
              brands: formValue['brands'],
              restrictedCountries: formValue['restrictedCountries'],
            }
          );
        });
  }

  get currenciesControl(): FormControl {
    return this.form.get('currencies') as FormControl;
  }

  get brandsControl(): FormControl {
    return this.form.get('brands') as FormControl;
  }

  get restrictedCountriesControl(): FormControl {
    return this.form.get('restrictedCountries') as FormControl;
  }

  initForm() {
    this.form = this.fb.group({
      currencies: [null, Validators.required],
      brands: [[], playersLengthValidator],
      restrictedCountries: []
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  writeValue( val: FeatureSegmentation ): void {
    if (!val) {
      return;
    }
    this.form.patchValue(val);
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState?( disabled: boolean ): void {
    disabled ? this.currenciesControl.disable() : this.currenciesControl.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid || this.form.status === 'DISABLED' ? null : { invalidForm: { valid: false } };
  }
}
