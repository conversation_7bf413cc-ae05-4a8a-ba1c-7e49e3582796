.segments {
  display: flex;
  width: 100%;

  &__item {
    position: relative;
    display: block;
    width: calc(50% - 43px);

    &--middle {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 86px;
      padding: 0 20px;
      box-sizing: border-box;
    }
  }

  &__button {
    width: 46px;
    min-width: 46px;
    margin-top: 20px;
    padding: 0;
    text-transform: uppercase;
  }

  &__btn-icon {
    font-size: 18px;
    height: 18px;
    width: 18px;
    margin-bottom: 1px;
  }
}

.error-message {
  padding: 20px;
  border-radius: 4px;
  background-color: #ff8a80;
  margin-bottom: 20px;
}

.sw-grid-layout {
  display: flex;

  &__filter {
    flex-shrink: 0;
    flex-grow: 0;
    width: 320px;
    padding-left: 32px;
  }

  &__table {
    flex-grow: 1;
    width: calc(100% - 320px)
  }

  @media (max-width: 1024px) {
    flex-direction: column;
    &__filter {
      width: 100%;
      padding-left: 0;
      padding-top: 32px;
    }
    &__table {
      width: 100%;
    }
  }
}

.currencies-error {
  font-size: 12px;
  margin-left: 6px;
  margin-top: -16px;
  color: #f44336;
}
