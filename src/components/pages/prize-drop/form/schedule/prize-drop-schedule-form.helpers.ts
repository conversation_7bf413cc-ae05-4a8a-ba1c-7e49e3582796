import { CustomPeriod } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import 'moment-timezone';

import { FeatureSchedule } from '../../../interfaces/feature';

const MIN_TIME = { 'hour': 0, 'minute': 0, 'second': 0, 'millisecond': 0 };

export enum RepetitionMode {
  Interval = 1,
  Dates
}

export interface ScheduleForm {
  duration?: number | null;
  timeZone?: string | null;
  repetitionMode: RepetitionMode;
  triggerAt?: number[];
  specificDates?: (moment.Moment | string | null)[];
  interval?: {
    range: {
      from: moment.Moment | string | null;
      to: moment.Moment | string | null;
    };
    daysOfWeek: number[];
  };
}

export function processScheduleForm( value: ScheduleForm ): Partial<FeatureSchedule> {
  const { interval, duration, timeZone, specificDates, triggerAt, repetitionMode } = value;
  const data: Partial<FeatureSchedule> = {
    ...(duration ? { duration } : {}),
    ...(timeZone ? { timeZone } : {}),
    repetitionMode,
    triggerAt: []
  };
  if (Array.isArray(triggerAt)) {
    data.triggerAt = triggerAt.filter(time => time !== null);
  }
  if (timeZone) {
    if (repetitionMode === RepetitionMode.Dates && Array.isArray(specificDates)) {
      data.specificDates = specificDates
        .reduce<string[]>(( result, date ) => {
          if (date === null) {
            return result;
          }
          const ts = moment.isMoment(date) ? date : moment.utc(date);
          return [...result, ts.clone().set(MIN_TIME).toISOString()];
        }, []);
    } else if (repetitionMode === RepetitionMode.Interval && interval) {
      const { daysOfWeek, range } = interval;
      if (range && range.from && range.to && daysOfWeek) {
        const from = moment.isMoment(range.from) ? range.from : moment.utc(range.from);
        const to = moment.isMoment(range.to) ? range.to : moment.utc(range.to);
        data.interval = {
          validFrom: from.clone().set(MIN_TIME).toISOString(),
          validTo: to.clone().set(MIN_TIME).toISOString(),
          daysOfWeek
        };
      }
    }
  }
  return data;
}

export const CUSTOM_PERIODS: CustomPeriod[] = [
  {
    title: 'COMPONENTS.DATE_RANGE.next7Days',
    fn: () => {
      const to = moment.utc().add(7, 'd').set({hour: 0, minute: 0, second: 0, millisecond: 0});
      const from = to.clone().subtract(7, 'd');
      return { from: from.toISOString(), to: to.toISOString()};
    },
  },
  {
    title: 'COMPONENTS.DATE_RANGE.next14Days',
    fn: () => {
      const to = moment.utc().add(14, 'd').set({hour: 0, minute: 0, second: 0, millisecond: 0});
      const from = to.clone().subtract(14, 'd');
      return { from: from.toISOString(), to: to.toISOString()};
    },
  },
  {
    title: 'COMPONENTS.DATE_RANGE.next30Days',
    fn: () => {
      const to = moment.utc().add(30, 'd').set({hour: 0, minute: 0, second: 0, millisecond: 0});
      const from = to.clone().subtract(30, 'd');
      return { from: from.toISOString(), to: to.toISOString()};
    },
  },
  {
    title: 'COMPONENTS.DATE_RANGE.next90Days',
    fn: () => {
      const to = moment.utc().add(90, 'd').set({hour: 0, minute: 0, second: 0, millisecond: 0});
      const from = to.clone().subtract(90, 'd');
      return { from: from.toISOString(), to: to.toISOString()};
    },
  },
  {
    title: 'COMPONENTS.DATE_RANGE.thisMonth',
    fn: () => {
      const from = moment.utc().startOf('month');
      const to = from.clone().add(1, 'month');
      return { from: from.toISOString(), to: to.toISOString()};
    },
  },
  {
    title: 'COMPONENTS.DATE_RANGE.nextMonth',
    fn: () => {
      const from = moment.utc().add(1, 'month').startOf('month');
      const to = from.clone().add(1, 'month');
      return { from: from.toISOString(), to: to.toISOString()};
    },
  }
];
