import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import {
  SwuiDatePickerModule, SwuiDateRangeModule, SwuiDateTimeRangeModule, SwuiIsControlInvalidModule, SwuiSelectModule, SwuiStartTimeModule,
  SwuiTimeDurationModule
} from '@skywind-group/lib-swui';
import { PipesModule } from '../../../../../common/pipes/pipes.module';
import {
  PrizeDropScheduleDatesConflictComponent
} from './prize-drop-schedule-dates-conflict/prize-drop-schedule-dates-conflict.component';

import { PrizeDropScheduleFormComponent } from './prize-drop-schedule-form.component';
import { PrizeDropScheduleWeekModule } from './prize-drop-schedule-week/prize-drop-schedule-week.module';

export const MODULES = [
  ReactiveFormsModule,
  MatSelectModule,
  MatFormFieldModule,
  MatRadioModule,
  MatButtonModule,
  MatIconModule,
  MatCheckboxModule,
  MatInputModule,
  MatTooltipModule,
  FlexLayoutModule,
  SwuiDatePickerModule,
  SwuiTimeDurationModule,
  SwuiDateTimeRangeModule,
  SwuiStartTimeModule,
  SwuiSelectModule,
  PrizeDropScheduleWeekModule,
  SwuiDateRangeModule,
  PipesModule,
  SwuiIsControlInvalidModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...MODULES,
  ],
  exports: [
    PrizeDropScheduleFormComponent,
  ],
  declarations: [
    PrizeDropScheduleFormComponent,
    PrizeDropScheduleDatesConflictComponent,
  ],
})
export class PrizeDropScheduleFormModule {
}
