import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PrizeDropScheduleDatesConflictComponent } from './prize-drop-schedule-dates-conflict.component';

describe('PrizeDropScheduleDatesConflictComponent', () => {
  let component: PrizeDropScheduleDatesConflictComponent;
  let fixture: ComponentFixture<PrizeDropScheduleDatesConflictComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [PrizeDropScheduleDatesConflictComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PrizeDropScheduleDatesConflictComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
