<form class="vertical-form" [formGroup]="form">

  <ng-container *swIsControlInvalid="form">
    <sw-prize-drop-schedule-dates-conflict
      *ngIf="form.hasError('scheduleValid')"
      [timezone]="currentTimezone"
      [dates]="form.errors['scheduleValid'].dates">
    </sw-prize-drop-schedule-dates-conflict>
    <div class="error-message margin-bottom20" *ngIf="form.hasError('inFuture')">
      Date should be in future
    </div>
  </ng-container>

  <div class="mat-card mat-elevation-z0 margin-bottom20">
    <div class="card-header">
      <div class="card-header__title">
        <h3 class="no-margin-top mat-title">Prize Drop Duration</h3>
      </div>
      <div class="card-header__icon">
        <mat-icon
          class="help-icon"
          svgIcon="question_mark"
          matTooltip="{{ 'PRIZE_DROP.TOOLTIP.duration' | translate }}">
        </mat-icon>
      </div>
    </div>
    <div class="margin-bottom8">
      Set the prize drop duration (how long each individual prize drop will run for) and time zone.
    </div>
    <div fxLayout="row" class="duration">

      <mat-form-field appearance="outline" class="duration__time">
        <mat-label>Duration</mat-label>
        <lib-swui-time-duration
          formControlName="duration"
          [secondsDisabled]="true"
          required>
        </lib-swui-time-duration>
        <mat-hint class="duration-hint">
          <span class="duration-hint__item">days</span>
          <span class="duration-hint__item">hours</span>
          <span class="duration-hint__item duration-hint__item--minutes">minutes</span>
        </mat-hint>
        <mat-error>Field is required</mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="duration__timezone">
        <mat-label>Time zone</mat-label>
        <lib-swui-select
          required
          [formControl]="timeZone"
          [data]="timezones"
          [showSearch]="true"
          [disableEmptyOption]="true"></lib-swui-select>
        <mat-error>Field is required</mat-error>
      </mat-form-field>

    </div>
  </div>

  <div class="mat-card mat-elevation-z0 margin-bottom20">
    <div class="card-header">
      <div class="card-header__title">
        <h3 class="no-margin-top mat-title">Start Time</h3>
      </div>
      <div class="card-header__icon">
        <mat-icon
          class="help-icon"
          svgIcon="question_mark"
          matTooltip="{{ 'PRIZE_DROP.TOOLTIP.startTime' | translate }}">
        </mat-icon>
      </div>
    </div>
    <div class="margin-bottom32">
      Set the time of day when the prize drops should start. The prize drop can run multiple times a day provided the
      difference between start times is more than the prize drop duration.
    </div>

    <ul class="dates-radio__list dates-list">

      <li class="dates-list__item" *ngFor="let time of triggerAt.controls; let i = index">
        <mat-form-field appearance="outline" class="dates-list__time">
          <mat-label>Start time</mat-label>
          <lib-swui-start-time
            required
            [formControl]="time"
            [disableSeconds]="true">
          </lib-swui-start-time>
          <mat-error *ngIf="time.hasError('required')">Field is required</mat-error>
          <mat-error *ngIf="time.hasError('startTimeDuplicate')">Start time already in use</mat-error>
        </mat-form-field>

        <button
          *ngIf="triggerAt?.controls.length > 1 && !time.disabled"
          class="dates-list__delete"
          mat-icon-button
          (click)="removeStartTime(i)"
          [disabled]="isDisabled">
          <mat-icon>clear</mat-icon>
        </button>
      </li>

      <li class="dates-list__item" *ngIf="isAddStarTimeAvailable">
        <button (click)="addStartTime()" mat-button class="dates-list__add mat-button-md" [color]="'primary'" [disabled]="isDisabled">
          <mat-icon>add</mat-icon>
          Add start time
        </button>
      </li>

    </ul>

  </div>

  <div class="mat-card mat-elevation-z0">
    <div class="card-header">
      <div class="card-header__title">
        <h3 class="no-margin-top mat-title">Date schedule</h3>
      </div>
      <div class="card-header__icon">
        <mat-icon
          class="help-icon"
          svgIcon="question_mark"
          matTooltip="{{ 'PRIZE_DROP.TOOLTIP.dateSchedule' | translate }}">
        </mat-icon>
      </div>
    </div>
    <div class="dates-section">
      <div class="dates-section__left">
        <mat-radio-group formControlName="repetitionMode" class="dates-radio">

          <div class="dates-radio__item">

            <mat-radio-button [value]="1" class="dates-radio__button">
              {{ 'PRIZE_DROP.TOOLTIP.dailySchedule' | translate }}
            </mat-radio-button>

            <div class="dates-radio__content" [formGroup]="interval"
                 [class.hidden]="form.get('repetitionMode')?.value !== 1">
              <div class="dates-radio__hint margin-bottom8">
                {{ 'PRIZE_DROP.TOOLTIP.descriptionDailySchedule' | translate }}
              </div>
              <mat-form-field appearance="outline" class="validity-dates">
                <mat-label>Validity dates</mat-label>
                <mat-icon matPrefix style="margin-right: 6px">date_range</mat-icon>
                <lib-swui-date-range
                  required
                  formControlName="range"
                  [customPeriods]="customPeriods"
                  [minDate]="minCalendarDate">
                </lib-swui-date-range>
                <mat-error *ngIf="range.hasError('required')">"From" and "To" dates are required</mat-error>
              </mat-form-field>

              <div class="margin-bottom8 font-weight500">On Days of the Week</div>

              <sw-prize-drop-schedule-week [formControlName]="'daysOfWeek'"></sw-prize-drop-schedule-week>
              <ng-container *swIsControlInvalid="daysOfWeek">
                <mat-error class="days-error" *ngIf="daysOfWeek?.hasError('required')">
                  {{ 'VALIDATION.daysOfWeekRequired' | translate }}
                </mat-error>
              </ng-container>
            </div>
          </div>

          <div class="dates-radio__item">

            <mat-radio-button [value]="2" class="dates-radio__button">
              {{ 'PRIZE_DROP.TOOLTIP.specificDates' | translate }}
            </mat-radio-button>

            <div class="dates-radio__content" [class.hidden]="form.get('repetitionMode')?.value !== 2">

              <div class="dates-radio__hint margin-bottom8">
                Use this settings if the prize drop should run on specific dates
              </div>

              <div class="error-message margin-top16 margin-bottom12" *swIsControlInvalid="specificDates">
                <strong class="error-message__heading">Following fields has errors:</strong>
                <ul *ngFor="let control of specificDates.controls" class="error-message__list">
                  <li *swIsControlInvalid="control">
                    <ul class="error-message__list error-message__list--nested">
                      <li *ngIf="control.hasError('required')">
                        {{ 'VALIDATION.fieldRequired' | translate }}
                      </li>
                      <li *ngIf="control.hasError('isLater')">
                        {{ 'VALIDATION.dateMustBeLater' | translate }}
                      </li>
                    </ul>
                  </li>
                </ul>
              </div>

              <div class="dates-radio__list dates-list">

                <div class="dates-list__item" *ngFor="let control of specificDates?.controls; let i = index">
                  <mat-form-field appearance="outline" class="dates-list__calendar">
                    <mat-label>Date</mat-label>
                    <lib-swui-date-picker
                      required
                      [formControl]="control"
                      [minDate]="minCalendarDate"
                      [disabled]="isDisabled"
                      [config]="{disableTimepicker: true, placeholder: 'Date'}">
                    </lib-swui-date-picker>
                    <mat-icon matSuffix>date_range</mat-icon>
                  </mat-form-field>
                  <button
                    *ngIf="specificDates?.controls.length > 1 && !control.disabled"
                    [disabled]="isDisabled"
                    (click)="removeDate(i)"
                    mat-icon-button class="dates-list__delete">
                    <mat-icon>clear</mat-icon>
                  </button>
                </div>
                <div class="dates-list__item">
                  <button
                    mat-button
                    class="dates-list__add mat-button-md"
                    (click)="addDate()"
                    [disabled]="isDisabled"
                    [color]="'primary'">
                    <mat-icon>add</mat-icon>
                    Add date
                  </button>
                </div>
              </div>
            </div>
          </div>

        </mat-radio-group>
      </div>
      <div class="dates-section__right">
        <div *ngIf="scheduleList.length">
          <div class="dates-table">
            <table>
              <thead>
              <tr>
                <th>Start Time</th>
                <th></th>
                <th>End Time</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let date of scheduleList">
                <td>{{ date.start | formatDate : 'MMM DD, YYYY HH:mm' : timeZone?.value : true : false }}</td>
                <td>-</td>
                <td>{{ date.end | formatDate : 'MMM DD, YYYY HH:mm' : timeZone?.value : true : false }}</td>
              </tr>
              </tbody>
            </table>
          </div>
          <div class="timezone-title">
            {{ ('PRIZE_DROP.FORM.SCHEDULE.timezone' | translate) + ': ' + getTimeZoneTitle(timeZone?.value) }}
          </div>
        </div>
      </div>
    </div>

  </div>

</form>
