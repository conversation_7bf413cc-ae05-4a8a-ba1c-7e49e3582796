import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { PrizeDropFormService } from '../../prize-drop-update/prize-drop-form-service/prize-drop-form.service';
import { PrizeDropScheduleFormComponent } from './prize-drop-schedule-form.component';
import { MODULES } from './prize-drop-schedule-form.module';
import { MatIconRegistry } from '@angular/material/icon';
import { FakeMatIconRegistry } from '@angular/material/icon/testing';

describe('PrizeDropScheduleFormComponent', () => {
  let component: PrizeDropScheduleFormComponent;
  let fixture: ComponentFixture<PrizeDropScheduleFormComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [PrizeDropScheduleFormComponent],
      imports: [
        NoopAnimationsModule,
        TranslateModule.forRoot(),
        ...MODULES,
      ],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        PrizeDropFormService,
        { provide: MatIconRegistry, useClass: FakeMatIconRegistry }
      ],
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PrizeDropScheduleFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
