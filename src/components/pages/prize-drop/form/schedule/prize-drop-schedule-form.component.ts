import { Component, forwardRef, HostB<PERSON>ing, HostListener, Input, OnInit } from '@angular/core';
import {
  AbstractControl, ControlValueAccessor, FormArray, FormBuilder, FormControl, FormGroup, NG_VALIDATORS,
  NG_VALUE_ACCESSOR, ValidationErrors, Validator, Validators
} from '@angular/forms';
import { CustomPeriod } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { startWith, takeUntil } from 'rxjs/operators';
import { getTimeZones } from '../../../../../app.constants';
import { BaseComponent } from '../../../../../common/components/base.component';
import { TimeZone } from '../../../../../common/models/time-zone';
import { FeatureSchedule } from '../../../interfaces/feature';
import { getScheduleRuns } from '../../../interfaces/prize-drop';
import { PrizeDropFormService } from '../../prize-drop-update/prize-drop-form-service/prize-drop-form.service';
import { CUSTOM_PERIODS, processScheduleForm, RepetitionMode } from './prize-drop-schedule-form.helpers';
import {
  laterThanPrevValidator, rangeRequiredValidator, scheduleValidator, startTimeValidator,
} from './prize-drop-schedule-form.validators';

export interface ScheduleItem {
  start: string;
  end: string;
}

@Component({
  selector: 'sw-prize-drop-schedule-form',
  templateUrl: './prize-drop-schedule-form.component.html',
  styleUrls: ['./prize-drop-schedule-form.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PrizeDropScheduleFormComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => PrizeDropScheduleFormComponent),
      multi: true
    }
  ]
})

export class PrizeDropScheduleFormComponent extends BaseComponent implements ControlValueAccessor, Validator, OnInit {
  timezones: TimeZone[] = getTimeZones();
  customPeriods: CustomPeriod[] = CUSTOM_PERIODS;

  readonly form: FormGroup;

  onChange: ( _: any ) => void = (() => {
  });
  isDisabled = false;

  currentTimezone: string | undefined;
  isAddStarTimeAvailable = true;
  minCalendarDate?: string;
  scheduleList: ScheduleItem[] = [];

  @Input() isDuplicate = false;
  @Input() status = '';

  @HostBinding('attr.tabindex') tabindex = 0;

  constructor( private readonly fb: FormBuilder,
               private readonly formService: PrizeDropFormService
  ) {
    super();
    this.form = this.initForm();
    this.minCalendarDate = moment().toISOString();
  }

  @HostListener('blur') onblur() {
    this.onTouched();
  }

  ngOnInit() {
    this.formService.formSubmitted$.pipe(takeUntil(this.destroyed)).subscribe(( val: boolean ) => {
      if (val) {
        this.form.markAllAsTouched();
      }
    });

    this.form.valueChanges
      .pipe(takeUntil(this.destroyed))
      .subscribe(() => {
        const value = this.form.getRawValue();
        this.calcTriggerAt(value.duration, value.triggerAt);
        this.scheduleList = this.calcScheduleList(getScheduleRuns(processScheduleForm(value), 10));
        this.onChange(processScheduleForm(value));
      });

    this.repetitionMode.valueChanges
      .pipe(
        startWith(this.repetitionMode.value as number),
        takeUntil(this.destroyed)
      )
      .subscribe(( val: number ) => {
        this.onRepetitionModeChange(val);
      });

    this.timeZone.valueChanges
      .pipe(takeUntil(this.destroyed))
      .subscribe(( val: string ) => this.currentTimezone = val);
  }

  onTouched: () => void = () => {
  };

  calcScheduleList( schedule: string[] ): ScheduleItem[] {
    const duration = this.duration.value || 0;
    return schedule.map(( el: string ) => {
      const date = new Date(el);
      const ms = date.getTime() + duration;
      return { start: el, end: new Date(ms).toISOString() };
    });
  }

  calcTriggerAt( duration: number | null, time: number[] ) {
    if (duration && time.length) {
      const dayMs = 86400000;
      const availableTimeCount = Math.ceil(dayMs / duration);
      this.isAddStarTimeAvailable = time.length < availableTimeCount;
    } else {
      this.isAddStarTimeAvailable = true;
    }
  }

  writeValue( val: FeatureSchedule ): void {
    if (!val) {
      return;
    }
    this.patchForm(val);
  }

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( isDisabled: boolean ): void {
    this.isDisabled = isDisabled;
    isDisabled ? this.form.disable() : this.form.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid ? null : { invalidForm: { valid: false } };
  }

  removeDate( i: number ) {
    this.specificDates.removeAt(i);
  }

  addDate() {
    const control = this.fb.control(null);
    control.setValidators(Validators.required);
    control.updateValueAndValidity();
    this.specificDates.push(control);
  }

  removeStartTime( i: number ) {
    this.triggerAt.removeAt(i);
  }

  addStartTime() {
    this.triggerAt.push(this.initStartTimeControl(0));
    this.triggerAt.updateValueAndValidity();
  }

  get interval(): FormGroup {
    return this.form.get('interval') as FormGroup;
  }

  get duration(): FormControl {
    return this.form.get('duration') as FormControl;
  }

  get specificDates(): FormArray {
    return this.form.get('specificDates') as FormArray;
  }

  get triggerAt(): FormArray {
    return this.form.get('triggerAt') as FormArray;
  }

  get timeZone(): FormControl {
    return this.form.get('timeZone') as FormControl;
  }

  get repetitionMode(): FormControl {
    return this.form.get('repetitionMode') as FormControl;
  }

  get range(): FormControl {
    return this.interval.get('range') as FormControl;
  }

  get daysOfWeek(): FormControl {
    return this.interval.get('daysOfWeek') as FormControl;
  }

  checkIsControlInvalid( control: AbstractControl ): boolean {
    return this.formService.isControlInvalid(control);
  }

  getTimeZoneTitle( id: string ): string | undefined {
    return this.timezones?.find(item => item.id === id)?.text;
  }

  private initForm(): FormGroup {
    return this.fb.group({
      duration: [null, Validators.required],
      timeZone: [null, Validators.required],
      repetitionMode: [RepetitionMode.Interval],
      specificDates: this.fb.array([
        this.fb.control(null)
      ], { validators: laterThanPrevValidator }),
      interval: this.fb.group({
        daysOfWeek: [[]],
        range: [
          {
            from: '',
            to: ''
          }
        ],
      }),
      triggerAt: this.fb.array([this.initStartTimeControl(0)], { validators: startTimeValidator }),
    }, { validators: scheduleValidator });
  }

  private initStartTimeControl( val: number | null ): FormControl {
    const startTimeControl = this.fb.control(val);
    startTimeControl.setValidators(Validators.required);
    startTimeControl.updateValueAndValidity();
    return startTimeControl;
  }

  private patchForm( value: FeatureSchedule | undefined ) {
    if (!value) {
      return;
    }

    const { duration, timeZone, repetitionMode, specificDates, triggerAt, interval } = value;
    const schedule = {
      duration,
      timeZone,
      repetitionMode,
      specificDates: specificDates || [],
      triggerAt,
      interval: {
        range: {
          from: interval && interval.validFrom ? interval.validFrom : '',
          to: interval && interval.validTo ? interval.validTo : '',
        },
        daysOfWeek: interval ? interval.daysOfWeek : []
      },
    };
    this.form.patchValue(schedule);

    this.specificDates.clear();
    for (const date of specificDates || []) {
      const control = this.fb.control(date);
      this.specificDates.push(control);
      if (!this.isDuplicate && this.status !== 'disabled') {
        if (moment().diff(date, 'days') > 0) {
          control.disable();
          control.updateValueAndValidity();
        }
      }
    }

    this.triggerAt.clear();
    for (const time of triggerAt || []) {
      this.triggerAt.push(this.initStartTimeControl(time));
    }

    this.onRepetitionModeChange(this.repetitionMode.value);
  }

  private onRepetitionModeChange( repetitionMode: RepetitionMode ): void {
    if (repetitionMode === RepetitionMode.Interval) {
      this.specificDates.controls.forEach(control => {
        control.clearValidators();
      });
      this.specificDates.clearValidators();
      this.specificDates.disable();

      this.interval.enable();
      this.range.setValidators(rangeRequiredValidator);
      this.daysOfWeek.setValidators(Validators.required);
    } else {
      this.specificDates.enable();
      if (this.specificDates.controls.length === 0) {
        this.specificDates.push(this.fb.control(null));
      }
      this.specificDates.controls.forEach(( control: AbstractControl ) => {
        control.setValidators(Validators.required);
        control.updateValueAndValidity();
      });
      this.specificDates.setValidators(laterThanPrevValidator);

      this.interval.disable();
      this.range.clearValidators();
      this.daysOfWeek.clearValidators();
    }

    this.specificDates.controls.forEach(( control: AbstractControl ) => {
      control.updateValueAndValidity();
    });
    this.range.updateValueAndValidity();
    this.daysOfWeek.updateValueAndValidity();
  }
}
