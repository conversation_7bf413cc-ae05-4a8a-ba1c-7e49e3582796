import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { TranslateModule } from '@ngx-translate/core';
import {
  SwuiChipsAutocompleteModule, SwuiControlMessagesModule, SwuiTimepickerModule
} from '@skywind-group/lib-swui';

import { CurrencyService } from '../../../../../common/services/currency.service';
import { PrizeDropGeneralFormComponent } from './prize-drop-general-form.component';

export const MODULES = [
  ReactiveFormsModule,
  MatSelectModule,
  MatSlideToggleModule,
  MatFormFieldModule,
  MatInputModule,
  MatRadioModule,
  MatChipsModule,
  MatCheckboxModule,
  SwuiControlMessagesModule,
  SwuiChipsAutocompleteModule,
  SwuiTimepickerModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...MODULES,
  ],
  providers: [
    CurrencyService,
  ],
  exports: [
    PrizeDropGeneralFormComponent,
  ],
  declarations: [
    PrizeDropGeneralFormComponent,
  ],
})
export class PrizeDropGeneralFormModule {
}
