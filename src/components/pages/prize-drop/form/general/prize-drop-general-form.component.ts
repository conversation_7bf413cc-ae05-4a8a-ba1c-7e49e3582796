import { Component, forwardRef, HostB<PERSON>ing, HostL<PERSON>ener, OnInit, } from '@angular/core';
import {
  ControlValueAccessor,
  FormBuilder,
  FormControl,
  FormGroup,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ValidationErrors,
  Validator,
  Validators
} from '@angular/forms';
import { delay, takeUntil } from 'rxjs/operators';

import { BaseComponent } from '../../../../../common/components/base.component';
import { FeatureGeneral } from '../../../interfaces/feature';


@Component({
  selector: 'sw-prize-drop-general-form',
  templateUrl: './prize-drop-general-form.component.html',
  styleUrls: ['./prize-drop-general-form.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PrizeDropGeneralFormComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => PrizeDropGeneralFormComponent),
      multi: true
    }
  ]
})
export class PrizeDropGeneralFormComponent extends BaseComponent implements ControlValueAccessor, Validator, OnInit {
  form: FormGroup = new FormGroup({});
  onChange: ( _: any ) => void = (() => {
  });

  maxNumberInputLength = 50;

  @HostBinding('attr.tabindex') tabindex = 0;

  constructor( private fb: FormBuilder ) {
    super();
    this.initForm();
  }

  @HostListener('blur') onblur() {
    this.onTouched();
  }

  ngOnInit() {
    this.form.valueChanges
      .pipe(
        delay(0),
        takeUntil(this.destroyed)
      )
      .subscribe(( val: FeatureGeneral ) => {
        this.onChange(val);
      });
  }

  onTouched: () => void = () => {
  };

  writeValue( val: FeatureGeneral ): void {
    if (!val) {
      return;
    }
    this.form.patchValue(val, { emitEvent: false });
  }

  registerOnChange( fn: any ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( isDisabled: boolean ): void {
    isDisabled ? this.form.disable() : this.form.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid ? null : { invalidForm: { valid: false } };
  }

  get nameControl(): FormControl {
    return this.form.get('name') as FormControl;
  }

  private initForm() {
    this.form = this.fb.group({
      name: [
        '', Validators.compose([
          Validators.required,
          Validators.maxLength(this.maxNumberInputLength),
        ])
      ],
      description: [''],
      showFeature: [true],
      optIn: [false],
    });
  }
}
