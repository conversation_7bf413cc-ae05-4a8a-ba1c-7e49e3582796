import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { CurrencyService, MockCurrencyService } from '../../../../../common/services/currency.service';
import { MockPrizeDropService, PrizeDropService } from '../../../../../common/services/prize-drop.service';
import { PrizeDropFormService } from '../../prize-drop-update/prize-drop-form-service/prize-drop-form.service';
import { PrizeDropGeneralFormComponent } from './prize-drop-general-form.component';
import { MODULES } from './prize-drop-general-form.module';

describe('PrizeDropGeneralFormComponent', () => {
  let component: PrizeDropGeneralFormComponent;
  let fixture: ComponentFixture<PrizeDropGeneralFormComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        PrizeDropGeneralFormComponent,
      ],
      imports: [
        NoopAnimationsModule,
        HttpClientTestingModule,
        RouterTestingModule,
        ReactiveFormsModule,
        TranslateModule.forRoot(),
        MatCheckboxModule,
        ...MODULES,
      ],
      providers: [
        PrizeDropFormService,
        { provide: CurrencyService, useClass: MockCurrencyService },
        { provide: PrizeDropService, useClass: MockPrizeDropService }
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PrizeDropGeneralFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
