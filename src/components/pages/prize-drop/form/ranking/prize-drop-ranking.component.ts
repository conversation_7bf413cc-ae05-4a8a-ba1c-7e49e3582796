import { Component, forwardRef, Input, OnInit } from '@angular/core';
import {
  ControlValueAccessor, FormBuilder, FormControl, FormGroup, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors,
  Validators
} from '@angular/forms';
import { takeUntil } from 'rxjs/operators';
import { BaseComponent } from '../../../../../common/components/base.component';
import { CurrenciesManagerService } from '../../../../../common/components/currencies-manager/currencies-manager.service';
import { CurrencyModel } from '../../../../../common/models/currency.model';
import { FeatureCurrency } from '../../../interfaces/feature';
import { PrizeDropRanking } from '../../../interfaces/prize-drop';
import { PrizeDropFormService } from '../../prize-drop-update/prize-drop-form-service/prize-drop-form.service';

@Component({
  selector: 'sw-prize-drop-ranking',
  templateUrl: './prize-drop-ranking.component.html',
  styleUrls: ['./prize-drop-ranking.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PrizeDropRankingComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => PrizeDropRankingComponent),
      multi: true
    }
  ]
})

export class PrizeDropRankingComponent extends BaseComponent implements ControlValueAccessor, OnInit {
  @Input('currencies')
  set currenciesList( value: CurrencyModel[] | undefined ) {
    if (!value) {
      return;
    }
    this.processedCurrencies = value.map(( { code } ) => ({ id: code, text: code }));
  }

  processedCurrencies: { id: string, text: string }[] = [];
  selectedCurrencies: FeatureCurrency[] = [];
  form: FormGroup;
  onChange: ( _: any ) => void = (() => {
  });

  constructor( private readonly fb: FormBuilder,
               private readonly formService: PrizeDropFormService,
               private readonly currenciesManagerService: CurrenciesManagerService,
  ) {
    super();
    this.form = this.initForm();
  }

  ngOnInit(): void {
    this.currenciesManagerService.selectedCurrencies$
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe((val: FeatureCurrency[]) => {
        this.selectedCurrencies = val;
      });


    this.baseCurrencyControl.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: string ) => {
        this.formService.baseCurrency = val;
      });

    this.form.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: PrizeDropRanking ) => {
        if (val) {
          val.qualifyingBets = val.qualifyingBets || null;
        }
        this.onChange(val);
      });
  }

  writeValue( val: PrizeDropRanking ): void {
    if (!val) {
      return;
    }

    this.form.patchValue(val);
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: any ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState?( isDisabled: boolean ): void {
    isDisabled ? this.form.disable() : this.form.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid ? null : { invalidForm: { valid: false } };
  }

  get baseCurrencyControl(): FormControl {
    return this.form.get('baseCurrency') as FormControl;
  }

  get qualifyingBetsControl(): FormControl {
    return this.form.get('qualifyingBets') as FormControl;
  }

  private initForm(): FormGroup {
    return this.fb.group({
      baseCurrency: ['', Validators.required],
      qualifyingBets: [],
    });
  }
}
