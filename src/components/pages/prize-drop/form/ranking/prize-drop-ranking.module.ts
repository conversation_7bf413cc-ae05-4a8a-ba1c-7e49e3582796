import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { QualifyingBetsModule } from '../../../../../common/components/qualifying-bets/qualifying-bets.module';
import { PrizeDropRankingComponent } from './prize-drop-ranking.component';

export const RANKING_MODULES = [
  MatFormFieldModule,
  MatSlideToggleModule,
  SwuiSelectModule,
  FlexLayoutModule,
  SwuiControlMessagesModule,
  QualifyingBetsModule,
];


@NgModule({
  declarations: [PrizeDropRankingComponent],
  exports: [PrizeDropRankingComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    ...RANKING_MODULES,
  ]
})
export class PrizeDropRankingModule {
}
