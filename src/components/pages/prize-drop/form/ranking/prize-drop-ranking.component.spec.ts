import { CommonModule } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { CurrenciesManagerService } from '../../../../../common/components/currencies-manager/currencies-manager.service';
import { PrizeDropFormService } from '../../prize-drop-update/prize-drop-form-service/prize-drop-form.service';
import { PrizeDropRankingComponent } from './prize-drop-ranking.component';
import { RANKING_MODULES } from './prize-drop-ranking.module';
import { JpnService, MockJpnService } from '../../../../../common/services/jpn.service';


describe('PrizeDropRankingComponent', () => {
  let component: PrizeDropRankingComponent;
  let fixture: ComponentFixture<PrizeDropRankingComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        NoopAnimationsModule,
        ReactiveFormsModule,
        TranslateModule.forRoot(),
        ...RANKING_MODULES,
      ],
      declarations: [ PrizeDropRankingComponent ],
      providers: [
        PrizeDropFormService,
        { provide: JpnService, useClass: MockJpnService },
        CurrenciesManagerService,
      ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PrizeDropRankingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
