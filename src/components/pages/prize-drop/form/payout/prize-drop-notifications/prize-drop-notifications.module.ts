import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiCurrencySymbolModule } from '@skywind-group/lib-swui';
import { SettingCurrencyFormatterModule } from '../../../../../../common/directives/appSettingCurrencyFormatter/settingCurrencyFormatter.module';
import { PrizeDropNotificationsComponent } from './prize-drop-notifications.component';



@NgModule({
  declarations: [
    PrizeDropNotificationsComponent,
  ],
  exports: [
    PrizeDropNotificationsComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    MatCheckboxModule,
    MatInputModule,
    MatFormFieldModule,
    MatIconModule,
    MatTooltipModule,
    SettingCurrencyFormatterModule,
    SwuiCurrencySymbolModule,
    SwuiControlMessagesModule,
  ]
})
export class PrizeDropNotificationsModule { }
