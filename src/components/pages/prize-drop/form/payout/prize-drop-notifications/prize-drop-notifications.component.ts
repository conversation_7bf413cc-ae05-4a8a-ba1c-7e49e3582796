import { Component, forwardRef, Input, OnDestroy, OnInit } from '@angular/core';
import {
  ControlValueAccessor, FormControl, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors, Validators
} from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'sw-prize-drop-notifications',
  templateUrl: './prize-drop-notifications.component.html',
  styleUrls: ['./prize-drop-notifications.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PrizeDropNotificationsComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => PrizeDropNotificationsComponent),
      multi: true
    },
  ]
})
export class PrizeDropNotificationsComponent implements OnInit, On<PERSON><PERSON><PERSON>, ControlValueAccessor {
  @Input() currency?: string;

  isDisabled = false;
  onChange: ( _: any ) => void = (() => {
  });
  winNotificationAmountControl = new FormControl();
  isChecked = false;

  private readonly _destroyed$ = new Subject<void>();

  constructor() {
  }

  ngOnInit(): void {
    this.winNotificationAmountControl.valueChanges
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: number | null ) => {
        this.onChange(val ?? null);
      });
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  writeValue( val: number | null ): void {
    this.isChecked = val === 0 ? true : !!val;
    this.winNotificationAmountControl.patchValue(val ?? null);
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState?( isDisabled: boolean ): void {
    this.isDisabled = isDisabled;
    isDisabled ? this.winNotificationAmountControl.disable() : this.winNotificationAmountControl.enable();
  }

  validate(): ValidationErrors | null {
    return this.winNotificationAmountControl.valid ? null : { invalidForm: { valid: false } };
  }

  handleChange( event: MatCheckboxChange ) {
    this.isChecked = event.checked;
    if (!event.checked) {
      this.winNotificationAmountControl.clearValidators();
      this.winNotificationAmountControl.patchValue(null);
    } else {
      this.winNotificationAmountControl.setValidators(Validators.required);
      this.winNotificationAmountControl.setValue(0);
    }
    this.winNotificationAmountControl.updateValueAndValidity();
  }
}
