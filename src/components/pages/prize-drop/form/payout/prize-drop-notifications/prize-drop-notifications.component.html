<div class="notifications">
  <mat-checkbox
    [checked]="isChecked"
    [disabled]="isDisabled"
    (change)="handleChange($event)">
    <div class="notifications__checkbox-label">
      {{'PRIZE_DROP.FORM.PAYOUT.winNotificationAmountLabel' | translate}}
      <mat-icon
        class="help-icon notifications__help"
        svgIcon="question_mark"
        matTooltip="{{ 'PRIZE_DROP.TOOLTIP.winNotificationAmount' | translate }}">
      </mat-icon>
    </div>
  </mat-checkbox>

  <div *ngIf="isChecked" class="notifications__checkbox-inner">
    {{'PRIZE_DROP.FORM.PAYOUT.winNotificationAmountControl' | translate}}
    <mat-form-field appearance="outline" class="notifications__checkbox-field">
      <span class="notifications__prefix" matPrefix *ngIf="currency">{{currency | currencySymbol}}</span>
      <input
        matInput
        type="number"
        min="0"
        placeholder="0"
        swCurrencyFormatter
        [currencyCode]="currency"
        [formControl]="winNotificationAmountControl">
      <mat-error>
        <lib-swui-control-messages [control]="winNotificationAmountControl"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </div>
</div>

