import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';
import {
    SwuiControlMessagesModule,
    SwuiCurrencySymbolModule,
    SwuiIsControlInvalidModule,
    SwuiStartTimeModule, SwuiTimeDurationModule
} from '@skywind-group/lib-swui';
import { AlertDialogModule } from '../../../../../common/components/modals/alert-dialog/alert-dialog.module';
import { TotalPayoutsModule } from '../../../../../common/components/total-payouts/total-payouts.module';
import { IntFormatterModule } from '../../../../../common/directives/integerFormatter/intFormatter.module';
import { PrizeDropNotificationsModule } from './prize-drop-notifications/prize-drop-notifications.module';
import { PrizeDropChancesModalComponent } from './prize-drop-payout-form-ranks/prize-drop-chances-modal/prize-drop-chances-modal.component';
import { PrizeDropChancesTabComponent } from './prize-drop-payout-form-ranks/prize-drop-chances-modal/prize-drop-chances-tab/prize-drop-chances-tab.component';
import { PrizeDropChancesComponent } from './prize-drop-payout-form-ranks/prize-drop-chances/prize-drop-chances.component';

import { PrizeDropPayoutFormRanksComponent } from './prize-drop-payout-form-ranks/prize-drop-payout-form-ranks.component';
import { PrizeDropPayoutFormComponent } from './prize-drop-payout-form.component';
import { PrizeDropPayoutTotalComponent } from './prize-drop-payout-total/prize-drop-payout-total.component';
import { SettingCurrencyFormatterModule } from '../../../../../common/directives/appSettingCurrencyFormatter/settingCurrencyFormatter.module';


export const MODULES = [
  MatFormFieldModule,
  MatInputModule,
  MatIconModule,
  MatButtonModule,
  MatCheckboxModule,
  MatMenuModule,
  FlexLayoutModule,
  ReactiveFormsModule,
  SwuiCurrencySymbolModule,
  AlertDialogModule,
  MatRadioModule,
  MatDialogModule,
  MatTabsModule,
  MatSelectModule,
  TotalPayoutsModule,
  SwuiIsControlInvalidModule,
  SwuiControlMessagesModule,
  IntFormatterModule,
  SwuiStartTimeModule,
  PrizeDropNotificationsModule,
];

@NgModule({
  declarations: [
    PrizeDropPayoutFormComponent,
    PrizeDropPayoutTotalComponent,
    PrizeDropPayoutFormRanksComponent,
    PrizeDropChancesModalComponent,
    PrizeDropChancesTabComponent,
    PrizeDropChancesComponent,
  ],
    imports: [
        CommonModule,
        TranslateModule.forChild(),
        ...MODULES,
        SettingCurrencyFormatterModule,
        SwuiTimeDurationModule,
    ],
  exports: [PrizeDropPayoutFormComponent],
})
export class PrizeDropPayoutFormModule {
}
