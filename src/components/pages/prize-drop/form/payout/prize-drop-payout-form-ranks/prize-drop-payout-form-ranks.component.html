<div class="error-message margin-top16" *swIsControlInvalid="payoutsFormArray">
  <strong class="error-message__heading">Following fields has errors:</strong>
  <ul *ngFor="let payout of payoutsFormArray.controls; let index = index;" class="error-message__list">
    <li *swIsControlInvalid="payout">
      <strong class="error-message__heading">Row #{{index + 1}}:</strong>

      <ul class="error-message__list error-message__list--nested">

        <li *swIsControlInvalid="getPlayers(payout)">
          "Players" :
          <lib-swui-control-messages [control]="getPlayers(payout)"></lib-swui-control-messages>
        </li>

        <li *swIsControlInvalid="getPrizeType(payout)">
          "Prize type" :
          <lib-swui-control-messages [control]="getPrizeType(payout)"></lib-swui-control-messages>
        </li>

        <li *swIsControlInvalid="getPayoutsPerRange(payout)">
          "Payout per range" :
          <ul *ngFor="let item of getPayoutsPerRange(payout).controls; let j = index">
            <li *swIsControlInvalid="item">
              <strong>Payout #{{j + 1}}:</strong>
              <lib-swui-control-messages [control]="item"></lib-swui-control-messages>
            </li>
          </ul>
        </li>

        <li *swIsControlInvalid="getChancesPerRange(payout)">
          "Drop chances" :
          <lib-swui-control-messages [control]="getChancesPerRange(payout)" [messages]="dropChancesMessages">
          </lib-swui-control-messages>
        </li>

      </ul>

    </li>
  </ul>
</div>

<form [formGroup]="form" class="scroll">
  <table class="ranks-table sw-mat-table">
    <thead>
    <tr>
      <th class="cell-players"># of prizes</th>
      <th class="cell-type">Prize types</th>
      <th class="cell-payout" *ngFor="let range of columnsRanges">
        {{range}}
      </th>
      <th class="cell-chances cell-chances__header">Drop chances</th>
      <th class="cell-action" *ngIf="payoutsFormArray.controls.length > 1"></th>
    </tr>
    </thead>
    <tbody>
    <ng-container formArrayName="payouts">
      <tr [formGroupName]="rowIndex" *ngFor="let payout of payoutsFormArray.controls; let rowIndex = index">
        <td class="cell-players">
          <mat-form-field appearance="outline" class="no-field-padding">
            <input type="number"
                   min="0"
                   placeholder="0"
                   matInput
                   formControlName="players"
                   swIntFormatter>
          </mat-form-field>
        </td>
        <td class="cell-type">
          <mat-form-field appearance="outline" class="no-field-padding">
            <mat-select formControlName="prizeType">
              <mat-option *ngFor="let item of prizeTypes" [value]="item.id">
                {{item.text}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </td>
        <ng-container formArrayName="payoutsPerRange">
          <td class="cell-payout" *ngFor="let range of getPayoutsPerRange(payout).controls; let colIndex = index">
            <div class="cell-wrap" [ngSwitch]="payout.get('prizeType')?.value">
              <mat-form-field appearance="outline" class="no-field-padding" *ngSwitchDefault>
                <span matPrefix class="cell-prefix">{{currency | currencySymbol}}</span>
                <input type="number"
                       min="0"
                       placeholder="0"
                       matInput
                       [formControlName]="colIndex"
                       swCurrencyFormatter [currencyCode]="currency">
              </mat-form-field>

              <mat-form-field appearance="outline" class="no-field-padding" *ngSwitchCase="'multiplier'">
                <span matPrefix class="cell-prefix cell-prefix--x">✕</span>
                <input type="number"
                       min="0"
                       placeholder="0"
                       matInput
                       [formControlName]="colIndex">
              </mat-form-field>

              <mat-form-field appearance="outline" class="no-field-padding" *ngSwitchCase="'text'">
                <input type="text"
                       matInput
                       [formControlName]="colIndex">
              </mat-form-field>
            </div>
          </td>
        </ng-container>
        <td class="cell-chances">
          <div class="cell-chances__content">
            <sw-prize-drop-chances formControlName="dropChances" [duration]="duration$ | async"></sw-prize-drop-chances>
            <button type="button" color="primary" mat-button
                    (click)="changePayoutChances(payout)">{{!isDisabled ? 'Change' : 'Info'}}</button>
          </div>
        </td>
        <td class="cell-action" *ngIf="payoutsFormArray.controls.length > 1">
          <button mat-icon-button type="button" (click)="removePayoutItem(rowIndex)" [disabled]="isDisabled">
            <mat-icon>clear</mat-icon>
          </button>
        </td>
      </tr>
      <tr *ngIf="payoutsFormArray.controls?.length > 1" class="row-average">
        <td></td>
        <td></td>
        <td class="cell-average">Average</td>
        <td class="cell-chances cell-chances__average">
          <div class="chances">
            <div class="chances__item" *ngFor="let chance of averagePayoutChances">
              {{ chance }}
            </div>
          </div>
        </td>
        <td></td>
      </tr>
    </ng-container>
    </tbody>
  </table>
</form>
<button mat-button [color]="'primary'" (click)="addPayoutItem()" [disabled]="isDisabled">
  <mat-icon>add</mat-icon>
  Add payout
</button>
