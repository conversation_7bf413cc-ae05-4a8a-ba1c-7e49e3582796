import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MODULES } from '../../prize-drop-payout-form.module';

import { PrizeDropChancesComponent } from './prize-drop-chances.component';

describe('PrizeDropChancesComponent', () => {
  let component: PrizeDropChancesComponent;
  let fixture: ComponentFixture<PrizeDropChancesComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PrizeDropChancesComponent ],
      imports: [
        ...MODULES
      ],
      schemas: [NO_ERRORS_SCHEMA],
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PrizeDropChancesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
