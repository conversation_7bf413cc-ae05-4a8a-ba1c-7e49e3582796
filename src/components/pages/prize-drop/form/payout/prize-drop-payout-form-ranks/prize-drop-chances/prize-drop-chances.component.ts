import { ChangeDetectionStrategy, ChangeDetectorRef, Component, forwardRef, Input } from '@angular/core';
import {
  AbstractControl, ControlValueAccessor, FormArray, FormBuilder, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors,
  Validator
} from '@angular/forms';
import { PrizeDropPayoutChance } from '../../../../../interfaces/prize-drop';

@Component({
  selector: 'sw-prize-drop-chances',
  templateUrl: './prize-drop-chances.component.html',
  styleUrls: ['./prize-drop-chances.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PrizeDropChancesComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => PrizeDropChancesComponent),
      multi: true
    }
  ]
})
export class PrizeDropChancesComponent implements ControlValueAccessor, Validator {
  @Input() set duration( duration: number ) {
    this._duration = duration;

    const dur = duration / 4;

    this.form.controls
      .forEach(chance => {
        chance.patchValue({ duration: dur });
      });

    if (this.onChange) {
      this.onChange(this.form.value);
    }
  }

  readonly form: FormArray;

  private _duration = 0;
  private onChange?: ( val: PrizeDropPayoutChance[] ) => void;

  constructor( private fb: FormBuilder,
               private cdr: ChangeDetectorRef
  ) {
    this.form = this.initForm();
  }

  registerOnChange( fn: ( value: PrizeDropPayoutChance[] ) => void ): void {
    this.onChange = fn;
    fn(this.form.value);
  }

  registerOnTouched(): void {
  }

  writeValue( obj: PrizeDropPayoutChance[] ): void {
    if (!obj) {
      return;
    }
    this.form.patchValue(obj);
    this.cdr.markForCheck();
  }

  validate(): ValidationErrors | null {
    return this.form.errors;
  }

  private initForm(): FormArray {
    const formArray = this.fb.array([], { validators: this.chancesValidator.bind(this) });

    const duration = this._duration / 4;

    for (let i = 0; i < 4; i++) {
      const group = this.fb.group({
        duration: [duration],
        chance: 25,
      });

      formArray.push(group);
    }

    return formArray;
  }

  private chancesValidator( abstractControl: AbstractControl ): ValidationErrors | null {
    const control = abstractControl as FormArray;

    if (!control) {
      return null;
    }

    const sum = control.controls
      .reduce(( res, item ) => {
        const perc = Number(item.value.chance);

        return res + perc;
      }, 0);

    if (sum !== 100) {
      return {
        dropChancesSum: true
      };
    }

    return control.controls
      .reduce(( res: any, item, index ) => {
        if (res) {
          return res;
        }

        if (index === 0 && Number(item.value.duration) < 60000) {
          return {
            dropChancesStart: true
          };
        }

        if (index > 0 && index < 3 && Number(item.value.duration) < 60000) {
          return {
            dropChancesPrev: true
          };
        }

        return null;

      }, null);
  }
}
