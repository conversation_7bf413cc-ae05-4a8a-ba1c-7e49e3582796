import { getCurrencySymbol } from '@angular/common';
import { ChangeDetectorRef, Component, forwardRef, Input, OnInit } from '@angular/core';
import {
  AbstractControl, ControlValueAccessor, FormArray, FormBuilder, FormControl, FormGroup, NG_VALIDATORS,
  NG_VALUE_ACCESSOR, ValidationErrors, Validator, Validators
} from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { SettingsService } from '@skywind-group/lib-swui';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, take, takeUntil } from 'rxjs/operators';
import { BaseComponent } from '../../../../../../common/components/base.component';
import { AlertDialogComponent } from '../../../../../../common/components/modals/alert-dialog/alert-dialog.component';
import { currencyFormatter } from '../../../../../../common/directives/appSettingCurrencyFormatter/settingCurrencyFormatter.directive';
import {
  fractionsNumbersLengthValidator, numberMaxLength, numbersOnlyValidator
} from '../../../../../../common/lib/validators';
import { FeaturePayoutPrizeType, FeatureSchedule } from '../../../../interfaces/feature';
import { PrizeDropPayout, PrizeDropPayoutChance, PrizeDropRange } from '../../../../interfaces/prize-drop';
import {
  FormPrizeDropRange, PrizeDropFormService
} from '../../../prize-drop-update/prize-drop-form-service/prize-drop-form.service';
import { PrizeDropChancesModalComponent } from './prize-drop-chances-modal/prize-drop-chances-modal.component';

@Component({
  selector: 'sw-hub-prize-drop-payout-form-ranks',
  templateUrl: './prize-drop-payout-form-ranks.component.html',
  styleUrls: [
    './prize-drop-payout-form-ranks.component.scss',
    './prize-drop-chances/prize-drop-chances.component.scss'
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PrizeDropPayoutFormRanksComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => PrizeDropPayoutFormRanksComponent),
      multi: true
    }
  ],
})
export class PrizeDropPayoutFormRanksComponent extends BaseComponent
  implements ControlValueAccessor, Validator, OnInit {

  @Input() currency?: string;

  @Input() set schedule( schedule: FeatureSchedule ) {
    this._schedule = schedule;

    if (schedule && this.duration$.value !== schedule.duration) {
      this.duration$.next(schedule.duration);
    }
  }

  @Input()
  get ranges(): FormPrizeDropRange | null {
    return this._ranges$.value;
  }

  set ranges( value: FormPrizeDropRange | null ) {
    this._ranges$.next(value || null);
  }

  readonly form: FormGroup;
  columnsRanges: string[] = ['Prize'];
  maxNumberInputLength = 8;
  onChange: ( _: any ) => void = (() => {
  });
  isDisabled = false;
  prizeTypes: { id: FeaturePayoutPrizeType, text: string }[] = [
    { id: 'fixed', text: 'Fixed' },
    { id: 'multiplier', text: 'Multiplier' },
    { id: 'text', text: 'Text' }
  ];

  averagePayoutChances: string[] = [];

  readonly dropChancesMessages = {
    dropChancesSum: 'Sum of the chances percents should be 100',
    dropChancesStart: 'Duration should be more than start time',
    dropChancesPrev: 'Duration should be more previous duration',
    dropChancesLast: 'Last duration should be equals schedule duration duration'
  };

  duration$ = new BehaviorSubject(0);
  private payouts: PrizeDropPayout[] = [];
  private _schedule!: FeatureSchedule;

  private readonly _ranges$ = new BehaviorSubject<FormPrizeDropRange | null>(null);
  private readonly _payouts$ = new Subject<void>();

  constructor( private readonly fb: FormBuilder,
               private readonly dialog: MatDialog,
               private readonly settingsService: SettingsService,
               private readonly cdr: ChangeDetectorRef,
               private readonly formService: PrizeDropFormService
  ) {
    super();
    this.form = this.initForm();
    this.getAverageChances();
  }

  ngOnInit() {
    combineLatest([this.formService.formSubmitted$, this.payoutsFormArray.valueChanges])
      .pipe(
        debounceTime(10),
        distinctUntilChanged(( [, prev], [, curr] ) => JSON.stringify(prev) === JSON.stringify(curr)),
        takeUntil(this.destroyed)
      )
      .subscribe(() => {
        this.payoutsFormArray.controls.forEach(cnt => {
          const control = cnt.get('players');
          control?.markAsTouched();
          control?.updateValueAndValidity();
        });
      });

    combineLatest([this._ranges$, this._payouts$])
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( [ranges] ) => {
        this.setHeader(ranges);
        this.setPayouts(this.payouts);
      });

    this.form.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(() => {
        this.payoutsFormArray.controls.forEach(( control: AbstractControl ) => {
          const ctrl = control as FormGroup;
          const prizeTypeControl = ctrl.get('prizeType') as FormControl;
          const payoutsPerRangeArray = ctrl.get('payoutsPerRange') as FormArray;
          payoutsPerRangeArray.controls.forEach(( payout: AbstractControl ) => {
            if (prizeTypeControl.value === 'text') {
              payout.setValidators(Validators.compose([
                Validators.required,
                Validators.maxLength(50)
              ]));
            } else {
              payout.setValidators(Validators.compose([
                Validators.required,
                numbersOnlyValidator,
                fractionsNumbersLengthValidator,
                numberMaxLength(8),
              ]));
            }
          });
        });
        const val = this.form.getRawValue();
        const processed = val.payouts.map(( payout: PrizeDropPayout ) => {
          const payoutsPerRange: any[] = payout.payoutsPerRange;
          if (payout.prizeType === 'fixed' || payout.prizeType === 'multiplier') {
            payout.payoutsPerRange = payoutsPerRange.map(( el: any ) => el !== null && typeof el !== 'number' ? parseFloat(el) : el);
          } else if (payout.prizeType === 'text') {
            payout.payoutsPerRange = payoutsPerRange.map(( el: any ) => el !== null && typeof el !== 'string' ? el.toString() : el);
          }
          return payout;
        });
        this.payouts = processed;
        this.onChange(processed);
      });
  }

  onTouched: any = () => {
  };

  writeValue( value: PrizeDropPayout[] ) {
    this.payouts = Array.isArray(value) ? value : [];
    this._payouts$.next();
  }

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( disabled: boolean ) {
    this.isDisabled = disabled;
    disabled ? this.form.disable() : this.form.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid ? null : { invalidForm: { valid: false } };
  }

  get payoutsFormArray(): FormArray {
    return this.form.get('payouts') as FormArray;
  }

  getPlayers( payoutRow: AbstractControl ): FormControl {
    return payoutRow.get('players') as FormControl;
  }

  getPrizeType( payoutRow: AbstractControl ): FormControl {
    return payoutRow.get('prizeType') as FormControl;
  }

  changePayoutChances( payout: AbstractControl ) {
    if (!this._schedule.duration) {
      this.dialog.open(AlertDialogComponent, {
        data: {
          message: 'Duration should be set'
        }
      });
      return;
    }

    const dropChancesArray = payout.get('dropChances') as FormArray;

    this.dialog.open(PrizeDropChancesModalComponent, {
      data: {
        chances: dropChancesArray.value,
        startTimes: this._schedule.triggerAt,
        duration: this.duration$.value,
        isDisabled: this.isDisabled,
        prizeDescription: this.getPrizeDescription(payout),
      }
    }).afterClosed()
      .pipe(
        take(1),
        filter(value => !!value)
      )
      .subscribe(result => {
        dropChancesArray.patchValue(result);
        this.cdr.markForCheck();
      });
  }

  getPrizeDescription( payout: AbstractControl ): string | undefined {
    let prefix = payout.value.prizeType === 'multiplier' ?
      '× ' :
      (payout.value.prizeType === 'text' ? '' : (getCurrencySymbol(this.currency || '', 'narrow') + ' '));

    let value = payout.value.prizeType === 'fixed' && Array.isArray(payout.value.payoutsPerRange) &&
    payout.value.payoutsPerRange.length ?
      payout.value.payoutsPerRange[0]?.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',') :
      payout.value.payoutsPerRange;

    return value ? `Prize: ${prefix}${value}` : '';
  }

  addPayoutItem() {
    this.newPayoutItem(this.payouts.slice().pop()?.prizeType);
  }

  removePayoutItem( index: number ) {
    this.payoutsFormArray.removeAt(index);
  }

  getPayoutsPerRange( payoutRow: AbstractControl ): FormArray {
    return payoutRow.get('payoutsPerRange') as FormArray;
  }

  getChancesPerRange( payoutRow: AbstractControl ): FormArray {
    return payoutRow.get('dropChances') as FormArray;
  }

  private setHeader( ranges: FormPrizeDropRange | null ) {
    if (ranges) {
      if (this.currency) {
        const currencyRanges = ranges[this.currency];
        const currencyFormat = this.settingsService.appSettings.currencyFormat;
        if (currencyRanges) {
          this.columnsRanges = currencyRanges.map(( el: PrizeDropRange ) => {
            const suffixMin = el && el.min ?
              currencyFormatter(currencyFormat, el.min, this.currency) : '0';
            const suffixMax = el && el.max ?
              currencyFormatter(currencyFormat, el.max, this.currency) : 'MAX';
            return 'Prize ' + getCurrencySymbol(this.currency || '', 'narrow') + suffixMin + '-' + suffixMax;
          });
        }
      }
    } else {
      this.columnsRanges = ['Prize'];
    }
  }

  private setPayouts( payouts: PrizeDropPayout[] ) {
    this.payoutsFormArray.clear();
    if (payouts.length) {
      payouts.forEach(payout => {
        this.newPayoutItem(payout.prizeType);
      });
      this.payoutsFormArray.patchValue(payouts);
    } else {
      this.newPayoutItem();
    }
  }

  private newPayoutItem( prizeType: FeaturePayoutPrizeType = 'fixed' ) {
    const payoutRow = this.initPayoutItem(prizeType);
    const payoutsPerRange = this.getPayoutsPerRange(payoutRow);
    payoutsPerRange.clear();
    if (this.ranges) {
      if (this.currency) {
        const currencyRanges = this.ranges[this.currency];
        if (currencyRanges?.length) {
          currencyRanges.forEach(() => {
            payoutsPerRange.push(this.initPerRangeItem());
          });
        }
      }
    } else {
      payoutsPerRange.push(this.initPerRangeItem());
    }
    this.payoutsFormArray.push(payoutRow);
  }

  private initForm(): FormGroup {
    return this.fb.group({
      payouts: this.fb.array([this.initPayoutItem()])
    });
  }

  private prizesCountValidator( cnt: AbstractControl ) {
    const form: FormArray = cnt.parent?.parent as any as FormArray;

    if (form) {
      const sum = form.controls.reduce(( res, control ) => {
        const val = control.get('players')?.value;

        res += val || 0;

        return res;
      }, 0);

      if (sum > 1000) {
        return { maxPrizesCount: true };
      }
    }

    return null;
  }

  private getAverageChances() {
    this.payoutsFormArray.valueChanges
      .pipe(
        takeUntil(this.destroyed),
      )
      .subscribe(
        payouts => {
          if (Array.isArray(payouts) && payouts.length) {
            let sumOfChances = payouts.map(payout =>
              payout.dropChances?.map(( dropChance: PrizeDropPayoutChance ) => dropChance.chance));

            let result = sumOfChances?.reduce(( acc, curr ) => {
              return acc?.map(( chance: number, i: number ) => chance + (curr && curr[i] || 0));
            });

            result = result?.map(( sum: number ) => (sum / payouts.length).toFixed(1));

            this.averagePayoutChances = result?.map(( item: string ) => item.replace('.0', '') + '%');

            this.cdr.detectChanges();
          }
        }
      );
  }

  private initPayoutItem( prizeType: FeaturePayoutPrizeType = 'fixed' ): FormGroup {
    return this.fb.group({
      players: [
        { value: null, disabled: this.form && this.form.disabled }, Validators.compose([
          Validators.required,
          numbersOnlyValidator,
          Validators.maxLength(this.maxNumberInputLength),
          this.prizesCountValidator.bind(this)
        ])
      ],
      prizeType: [prizeType, Validators.required],
      payoutsPerRange: this.fb.array([this.initPerRangeItem()]),
      dropChances: []
    });
  }

  private initPerRangeItem(): FormControl {
    return this.fb.control({
      value: null,
      disabled: this.form && this.form.disabled
    }, Validators.compose([
      Validators.required,
      numbersOnlyValidator,
      Validators.maxLength(this.maxNumberInputLength),
    ]));
  }
}
