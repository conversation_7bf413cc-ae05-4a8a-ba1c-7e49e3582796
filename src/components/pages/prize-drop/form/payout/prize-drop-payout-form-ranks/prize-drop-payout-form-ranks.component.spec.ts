import { CommonModule } from '@angular/common';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { SettingsService } from '@skywind-group/lib-swui';

import { PrizeDropFormService } from '../../../prize-drop-update/prize-drop-form-service/prize-drop-form.service';
import { MODULES } from '../prize-drop-payout-form.module';
import { PrizeDropChancesComponent } from './prize-drop-chances/prize-drop-chances.component';
import { PrizeDropPayoutFormRanksComponent } from './prize-drop-payout-form-ranks.component';


describe('PrizeDropPayoutFormRanksComponent', () => {
  let component: PrizeDropPayoutFormRanksComponent;
  let fixture: ComponentFixture<PrizeDropPayoutFormRanksComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        PrizeDropPayoutFormRanksComponent,
        PrizeDropChancesComponent
      ],
      imports: [
        CommonModule,
        NoopAnimationsModule,
        TranslateModule.forRoot(),
        ...MODULES,
      ],
      providers: [
        PrizeDropFormService,
        SettingsService,
      ],
      schemas: [NO_ERRORS_SCHEMA],
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PrizeDropPayoutFormRanksComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
