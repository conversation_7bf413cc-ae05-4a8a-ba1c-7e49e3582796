<div class="chances-dialog">
  <h2 mat-dialog-title class="chances-dialog__title">
    Drop chances
  </h2>
  <div class="chances-dialog__title">{{ prizeDescription }}</div>
  <div class="chances-dialog__body">
    <div class="error-message margin-top16" *ngIf="errors && submitted$ | async">
      <div>{{formErrors}}</div>
    </div>

    <ng-container *ngIf="startTimes.length > 1; else oneStartTimeTmt">
      <mat-tab-group animationDuration="0">
        <mat-tab *ngFor="let time of startTimes; let index = index">
          <ng-template mat-tab-label>{{time | date : 'HH:mm' : '+000'}}
            - {{time + duration | date : 'HH:mm' : '+000'}}</ng-template>
          <sw-prize-drop-chances-tab
            class="prize-drop-chance-change-dialog"
            [editable]="index === 0"
            [startTime]="time"
            [duration]="duration"
            [formData]="chances"
            [isDisabled]="isDisabled"
            (chancesChange)="onChanceChange($event)"
            (errors)="onChanceError($event)"
          ></sw-prize-drop-chances-tab>
        </mat-tab>
      </mat-tab-group>
    </ng-container>

    <ng-template #oneStartTimeTmt>
      <sw-prize-drop-chances-tab
        class="prize-drop-chance-change-dialog"
        [startTime]="startTimes[0]"
        [duration]="duration"
        [formData]="chances"
        [isDisabled]="isDisabled"
        (chancesChange)="onChanceChange($event)"
        (errors)="onChanceError($event)"
      ></sw-prize-drop-chances-tab>
    </ng-template>

  </div>
  <mat-dialog-actions class="chances-dialog__actions">
    <ng-container *ngIf="!isDisabled; else okButton">
      <button mat-flat-button class="mat-button-md link chances-dialog__cancel" (click)="close()">
        {{ 'DIALOG.cancel' | translate }}
      </button>
      <button mat-flat-button
              color="primary"
              class="mat-button-md"
              [disabled]="errors && submitted$ | async"
              (click)="save()">
        {{ 'DIALOG.save' | translate }}
      </button>
    </ng-container>
    <ng-template #okButton>
      <button mat-flat-button class="mat-button-md link" (click)="close()">
        {{ 'DIALOG.ok' | translate }}
      </button>
    </ng-template>
  </mat-dialog-actions>
  <button class="chances-dialog__close" mat-icon-button (click)="dialogRef.close()">
    <mat-icon svgIcon="clear"></mat-icon>
  </button>
</div>
