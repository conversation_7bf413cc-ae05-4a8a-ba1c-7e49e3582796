import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormControl } from '@angular/forms';
import * as moment from 'moment';
import { Subject } from 'rxjs';
import { distinctUntilChanged, takeUntil } from 'rxjs/operators';
import { PrizeDropPayoutChance } from '../../../../../../interfaces/prize-drop';

@Component({
  selector: 'sw-prize-drop-chances-tab',
  templateUrl: './prize-drop-chances-tab.component.html',
  styleUrls: ['./prize-drop-chances-tab.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PrizeDropChancesTabComponent implements OnInit {
  @Input()
  set isDisabled( val: boolean ) {
    val ? this.form.disable() : this.form.enable();
  }

  @Input() set formData( formData: PrizeDropPayoutChance[] ) {
    if (JSON.stringify(this.form.getRawValue()) === JSON.stringify(formData)) {
      return;
    }

    let currentDur = this.startTime || 0;

    formData.forEach(( chance, index ) => {
      currentDur += chance.duration || 0;

      const value = {
        duration: index === 3 ? this.startTime + this.duration : currentDur,
        chance: chance.chance
      };

      if (this.form.controls[index]) {
        this.form.controls[index].patchValue(value, { emitEvent: false });
      } else {
        const group = this.fb.group(value);
        this.form.push(group);
      }
    });
  }

  @Input() editable = true;
  @Input() startTime!: number;
  @Input() duration!: number;

  @Output() chancesChange = new EventEmitter<PrizeDropPayoutChance[]>();
  @Output() errors = new EventEmitter<any>();

  form: FormArray;
  private destroy$ = new Subject();

  constructor( private fb: FormBuilder ) {
    this.form = this.fb.array([], { validators: [this.chancesValidator.bind(this)] });

    this.form.valueChanges
      .pipe(
        distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        const data = this.form.getRawValue();
        let currentDur = this.startTime;

        const result = data.map(( item: PrizeDropPayoutChance ) => {
          const chance = {
            chance: Number(item.chance),
            duration: Number(item.duration) - currentDur
          };

          currentDur = item.duration;

          return chance;
        });

        this.chancesChange.emit(result);
      });
  }

  ngOnInit(): void {
    this.form.statusChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.errors.emit(this.form.errors);
      });
  }

  getDurationControl( chance: AbstractControl ): FormControl {
    return chance.get('duration') as FormControl;
  }

  getChanceControl( chance: AbstractControl ): FormControl {
    return chance.get('chance') as FormControl;
  }

  chancesValidator( control: AbstractControl ) {
    const chancesForm = control as FormArray;

    const sum = chancesForm.controls
      .reduce(( res, item ) => {
        const perc = Number(item.value.chance);

        return res + perc;
      }, 0);

    if (sum !== 100) {
      return {
        error: 'Sum of the chances percents should be 100'
      };
    }

    return chancesForm.controls
      .reduce(( res: any, item, index ) => {
        if (res) {
          return res;
        }

        if ((index === 0 && Number(item.value.duration) < this.startTime - 60000) ||
          Number(item.value.duration) === this.startTime) {
          return {
            error: 'Duration should be more than start time'
          };
        }

        if (index > 0 && (Number(item.value.duration) - 60000)
          < Number(chancesForm.controls[index - 1].value.duration)) {
          return {
            error: 'Duration should be more previous duration'
          };
        }

        if (index === 3 && Number(item.value.duration) > this.startTime + this.duration) {
          return {
            error: 'Duration should be equal start time with duration'
          };
        }

        return null;

      }, null);
  }

  getDuration( startTime: number, endTime: number ): string {
    let result = '';
    const resStartTime = startTime;
    const resEndTime = endTime;

    if (resEndTime > resStartTime) {
      const diff = resEndTime - resStartTime;

      const hours = moment.duration(diff).hours();
      const minutes = moment.duration(diff).minutes();
      const days = moment.duration(diff).days();

      if (days) {
        result = `${days}d `;
      }

      if (hours) {
        result = `${result}${hours}h `;
      }

      if (minutes) {
        result = `${result}${minutes}min`;
      }
    }

    return result;
  }

  getTimeDuration( time: number ) {
    const hours = String(moment.duration(time).hours()).padStart(2, '0');
    const minutes = String(moment.duration(time).minutes()).padStart(2, '0');
    const days = String(moment.duration(time).days()).padStart(2, '0');

    return `${days}:${hours}:${minutes}`;
  }
}
