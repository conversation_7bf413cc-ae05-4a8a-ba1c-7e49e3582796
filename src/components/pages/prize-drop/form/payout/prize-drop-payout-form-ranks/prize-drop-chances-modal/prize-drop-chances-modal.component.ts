import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { BehaviorSubject } from 'rxjs';
import { PrizeDropPayoutChance } from '../../../../../interfaces/prize-drop';

interface DialogData {
  chances: PrizeDropPayoutChance[];
  startTimes: number[];
  duration: number;
  isDisabled: boolean;
  prizeDescription?: string;
}

@Component({
  selector: 'sw-prize-drop-chances-modal',
  templateUrl: './prize-drop-chances-modal.component.html',
  styleUrls: ['./prize-drop-chances-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PrizeDropChancesModalComponent implements OnInit {
  chances: PrizeDropPayoutChance[];
  startTimes: number[];
  duration: number;
  errors: any;
  submitted$ = new BehaviorSubject(false);
  isDisabled = false;
  prizeDescription?: string;

  constructor( @Inject(MAT_DIALOG_DATA) { chances, startTimes, duration, isDisabled, prizeDescription }: DialogData,
               public dialogRef: MatDialogRef<PrizeDropChancesModalComponent>,
               private cdr: ChangeDetectorRef
  ) {
    this.chances = [...chances];
    this.startTimes = startTimes || 0;
    this.duration = duration || 0;
    this.isDisabled = !!isDisabled;
    this.prizeDescription = prizeDescription;
  }

  ngOnInit(): void {
  }

  onChanceChange( data: PrizeDropPayoutChance[] ) {
    this.submitted$.next(false);
    this.chances = data;
    this.cdr.markForCheck();
  }

  onChanceError( errors: any ) {
    this.errors = errors;
  }

  get formErrors(): string {
    return Object.values(this.errors)[0] as string;
  }

  close() {
    this.dialogRef.close();
  }

  save() {
    this.submitted$.next(true);

    if (this.errors) {
      return;
    }

    this.dialogRef.close(this.chances);
  }

}
