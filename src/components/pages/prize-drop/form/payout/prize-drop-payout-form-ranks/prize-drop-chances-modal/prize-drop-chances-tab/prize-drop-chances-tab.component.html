<table class="sw-mat-table chances-table">
  <thead>
  <tr>
    <th>From</th>
    <th>To</th>
    <th>Duration</th>
    <th>Chance</th>
  </tr>
  </thead>
  <tr class="row" *ngFor="let chanceItem of form.controls; let index = index">
    <td>
      <div class="from-cell">
        {{ getTimeDuration(index >= 1 ? form.controls[index - 1]?.value?.duration : startTime) }}
        <div class="mr-5">-</div>
      </div>
    </td>
    <td>
      <ng-container *ngIf="editable && index !== 3; else duration">
        <mat-form-field class="new-duration_field no-field-padding" appearance="outline">
          <lib-swui-time-duration
            required
            [formControl]="getDurationControl(chanceItem)"
            [secondsDisabled]="true">
          </lib-swui-time-duration>
        </mat-form-field>
      </ng-container>

      <ng-template #duration>
        <div
          class="cell__not-editable">{{ getTimeDuration(form.controls[index]?.value?.duration) }}</div>
      </ng-template>
    </td>
    <td>
      <div class="cell__not-editable">
        {{ getDuration(index >= 1 ? form.controls[index - 1]?.value?.duration : startTime,
        form.controls[index]?.value?.duration) }}
      </div>
    </td>
    <td>
      <ng-container *ngIf="editable; else chance">
        <mat-form-field class="new-chance_field no-field-padding" appearance="outline">
          <input matInput type="number" [formControl]="getChanceControl(chanceItem)">
          <span matSuffix class="new-chance_suffix">%</span>
        </mat-form-field>
      </ng-container>

      <ng-template #chance>
        <div class="cell__not-editable">{{ form.controls[index]?.value?.chance }} %</div>
      </ng-template>
    </td>
  </tr>
</table>
