import { CommonModule } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';

import { PrizeDropChancesTabComponent } from './prize-drop-chances-tab.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CommonService } from '../../../../../../../../common/services/common/common.service';
import { SwuiNotificationsModule } from '@skywind-group/lib-swui';

describe('PrizeDropChancesTabComponent', () => {
  let component: PrizeDropChancesTabComponent;
  let fixture: ComponentFixture<PrizeDropChancesTabComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CommonModule, ReactiveFormsModule, MatDialogModule, HttpClientTestingModule, SwuiNotificationsModule.forRoot(),],
      declarations: [PrizeDropChancesTabComponent],
      providers: [CommonService]
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PrizeDropChancesTabComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
