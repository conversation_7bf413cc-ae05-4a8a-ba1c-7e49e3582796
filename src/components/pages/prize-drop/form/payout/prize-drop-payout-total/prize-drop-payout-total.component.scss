.total-table {
  position: relative;
  display: inline-block;
  min-width: 256px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  box-shadow: 0 0 6px rgba(#2A2C44, 0.2);
  &.multiplier-enabled {
    margin-top: 76px;
  }
  &__title {
    margin: 24px 0 8px;
    font-weight: 500;
  }
  &__total {
    margin-left: auto;
    white-space: nowrap;
  }
  &__acr-row {
    th {
      background: #ECECEC;
    }
  }
  &__footer {
    font-size: 14px;
    font-weight: 500;
  }
  &__tbl{
    width: 100%;
    td, th {
      height: 65px;
      min-width: 200px;
      line-height: 1;
      text-align: left !important;
      &:first-child {
        width: 138px;
        min-width: auto;
        padding-left: 24px;
      }
      &:last-child {
        padding-right: 24px;
      }
      &.total {
        font-weight: 500;
      }
    }
  }
  &__th-inner {
    display: inline-block;
  }
  &__symbol {
    margin-right: 3px;
  }
}


.col-text {
  display: flex;
  align-items: center;
  line-height: 1;
  &__payout {
    max-width: 115px;
    margin-right: 4px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  &__amount {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    height: 22px;
    min-width: 22px;
    padding: 2px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 7px;
  }
}

.col-multi {
  &__expected {
    display: inline-flex;
    align-items: center;
    height: 20px;
    margin-top: 4px;
    padding: 0 5px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.87);
    background-color: #ECECEC;
    border-radius: 3px;
  }
}

.ttl {
  width: 100%;
  display: flex;
  min-height: 72px;
  padding: 12px 24px;
  color: #fff;
  background: rgba(0, 0, 0, 0.87);
  &__left {
    min-width: 124px;
    padding-right: 24px;
  }
  &__expected {
    font-size: 14px;
    line-height: 20px;
    font-weight: 500;
    letter-spacing: 0.25px;
    color: rgba(255, 255, 255, 0.74);
  }
  &__total {
    line-height: 24px;
    font-weight: 500;
  }
  &__item {
    line-height: 24px;
  }

}

.average {
  min-width: 125px;
  &__inner {
    position: absolute;
    bottom: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 64px;
    width: 100%;
    background: #ECECEC;
  }
  &__title {
    font-size: 14px;
    line-height: 16px;
    font-weight: 500;
  }
  &__subtitle {
    font-size: 12px;
    line-height: 16px;
    font-weight: 400;
  }
  &__field {
    width: 97px;
  }
  th {
    position: relative;
    padding: 0 !important;
    height: 0 !important;
    &:not(:first-child) {
      .average {
        &__inner {
          padding-left: 16px;
          border-top-left-radius: 3px;
          border-bottom-left-radius: 3px;
        }
      }
    }
    &:first-child {
      min-width: 150px;
      .average {
        &__inner {
          padding-left: 24px;
          border-top-left-radius: 3px;
          border-bottom-left-radius: 3px;
        }
      }
    }
    &:last-child {
      .average {
        &__inner {
          padding-right: 24px;
          border-top-right-radius: 3px;
          border-bottom-right-radius: 3px;
        }
      }
    }
  }
}
