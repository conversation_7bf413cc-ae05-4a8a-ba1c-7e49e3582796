import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { combineLatest } from 'rxjs';
import { filter, map, takeUntil } from 'rxjs/operators';

import { BaseComponent } from '../../../../../../common/components/base.component';
import { CurrenciesManagerService } from '../../../../../../common/components/currencies-manager/currencies-manager.service';
import { ColumnNames } from '../../../../../../common/components/total-payouts/total-payouts.component';
import { FeatureCurrency, FeaturePayout } from '../../../../interfaces/feature';
import { PrizeDropFormService } from '../../../prize-drop-update/prize-drop-form-service/prize-drop-form.service';


@Component({
  selector: 'sw-hub-prize-drop-payout-total',
  templateUrl: './prize-drop-payout-total.component.html',
  styleUrls: ['./prize-drop-payout-total.component.scss']
})
export class PrizeDropPayoutTotalComponent extends BaseComponent implements OnInit {

  @Input()
  get payouts(): FeaturePayout[] {
    return this._payouts;
  }

  set payouts( value: FeaturePayout[] ) {
    if (!value) {
      return;
    }
    this._payouts = value;
  }
  columnNames: ColumnNames = {
    columnTier: 'Prize Tier',
    columnTierTotal: 'Tier Total'
  };
  currencies: FeatureCurrency[] = [];
  currencyToActivate?: string;
  @ViewChild('payoutTotal') payoutTotalRef?: ElementRef;
  private _payouts: FeaturePayout[] = [];

  constructor( private readonly formService: PrizeDropFormService,
               private readonly currenciesManagerService: CurrenciesManagerService, ) {
    super();
  }

  ngOnInit(): void {
    combineLatest([this.formService.baseCurrency$, this.currenciesManagerService.selectedCurrencies$])
      .pipe(
        map(( [baseCurrency, currencies] ) => {
          if (baseCurrency && !currencies.find(( el: FeatureCurrency ) => el.code === baseCurrency)) {
            return [{ code: baseCurrency, rate: 1 }, ...currencies];
          } else if (baseCurrency && currencies.find(( el: FeatureCurrency ) => el.code === baseCurrency)){
            const index = currencies.findIndex( ( el: FeatureCurrency ) => el.code === baseCurrency);
            currencies.splice(index, 1);
            return [{ code: baseCurrency, rate: 1 }, ...currencies];
          } else {
            return currencies;
          }
        }),
        takeUntil(this.destroyed)
      )
      .subscribe( (currencies: FeatureCurrency[]) => {
        this.currencies = currencies;
      });

    this.currenciesManagerService.selectedPayoutCurrency$
      .pipe(
        filter(val => !!val),
        takeUntil(this.destroyed)
      )
      .subscribe((val: FeatureCurrency | undefined) => {
        this.currencyToActivate = val ? val.code : undefined;
        setTimeout( () => {
          if (this.payoutTotalRef) {
            const refElement = this.payoutTotalRef.nativeElement as HTMLElement;
            const scrollableContainer = document.getElementById('main-content-wrap');
            if (scrollableContainer) {
              scrollableContainer.scroll({top: refElement?.offsetTop + 500, behavior:'smooth'});
            }
          }
        }, 500);
      });
  }

}
