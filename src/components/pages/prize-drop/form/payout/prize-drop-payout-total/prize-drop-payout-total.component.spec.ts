import { CommonModule } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { CurrenciesManagerService } from '../../../../../../common/components/currencies-manager/currencies-manager.service';
import { JpnService, MockJpnService } from '../../../../../../common/services/jpn.service';
import { PrizeDropFormService } from '../../../prize-drop-update/prize-drop-form-service/prize-drop-form.service';

import { MODULES } from '../prize-drop-payout-form.module';
import { PrizeDropPayoutTotalComponent } from './prize-drop-payout-total.component';
import { SettingsService } from '@skywind-group/lib-swui';

describe('PrizeDropPayoutTotalComponent', () => {
  let component: PrizeDropPayoutTotalComponent;
  let fixture: ComponentFixture<PrizeDropPayoutTotalComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [PrizeDropPayoutTotalComponent],
      imports: [
        CommonModule,
        HttpClientTestingModule,
        NoopAnimationsModule,
        ...MODULES,
      ],
      providers: [
        SettingsService,
        PrizeDropFormService,
        { provide: JpnService, useClass: MockJpnService },
        CurrenciesManagerService,
      ],
      schemas: [NO_ERRORS_SCHEMA],
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PrizeDropPayoutTotalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
