import { Component, forwardRef, Input, OnInit } from '@angular/core';
import {
  ControlValueAccessor, FormArray, FormBuilder, FormControl, FormGroup, NG_VALIDATORS, NG_VALUE_ACCESSOR,
  ValidationErrors, Validator,
} from '@angular/forms';
import { combineLatest } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { BaseComponent } from '../../../../../common/components/base.component';
import { FeatureSchedule } from '../../../interfaces/feature';
import { PrizeDropConfiguration } from '../../../interfaces/prize-drop';
import {
  FormPrizeDropRange, PrizeDropFormService
} from '../../prize-drop-update/prize-drop-form-service/prize-drop-form.service';


@Component({
  selector: 'sw-prize-drop-payout-form',
  templateUrl: './prize-drop-payout-form.component.html',
  styleUrls: ['./prize-drop-payout-form.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PrizeDropPayoutFormComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => PrizeDropPayoutFormComponent),
      multi: true
    }
  ]
})
export class PrizeDropPayoutFormComponent extends BaseComponent implements ControlValueAccessor, Validator, OnInit {
  form: FormGroup;
  baseCurrency?: string;
  ranges: FormPrizeDropRange | null = null;
  onChange: ( _: any ) => void = (() => {
  });

  @Input() schedule!: FeatureSchedule;

  constructor( private readonly fb: FormBuilder,
               private readonly formService: PrizeDropFormService
  ) {
    super();
    this.form = this.initForm();
  }

  ngOnInit() {
    combineLatest([this.formService.baseCurrency$, this.formService.ranges$])
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( [baseCurrency, ranges] ) => {
        this.baseCurrency = baseCurrency;
        if (ranges && this.baseCurrency) {
          this.ranges = ranges;
        } else {
          this.ranges = null;
        }
      });

    this.formService.formSubmitted$
      .pipe(
        takeUntil(this.destroyed)
      ).subscribe(( val: boolean ) => {
      if (val) {
        this.form.markAllAsTouched();
      }
    });

    this.form.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: PrizeDropConfiguration ) => {
        this.onChange(val);
      });
  }

  onTouched: () => void = () => {
  };

  writeValue( val: PrizeDropConfiguration ): void {
    if (!val) {
      return;
    }
    this.form.patchValue(val);
  }

  registerOnChange( fn: any ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState?( isDisabled: boolean ): void {
    isDisabled ? this.form.disable() : this.form.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid ? null : { invalidForm: { valid: false } };
  }

  get payouts(): FormArray {
    return this.form.get('payouts') as FormArray;
  }

  get nonDroppedPrizes(): FormControl {
    return this.form.get('nonDroppedPrizes') as FormControl;
  }

  get winNotificationAmountControl(): FormControl {
    return this.form.get('winNotificationAmount') as FormControl;
  }

  private initForm(): FormGroup {
    return this.fb.group({
      payouts: [],
      nonDroppedPrizes: ['remove'],
      winNotificationAmount: []
    });
  }
}
