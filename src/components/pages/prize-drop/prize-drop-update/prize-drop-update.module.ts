import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { GamesFormModule } from '../../../../common/components/games-form/games-form.module';
import { PrizeDropGeneralFormModule } from '../form/general/prize-drop-general-form.module';
import { PrizeDropOperatorsFormModule } from '../form/operators/prize-drop-operators-form.module';

import { PrizeDropPayoutFormModule } from '../form/payout/prize-drop-payout-form.module';
import { PrizeDropRankingModule } from '../form/ranking/prize-drop-ranking.module';
import { PrizeDropScheduleFormModule } from '../form/schedule/prize-drop-schedule-form.module';
import { PrizeDropSegmentationFormModule } from '../form/segmentation/prize-drop-segmentation-form.module';
import { PrizeDropUiModule } from '../form/ui/prize-drop-ui.module';
import { PrizeDropInfoModule } from './prize-drop-info/prize-drop-info.module';
import { PrizeDropUpdateComponent } from './prize-drop-update.component';

export const MODULES = [
  ReactiveFormsModule,
  MatTabsModule,
  MatIconModule,
  MatButtonModule,
  MatCardModule,
  FlexLayoutModule,
  PrizeDropGeneralFormModule,
  PrizeDropScheduleFormModule,
  PrizeDropPayoutFormModule,
  PrizeDropSegmentationFormModule,
  PrizeDropGeneralFormModule,
  PrizeDropInfoModule,
  PrizeDropOperatorsFormModule,
  PrizeDropUiModule,
  SwuiPagePanelModule,
  PrizeDropRankingModule,
  GamesFormModule,
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule.forChild(),
    ...MODULES,
  ],
  declarations: [
    PrizeDropUpdateComponent
  ],
})
export class PrizeDropUpdateModule {
}
