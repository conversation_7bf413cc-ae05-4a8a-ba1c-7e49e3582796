import { HttpClientTestingModule } from '@angular/common/http/testing';
import { forwardRef, NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { ActivatedRoute } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import {
  SettingsService, SwBrowserTitleService,
  SwHubAuthService,
  SwHubConfigService,
  SwuiNotificationsModule
} from '@skywind-group/lib-swui';
import { BehaviorSubject, of } from 'rxjs';
import { CurrenciesManagerService } from '../../../../common/components/currencies-manager/currencies-manager.service';
import { MockFormService, SW_FORM_SERVICE } from '../../../../common/components/games-form/form-service.model';
import { CountriesService } from '../../../../common/services/countries.service';
import { CurrencyService, MockCurrencyService } from '../../../../common/services/currency.service';
import { EntityService, MockEntityService } from '../../../../common/services/entity.service';
import { MockAuthService } from '../../../../common/services/mock-auth.service';
import { MockPrizeDropService, PrizeDropService } from '../../../../common/services/prize-drop.service';
import { PrizeDropFormService } from './prize-drop-form-service/prize-drop-form.service';
import { PrizeDropUpdateComponent } from './prize-drop-update.component';
import { MODULES } from './prize-drop-update.module';
import { JpnService, MockJpnService } from '../../../../common/services/jpn.service';
import { MatIconRegistry } from '@angular/material/icon';
import { FakeMatIconRegistry } from '@angular/material/icon/testing';


const structure = {
  path: ':',
  staticDomainId: 'test',
  status: 'normal',
  type: 'entity',
  dynamicDomainId: 'test',
  environment: 'gs1',
  isTest: false,
  key: 'test',
  merchantTypes: [],
  child: []
};

describe('PrizeDropUpdateComponent', () => {
  let component: PrizeDropUpdateComponent;
  let fixture: ComponentFixture<PrizeDropUpdateComponent>;

  const fakeActivatedRoute = {
    data: new BehaviorSubject({ structure }),
    snapshot: {
      params: {
        location: 'test/foo'
      },
      data: {
        prizeDrop: {
          assets: [],
          baseCurrency: 'CNY',
          created: '2019-09-16T11:51:25.623Z',
          description: 'test',
          filter: {
            games: [],
            currencies: [],
            brandUsers: [],
          },
          id: '5d7d4e098413e956f7713d8f',
          name: 'test',
          schedule: {
            duration: 5,
            interval: {
              daysOfWeek: [0],
              validFrom: '2019-09-14T00:00:00.000Z',
              validTo: '2020-09-14T00:00:00.000Z',
            },
            repetitionMode: 1,
            specificDates: [],
            timeZone: 'Asia/Nicosia',
            triggerAt: [1415],
          },
          settings: {
            betPoints: 1,
            winPoints: 2,
            luckyDraw: {
              enabled: true,
              players: 4,
              payoutsPerRange: [1]
            },
            payouts: [{ players: 1, payoutsPerRange: Array(3) }],
            ranges: [{ id: 1, max: 5, minQualifyAmount: 2 }],
          },
          showFeature: true,
          status: 'enabled',
          type: 'prizeDrop',
          updated: '2019-09-16T11:51:26.932Z',
        }
      }
    }
  };

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        PrizeDropUpdateComponent,
      ],
      imports: [
        NoopAnimationsModule,
        RouterTestingModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        SwuiNotificationsModule.forRoot(),
        ...MODULES,
      ],
      providers: [
        PrizeDropFormService,
        SettingsService,
        MockFormService,
        CurrenciesManagerService,
        SwBrowserTitleService,
        { provide: JpnService, useClass: MockJpnService },
        { provide: EntityService, useClass: MockEntityService },
        { provide: SwHubAuthService, useClass: MockAuthService },
        { provide: PrizeDropService, useClass: MockPrizeDropService },
        { provide: CurrencyService, useClass: MockCurrencyService },
        { provide: ActivatedRoute, useFactory: () => fakeActivatedRoute },
        { provide: SwHubConfigService, useValue: {} },
        {
          provide: SW_FORM_SERVICE,
          useExisting: forwardRef(() => MockFormService),
        },
        { provide: MatIconRegistry, useClass: FakeMatIconRegistry },
        {
          provide: CountriesService, useValue: {
            countries: of([])
          }
        }
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PrizeDropUpdateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
