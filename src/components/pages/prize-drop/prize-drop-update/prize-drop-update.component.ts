import {
  ChangeDetectionStrategy, Component, forwardRef, HostListener, On<PERSON>estroy, OnInit, ViewChild
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { MatTabGroup } from '@angular/material/tabs';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  ActionConfirmDialogComponent, SwBrowserTitleService, SwHubAuthService, SWUI_CONTROL_MESSAGES,
  SwuiIsControlInvalidService, SwuiNotificationsService
} from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { merge, Observable, ReplaySubject, Subscription, timer } from 'rxjs';
import { filter, finalize, map, switchMap, take, takeUntil } from 'rxjs/operators';

import { BaseComponent } from '../../../../common/components/base.component';
import { CurrenciesManagerService } from '../../../../common/components/currencies-manager/currencies-manager.service';
import { SW_FORM_SERVICE } from '../../../../common/components/games-form/form-service.model';
import { MESSAGE_ERRORS } from '../../../../common/constants/errors-message-list';
import { formatDate } from '../../../../common/lib/format-date';
import { CurrencyModel } from '../../../../common/models/currency.model';
import { Entity, isBrandType } from '../../../../common/models/entity';
import { GameInfo } from '../../../../common/models/game';
import { PrizeDropService } from '../../../../common/services/prize-drop.service';
import { FeatureSchedule, OperatorsBrand, SegmentationBrand } from '../../interfaces/feature';
import { PrizeDrop } from '../../interfaces/prize-drop';
import { PrizeDropFormService } from './prize-drop-form-service/prize-drop-form.service';

const MILLISECONDS_PER_DAY = 86400000;

@Component({
  selector: 'sw-prize-drop-update',
  templateUrl: './prize-drop-update.component.html',
  styleUrls: ['./prize-drop-update.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    CurrenciesManagerService,
    PrizeDropFormService,
    { provide: SW_FORM_SERVICE, useExisting: forwardRef(() => PrizeDropFormService) },
    { provide: ErrorStateMatcher, useExisting: forwardRef(() => PrizeDropFormService) },
    { provide: SwuiIsControlInvalidService, useExisting: forwardRef(() => PrizeDropFormService) },
    { provide: SWUI_CONTROL_MESSAGES, useValue: MESSAGE_ERRORS }
  ]
})
export class PrizeDropUpdateComponent extends BaseComponent implements OnInit, OnDestroy {
  readonly currencies: CurrencyModel[] = [];
  readonly brief: Entity;
  prizeDropId?: string;
  prizeDrop: PrizeDrop;
  form: FormGroup = new FormGroup({});
  schedule$: Observable<FeatureSchedule | undefined>;
  baseCurrency?: string;
  isSidebarHidden = false;
  isDuplicate = false;
  isDisabled = false;
  isPrizeDropInfoDisabled = false;
  sourceGames: GameInfo[] = [];
  isOwner: boolean;
  isBrand: boolean;
  canSave: boolean;

  @ViewChild('tabs') tabs?: MatTabGroup;

  private status: 'enabled' | 'disabled' | null = null;
  private prizeDropName = '';
  private prizeDrop$ = new ReplaySubject<PrizeDrop>(1);
  private statusUpdateSubscription = new Subscription();

  constructor( private readonly service: PrizeDropService,
               private readonly route: ActivatedRoute,
               private readonly fb: FormBuilder,
               private readonly formService: PrizeDropFormService,
               private readonly notifications: SwuiNotificationsService,
               private readonly translate: TranslateService,
               private readonly authService: SwHubAuthService,
               private readonly router: Router,
               private readonly dialog: MatDialog,
               private readonly currenciesManagerService: CurrenciesManagerService,
               protected readonly browserTitleService: SwBrowserTitleService,
  ) {
    super();
    this.hideSidebar();

    const { data: { games, currencies, prizeDrop, duplicate, brief }, params: { id } } = route.snapshot;

    this.sourceGames = games;
    this.isDuplicate = duplicate;
    this.currencies = currencies;
    this.prizeDropId = id;
    this.brief = brief;
    this.prizeDrop = prizeDrop || { type: 'prizeDrop', status: 'disabled' };
    this.isOwner = !id || this.authService.isSuperAdmin || this.brief.id === prizeDrop.operators.owner?.id;
    this.isBrand = isBrandType(brief);
    this.canSave = this.isOwner || this.isBrand || this.isDuplicate;
    this.form = this.initForm();

    this.prizeDrop$
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(t => {
        this.initValues(t);
        this.browserTitleService.setupTitles('Engagement', t?.general?.name || '');
      });

    this.prizeDrop$.next(prizeDrop);

    this.schedule$ = merge(
      this.scheduleControl.valueChanges,
      this.prizeDrop$.pipe(
        map(t => t ? t.schedule : null)
      )
    );

    if (duplicate) {
      this.prizeDropId = undefined;
      this.prizeDrop.status = 'disabled';
      this.prizeDrop.active = false;
      this.prizeDrop.modifiedBy = undefined;
      this.prizeDrop.activatedAt = undefined;
      delete this.prizeDrop.updated;
      delete this.prizeDrop.created;
      delete this.prizeDrop.lastExecution;
      delete this.prizeDrop.nextExecution;
    }

  }

  @HostListener('window:resize') onResize() {
    this.hideSidebar();
  }

  ngOnInit(): void {
    this.generalControl.valueChanges.pipe(
      takeUntil(this.destroyed),
    ).subscribe(() => {
      const { general: { name } } = this.form.getRawValue();
      this.prizeDropName = name || '';
    });

    this.currenciesManagerService.selectedPayoutCurrency$
      .pipe(
        filter(val => !!val),
        takeUntil(this.destroyed)
      )
      .subscribe(() => {
        if (this.tabs) {
          this.tabs.selectedIndex = 3;
        }
      });

    this.segmentationControl.valueChanges.pipe(
      map(() => {
        const { segmentation: { brands }, operators } = this.form.getRawValue();
        return [brands, operators];
      }),
      filter(( [brands] ) => {
        return !!brands.length;
      }),
      takeUntil(this.destroyed),
    ).subscribe(( [brands, operators] ) => {
      const operatorsSet = new Set(operators.brands.map(( el: OperatorsBrand ) => el.id));
      (brands as SegmentationBrand[]).forEach(brand => {
        if (!operatorsSet.has(brand.id)) {
          operators.brands.push({ id: brand.id });
        }
      });
      this.operatorsControl.patchValue(operators, { emitEvent: false });
    });

    this.operatorsControl.valueChanges.pipe(
      map(() => {
        const { operators, segmentation } = this.form.getRawValue();
        const segmentationBrands = segmentation && segmentation.brands ? segmentation.brands.map(( el: SegmentationBrand ) => el.id) : [];
        const segmentationSet = new Set<string>(segmentationBrands);
        const operatorsBrands = operators && operators.brands ? operators.brands.map(( el: OperatorsBrand ) => el.id) : [];
        const operatorsSet = new Set<string>(operatorsBrands);
        return [segmentationSet, operatorsSet, operators, segmentation];
      }),
      filter(( [segmentationSet, operatorsSet] ) => {
        return !isEqualSets(segmentationSet, operatorsSet);
      }),
      takeUntil(this.destroyed)
    ).subscribe(( [segmentationSet, operatorsSet, operators, segmentation] ) => {
      segmentation.brands.forEach(( brand: SegmentationBrand, index: number ) => {
        if (!operatorsSet.has(brand.id)) {
          segmentation.brands.splice(index, 1);
        }
      });
      operators.brands.forEach(( brand: OperatorsBrand ) => {
        if (!segmentationSet.has(brand.id)) {
          segmentation.brands.push({ id: brand.id, players: [] });
        }
      });
      this.segmentationControl.patchValue(segmentation, { emitEvent: false });
    });
  }

  ngOnDestroy() {
    super.ngOnDestroy();
    this.formService.ranges = null;
    this.formService.baseCurrency = '';
    this.formService.formSubmitted = false;
    this.formService.baseCurrencyControlValue = [];
    this.currenciesManagerService.selectedCurrencies = [];
    this.currenciesManagerService.selectedPayoutCurrency = undefined;
    this.formService.segmentationBrands = [];
  }

  onSubmit() {
    // on disabled status form is not valid and not invalid
    if (this.form.valid || this.form.disabled) {
      if (!this.isDuplicate && this.prizeDrop && this.prizeDrop.status === 'running') {
        const operatorsWithAllPlayers = getOperatorsWithAllPlayers(this.form.getRawValue());
        const confirmText = !!operatorsWithAllPlayers.length ?
          this.translate.instant('PRIZE_DROP.NOTIFICATIONS.makeChangesWillAffectAllPlayers',
            { brands: operatorsWithAllPlayers.join(', ') }) :
          this.translate.instant('PRIZE_DROP.NOTIFICATIONS.makeChanges');
        this.dialog.open(ActionConfirmDialogComponent, {
          data: {
            action: {
              confirmText,
            }
          }
        }).afterClosed().subscribe(confirmed => {
            if (confirmed) {
              this.updatePrizeDrop();
            }
          }
        );
      } else {
        this.updatePrizeDrop();
      }
    } else {
      this.formService.formSubmitted = true;
      this.switchToFirstInvalidTab();
    }
  }

  updatePrizeDrop() {
    const {
      general,
      ranking,
      configuration,
      schedule,
      games,
      operators,
      segmentation,
      assets
    } = this.form.getRawValue();
    const data: PrizeDrop = {
      status: 'disabled',
      ...this.prizeDrop,
      general,
      ranking,
      configuration,
      schedule,
      games,
      operators,
      segmentation,
      assets,
      ...(this.status ? { status: this.status } : {})
    };

    if (this.prizeDrop && this.prizeDrop.status === 'running') {
      data.general = { ...this.prizeDrop.general, ...general };
      data.configuration = JSON.parse(JSON.stringify(this.prizeDrop.configuration));
    }

    if (this.prizeDrop && this.prizeDrop.status === 'scheduled') {
      data.configuration = JSON.parse(JSON.stringify(this.prizeDrop.configuration));
    }

    if (this.prizeDrop && this.isDuplicate && !this.authService.isSuperAdmin) {
      data.operators.owner = null;
    }

    const action = ( prizeDrop: PrizeDrop ): Observable<PrizeDrop> => {
      if (this.prizeDropId) {
        return this.service.update(this.prizeDropId, prizeDrop);
      }
      return this.service.create(prizeDrop);
    };
    this.isDisabled = true;
    action(data).pipe(
      take(1),
      finalize(() => this.isDisabled = false)
    ).subscribe(( item: PrizeDrop ) => {
      if (item) {
        let id = item.id;

        this.prizeDrop$.next(item);

        if (!this.prizeDropId) {
          this.router.navigate(['./pages/prize-drop/edit', id]);
        }

        const message = this.prizeDropId ?
          this.translate.instant('PRIZE_DROP.NOTIFICATIONS.updatedPrizeDropMessage', { name: data.general.name }) :
          this.translate.instant('PRIZE_DROP.NOTIFICATIONS.createdPrizeDropMessage');
        this.notifications.success(message);

        this.isDisabled = true;

        if (this.tabs && !this.prizeDropId) {
          this.tabs.selectedIndex = 0;
        }
      }
    });
  }

  onChangeStatus( status: 'enabled' | 'disabled' ) {
    this.status = status;

    this.prizeDropId = this.route.snapshot.params.id;

    if (this.prizeDropId) {
      this.service.get(this.prizeDropId as string)
        .subscribe(prizeDrop => {
          this.prizeDrop$.next(prizeDrop);
        });
    }
  }

  private hideSidebar() {
    this.isSidebarHidden = window.innerWidth <= 1300;
  }

  get topPanelTitle(): string {
    return this.prizeDropId ? ('Edit ' + this.prizeDropName) : 'Create Prize Drop ' + this.prizeDropName;
  }

  get generalControl(): FormControl {
    return this.form.get('general') as FormControl;
  }

  private initForm(): FormGroup {
    return this.fb.group({
      general: ['', Validators.required],
      schedule: ['', Validators.required],
      ranking: ['', Validators.required],
      configuration: ['', Validators.required],
      games: ['', Validators.required],
      operators: ['', Validators.required],
      segmentation: ['', Validators.required],
      assets: []
    });
  }

  private initValues( prizeDrop: PrizeDrop ): void {
    this.prizeDrop = prizeDrop || { type: 'prizeDrop', status: 'disabled' };

    this.prizeDropName = prizeDrop && prizeDrop.general && prizeDrop.general.name ? prizeDrop.general.name : '';

    if (this.isDuplicate) {
      this.prizeDropId = undefined;
      this.prizeDrop.active = false;
      this.prizeDrop.status = 'disabled';

      if (!this.authService.isSuperAdmin) {
        this.prizeDrop.operators.owner = null;
      }

      delete this.prizeDrop.id;
      delete this.prizeDrop.created;
      delete this.prizeDrop.updated;
      delete this.prizeDrop.activated;
    }

    this.form.enable();
    this.form.patchValue(prizeDrop || {});

    if (this.prizeDropId &&
      !this.isDuplicate &&
      !this.authService.isSuperAdmin &&
      this.prizeDrop.operators.owner !== null &&
      this.prizeDrop.operators.owner.id !== this.brief.id) {
      this.isPrizeDropInfoDisabled = true;
      this.form.disable();
    }

    if (prizeDrop && (prizeDrop.status === 'scheduled' || prizeDrop.status === 'running') && !this.isDuplicate) {
      this.scheduleControl.disable();
      this.configurationControl.disable();
      this.rankingControl.disable();
    }

    if (prizeDrop && prizeDrop.status === 'expired' && !this.isDuplicate) {
      this.form.disable();
    }

    if (prizeDrop && prizeDrop.status === 'running' && !this.isDuplicate && !prizeDrop.nextExecution) {
      this.segmentationControl.disable();
    }

    this.listenStatus();
  }

  get scheduleControl(): FormControl {
    return this.form.get('schedule') as FormControl;
  }

  get configurationControl(): FormControl {
    return this.form.get('configuration') as FormControl;
  }

  get rankingControl(): FormControl {
    return this.form.get('ranking') as FormControl;
  }

  get segmentationControl(): FormControl {
    return this.form.get('segmentation') as FormControl;
  }

  get operatorsControl(): FormControl {
    return this.form.get('operators') as FormControl;
  }

  get assetsControl(): FormControl {
    return this.form.get('assets') as FormControl;
  }

  private listenStatus(): void {
    this.statusUpdateSubscription.unsubscribe();

    if (!this.prizeDrop.active || !this.prizeDrop.id) {
      return;
    }

    const nextUpdate = this.prizeDrop.nextExecution
      ? formatDate(this.prizeDrop.nextExecution, '', '', false, false)
      : (this.prizeDrop.schedule as FeatureSchedule).nextCheck;

    if (!nextUpdate) {
      return;
    }

    const time = moment(nextUpdate).valueOf() - moment.utc().valueOf() + 10000;

    if (time < 0 || time > MILLISECONDS_PER_DAY) {
      return;
    }

    this.statusUpdateSubscription = timer(time)
      .pipe(
        switchMap(() => this.service.get(this.prizeDrop.id as string)),
      )
      .subscribe(prizeDrop => {
        this.prizeDrop$.next(prizeDrop);
      });
  }

  private switchToFirstInvalidTab() {
    if (this.tabs) {
      const controls = Object.keys(this.form.controls);
      const firstInvalid = controls.find(key => this.form.controls[key].invalid);
      if (firstInvalid) {
        this.tabs.selectedIndex = controls.indexOf(firstInvalid);
      }
    }
  }
}

export function getOperatorsWithAllPlayers( feature: PrizeDrop ): string[] {
  let operatorsBrands: OperatorsBrand[] = feature.operators?.brands || [];
  let segmentationBrands: SegmentationBrand[] = feature.segmentation?.brands || [];
  let operatorsWithAllPlayers: string[] = [];

  segmentationBrands?.forEach(( segmentationBrand: SegmentationBrand ) => {
    if (!segmentationBrand.players.length) {
      const operator = operatorsBrands.find(( brand: OperatorsBrand ) => brand.id === segmentationBrand.id);
      if (operator) {
        operatorsWithAllPlayers.push(operator.title || operator.id);
      }
    }
  });
  return operatorsWithAllPlayers;
}

function isEqualSets( as: Set<string>, bs: Set<string> ): boolean {
  if (as.size !== bs.size) {
    return false;
  }
  for (let a of as) {
    if (!bs.has(a)) {
      return false;
    }
  }
  return true;
}
