<lib-swui-page-panel [title]="topPanelTitle" [back]="true" backUrl="/pages/prize-drop"></lib-swui-page-panel>

<div class="prize-drop-edit">
  <mat-tab-group #tabs class="prize-drop-edit__main" [formGroup]="form" animationDuration="0ms">

    <mat-tab>
      <ng-template mat-tab-label>General</ng-template>
      <sw-prize-drop-general-form formControlName="general"></sw-prize-drop-general-form>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>Schedule</ng-template>
      <sw-prize-drop-schedule-form
        formControlName="schedule"
        [isDuplicate]="isDuplicate"
        [status]="prizeDrop.status">
      </sw-prize-drop-schedule-form>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>Qualification</ng-template>
      <sw-prize-drop-ranking [currencies]="currencies" formControlName="ranking"></sw-prize-drop-ranking>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>Payout</ng-template>
      <sw-prize-drop-payout-form
        formControlName="configuration"
        [schedule]="schedule$ | async"
      >
      </sw-prize-drop-payout-form>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>Games</ng-template>
      <sw-games-form formControlName="games" [sourceGames]="sourceGames"></sw-games-form>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>Operators</ng-template>
      <sw-prize-drop-operators-form formControlName="operators"></sw-prize-drop-operators-form>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>Segmentation</ng-template>
      <sw-prize-drop-segmentation-from [currencies]="currencies"
                                       [canPlayersEdit]="canSave"
                                       [status]="prizeDrop?.status"
                                       [initialCurrencies]="prizeDrop?.segmentation?.currencies"
                                       [isDuplicate]="isDuplicate"
                                       formControlName="segmentation">
      </sw-prize-drop-segmentation-from>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>UI</ng-template>
      <sw-prize-drop-ui [formControl]="assetsControl"></sw-prize-drop-ui>
    </mat-tab>

    <mat-tab *ngIf="isSidebarHidden">
      <ng-template mat-tab-label>Info</ng-template>
      <div class="mat-card">
        <sw-prize-drop-info
          [prizeDrop]="prizeDrop"
          [schedule]="schedule$ | async"
          [id]="prizeDropId"
          [disabled]="isPrizeDropInfoDisabled"
          (status)="onChangeStatus($event)">
        </sw-prize-drop-info>
      </div>
    </mat-tab>

  </mat-tab-group>

  <div class="prize-drop-edit__sidebar" *ngIf="!isSidebarHidden">
    <sw-prize-drop-info [prizeDrop]="prizeDrop"
                        [schedule]="schedule$ | async"
                        [id]="prizeDropId"
                        [disabled]="isPrizeDropInfoDisabled"
                        (status)="onChangeStatus($event)">
    </sw-prize-drop-info>
    <div class="padding-top32" [ngClass]="{'sidebar-hidden': isSidebarHidden}">
      <div class="controls">
        <button mat-flat-button class="controls__button link" routerLink="/pages/prize-drop">
          {{ 'COMMON.ACTIONS.cancel' | translate }}
        </button>
        <button mat-flat-button
                class="controls__button"
                [disabled]="(prizeDrop?.status === 'expired' && !isDuplicate) || isDisabled || !canSave"
                [color]="'primary'"
                (click)="onSubmit()">
          {{ 'COMMON.ACTIONS.save' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>

<div *ngIf="isSidebarHidden" class="footer" [ngClass]="{'sidebar-hidden': isSidebarHidden}">
  <div class="controls">
    <button mat-flat-button class="controls__button link" routerLink="/pages/prize-drop">
      {{ 'COMMON.ACTIONS.cancel' | translate }}
    </button>
    <button mat-flat-button
            class="controls__button"
            [disabled]="(prizeDrop?.status === 'expired' && !isDuplicate) || isDisabled || !canSave"
            [color]="'primary'"
            (click)="onSubmit()">
      {{ 'COMMON.ACTIONS.save' | translate }}
    </button>
  </div>
</div>
