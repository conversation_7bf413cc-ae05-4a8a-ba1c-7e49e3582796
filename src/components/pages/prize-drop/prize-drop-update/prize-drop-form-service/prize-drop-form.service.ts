import { Injectable } from '@angular/core';
import { AbstractControl } from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import { SwuiIsControlInvalidService } from '@skywind-group/lib-swui';
import { BehaviorSubject, Observable } from 'rxjs';
import { SegmentationBrand } from '../../../interfaces/feature';
import { PrizeDropRange } from '../../../interfaces/prize-drop';

export interface FormPrizeDropRange {
  [key: string]: PrizeDropRange[];
}


@Injectable()
export class PrizeDropFormService implements SwuiIsControlInvalidService, ErrorStateMatcher  {
  private _formSubmitted = new BehaviorSubject<boolean>(false);
  private _baseCurrency = new BehaviorSubject<string>('');
  private _ranges$ = new BehaviorSubject<FormPrizeDropRange | null>(null);
  private _baseCurrencyControlValue$ = new BehaviorSubject<PrizeDropRange[]>([]);
  private _segmentationBrands$ = new BehaviorSubject<SegmentationBrand[]>([]);

  set segmentationBrands( val: SegmentationBrand[] ) {
    this._segmentationBrands$.next(val);
  }

  get segmentationBrands$(): Observable<SegmentationBrand[]> {
    return this._segmentationBrands$ as Observable<SegmentationBrand[]>;
  }

  set baseCurrencyControlValue( val: PrizeDropRange[] ) {
    this._baseCurrencyControlValue$.next(val);
  }

  set ranges( val: FormPrizeDropRange | null ) {
    this._ranges$.next(val);
  }

  get ranges$(): Observable<FormPrizeDropRange | null> {
    return this._ranges$ as Observable<FormPrizeDropRange>;
  }

  set baseCurrency( val: string ) {
    this._baseCurrency.next(val || '');
  }

  get baseCurrency$(): Observable<string> {
    return this._baseCurrency as Observable<string>;
  }

  set formSubmitted( val: boolean ) {
    this._formSubmitted.next(val);
  }

  get formSubmitted(): boolean {
    return this._formSubmitted.value;
  }

  get formSubmitted$(): Observable<boolean> {
    return this._formSubmitted as Observable<boolean>;
  }

  isErrorState( control: AbstractControl | null ): boolean {
    if (!control) {
      return false;
    }
    if (this.formSubmitted) {
      return control.invalid;
    }
    return false;
  }

  isControlInvalid( control: AbstractControl | null ): boolean {
    return this.isErrorState(control);
  }
}
