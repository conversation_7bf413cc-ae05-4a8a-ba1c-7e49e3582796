import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSlideToggle, MatSlideToggleChange } from '@angular/material/slide-toggle';
import { TranslateService } from '@ngx-translate/core';
import {
  ActionConfirmDialogComponent, SettingsService, SwHubConfigService, SwuiNotificationsService
} from '@skywind-group/lib-swui';
import { AppSettings } from '@skywind-group/lib-swui/services/settings/app-settings';
import 'moment-timezone';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { map, take, takeUntil, tap } from 'rxjs/operators';
import { getTimeZones, REPORT_ID } from '../../../../../app.constants';
import { BaseInfoComponent } from '../../../../../common/components/base-info.component';
import { SchemaFieldMapItem } from '../../../../../common/models/schema-field-map-item';
import { PrizeDropService } from '../../../../../common/services/prize-drop.service';
import { FeatureSchedule, STATUS_MAP } from '../../../interfaces/feature';
import { getScheduleRuns, PrizeDrop } from '../../../interfaces/prize-drop';
import { getOperatorsWithAllPlayers } from '../prize-drop-update.component';

@Component({
  selector: 'sw-prize-drop-info',
  templateUrl: './prize-drop-info.component.html',
})
export class PrizeDropInfoComponent extends BaseInfoComponent implements OnInit {

  @ViewChild('toggle') toggle: MatSlideToggle | undefined;

  @Output() status = new EventEmitter<'enabled' | 'disabled'>();

  @Input()
  set prizeDrop( val: PrizeDrop | undefined ) {
    if (!val) {
      return;
    }
    this._prizeDrop = val;
    this.setPrizeDropStatus(this._prizeDrop);
  }

  get prizeDrop(): PrizeDrop | undefined {
    return this._prizeDrop;
  }

  @Input()
  set schedule( val: FeatureSchedule | undefined ) {
    if (!val) {
      return;
    }
    this.schedule$.next(val);
  }

  @Input() id?: string;

  @Input()
  set count( value: number ) {
    if (typeof value === 'undefined') {
      return;
    }
    this.count$.next(value);
  }

  @Input() disabled = false;

  readonly scheduleRuns$: Observable<string[]>;
  prizeDropReportUrl = '';
  analyticsUrl: string | undefined;

  prizeDropStatus: SchemaFieldMapItem | undefined;

  private _prizeDrop: PrizeDrop | undefined;
  private _userTimeZone: string | undefined;

  private readonly schedule$ = new BehaviorSubject<FeatureSchedule | null>(null);
  private readonly count$ = new BehaviorSubject<number>(6);

  constructor( { hubs }: SwHubConfigService,
               private readonly service: PrizeDropService,
               private readonly dialog: MatDialog,
               private readonly notifications: SwuiNotificationsService,
               private readonly translate: TranslateService,
               protected readonly settingsService: SettingsService,
  ) {
    super(settingsService);
    this.scheduleRuns$ = combineLatest([this.schedule$, this.count$]).pipe(
      map(( [schedule, count] ) => schedule ? getScheduleRuns(schedule, count) : [])
    );
    if (hubs && hubs.analytics) {
      const { url } = hubs.analytics;
      this.analyticsUrl = url;
    }
  }

  ngOnInit(): void {
    this.initDates(this.prizeDrop);
    this.prizeDropReportUrl = `${this.analyticsUrl}/${REPORT_ID.prizeDrops}/${this.prizeDrop?.id}`;

    this.settingsService.settings.pipe(
      tap(( data: AppSettings ) => this._userTimeZone = data.timezoneName),
      takeUntil(this.destroyed)
    ).subscribe();
  }

  get disableToggle() {
    if (this.prizeDrop) {
      if (!!this.prizeDrop.id && this.prizeDrop.status !== 'expired' && !this.disabled) {
        return false;
      }
    }
    return true;
  }

  get timeZone(): string | undefined {
    const value = this.schedule$.value;
    return value ? value.timeZone : undefined;
  }

  get userTimeZone(): string | undefined {
    return this._userTimeZone;
  }

  triggerActive( event: MatSlideToggleChange ) {
    if (this.id) {
      const id = this.id;

      if (this.prizeDrop) {
        const operatorsWithAllPlayers = getOperatorsWithAllPlayers(this.prizeDrop);
        const confirmText = !!operatorsWithAllPlayers.length ?
          this.translate.instant('PRIZE_DROP.NOTIFICATIONS.makeChangesWillAffectAllPlayers',
            { brands: operatorsWithAllPlayers.join(', ') }) :
          this.translate.instant('PRIZE_DROP.NOTIFICATIONS.makeChanges');

        this.dialog.open(ActionConfirmDialogComponent, {
          data: {
            action: {
              confirmText,
            },
            closeButtonText: 'DIALOG.no',
            confirmButtonText: 'DIALOG.yes',
          }
        }).afterClosed()
          .pipe(take(1))
          .subscribe(confirmed => {
              if (confirmed) {
                this.updatePrizeDropStatus(id, event.checked);
              } else {
                if (this.toggle) {
                  this.toggle.checked = !this.toggle.checked;
                }
              }
            }
          );
      }
    }
  }

  updatePrizeDropStatus( id: string, isActive: boolean ) {
    this.service.patch(id, { active: isActive })
      .pipe(take(1))
      .subscribe(( prizeDrop: PrizeDrop ) => {
          if (this.prizeDrop) {
            this.setPrizeDropStatus(prizeDrop);
          }

          this.notifications.success(
            this.translate.instant('PRIZE_DROP.NOTIFICATIONS.statusChangedMessage'),
          );

          const status = this.toggle && this.toggle.checked
            ? 'enabled'
            : 'disabled';
          this.status.emit(status);
        },
        () => {
          if (this.toggle) {
            this.toggle.checked = !isActive;
          }
        });
  }

  getTimeZoneTitle( id: string ): string | undefined {
    return getTimeZones()?.find(item => item.id === id)?.text;
  }

  private setPrizeDropStatus( val: PrizeDrop ) {
    this.prizeDropStatus = STATUS_MAP.find(( item: SchemaFieldMapItem ) => item.id === val.status);
  }
}
