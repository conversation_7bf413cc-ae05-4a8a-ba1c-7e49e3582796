<div class="sd">
  <div class="sd__item">
    <div class="sd__title">Prize drop status</div>
    <ul class="sd__list sd-list">
      <li class="sd-list__item">
        <div class="sd-list__title">Active</div>
        <div class="sd-list__content">
          <mat-slide-toggle
            #toggle
            [disabled]="disableToggle"
            [checked]="prizeDrop?.active"
            (change)="triggerActive($event)">
          </mat-slide-toggle>
        </div>
      </li>

      <li class="sd-list__item">
        <div class="sd-list__title">Status</div>
        <div class="sd-list__content">
          <div class="sw-chip" [ngClass]="prizeDropStatus?.class">
            {{ prizeDropStatus?.title }}
          </div>
        </div>
      </li>

      <li class="sd-list__item">
        <div class="sd-list__title">Last Run</div>
        <div class="sd-list__content">
          {{ prizeDrop?.lastExecution | formatDate : 'MMM DD, YYYY HH:mm' : timeZone }}
        </div>
      </li>
    </ul>
  </div>

  <div class="sd__item">
    <div class="sd__title">Schedule</div>
    <ul class="sd__list sd-list">
      <li class="sd-list__item">
        <div class="sd-schedule" *ngIf="(scheduleRuns$ | async)?.length > 0; else tplEmptySchedule">
          <div class="sd-schedule__list">
            <div class="sd-schedule__item" *ngFor="let date of scheduleRuns$ | async">
              {{ date | formatDate : 'MMM DD, YYYY HH:mm' : timeZone : true : false }}
            </div>
          </div>
          <div>{{ ('PRIZE_DROP.FORM.SCHEDULE.timezone' | translate) + ': ' + getTimeZoneTitle(timeZone) }}</div>
          <div class="sd-schedule__hint">
            *Showing next 6 scheduled runs. Prize drop must be enabled for this schedule to run.
          </div>
        </div>
        <ng-template #tplEmptySchedule>
          <div class="sd-empty">
            <mat-icon class="sd-empty__icon">date_range</mat-icon>
            <div class="sd-empty__label">No scheduled prize drops yet</div>
          </div>
        </ng-template>
      </li>
    </ul>
  </div>

  <div class="sd__item">
    <div class="sd__title">Reports</div>
    <ul class="sd__list sd-list">
      <ng-container *ngIf="prizeDrop && prizeDrop?.lastExecution; else tplEmptyReports">
        <li class="sd-list__item">
          <a [href]=prizeDropReportUrl
             class="sd-icon-link">
            <mat-icon svgIcon="icon_report" class="sd-icon-link__icon"></mat-icon>
            <div class="sd-icon-link__label">
              Prize Drop Results
            </div>
          </a>
        </li>
      </ng-container>

      <ng-template #tplEmptyReports>
        <div class="sd-empty">
          <mat-icon svgIcon="icon_report" class="sd-empty__icon"></mat-icon>
          <div class="sd-empty__label">No reported prize drops yet</div>
        </div>
      </ng-template>

    </ul>
  </div>

  <div class="sd__item">
    <div class="sd__title">Info</div>
    <ul class="sd__list sd-list">
      <li class="sd-list__item">
        <div class="sd-list__title">{{ 'JACKPOT.FORM.INFO.featureId' | translate }}</div>
        <div class="sd-list__content">{{ prizeDrop?.id || '-' }}</div>
      </li>

      <li class="sd-list__item">
        <div class="sd-list__title">Created</div>
        <div class="sd-list__content">
          {{ prizeDrop?.created | formatDate : 'MMM DD, YYYY HH:mm' : userTimeZone : false }}
        </div>
      </li>

      <li class="sd-list__item">
        <div class="sd-list__title">Activated</div>
        <div class="sd-list__content">
          {{ (prizeDrop?.activatedAt | formatDate : 'MMM DD, YYYY HH:mm' : userTimeZone : false)  || '-' }}
        </div>
      </li>

      <li class="sd-list__item">
        <div class="sd-list__title">Modified by</div>
        <div class="sd-list__content">
          {{ prizeDrop?.modifiedBy || '-' }}
        </div>
      </li>

      <li class="sd-list__item">
        <div class="sd-list__title">Modified</div>
        <div class="sd-list__content">
          {{ prizeDrop?.updated | formatDate : 'MMM DD, YYYY HH:mm' : userTimeZone : false }}
        </div>
      </li>
    </ul>
  </div>
</div>
