import { HttpClientModule } from '@angular/common/http';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { SettingsService, SwHubConfigService, SwuiNotificationsModule } from '@skywind-group/lib-swui';
import { MockPrizeDropService, PrizeDropService } from '../../../../../common/services/prize-drop.service';

import { PrizeDropInfoComponent } from './prize-drop-info.component';
import { MODULES } from './prize-drop-info.module';
import { FakeMatIconRegistry } from '@angular/material/icon/testing';
import { MatIconRegistry } from '@angular/material/icon';

describe('PrizeDropInfoComponent', () => {
  let component: PrizeDropInfoComponent;
  let fixture: ComponentFixture<PrizeDropInfoComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        NoopAnimationsModule,
        HttpClientModule,
        RouterTestingModule,
        TranslateModule.forRoot(),
        SwuiNotificationsModule.forRoot(),
        ...MODULES,
      ],
      declarations: [PrizeDropInfoComponent],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        { provide: PrizeDropService, useClass: MockPrizeDropService },
        SettingsService,
        { provide: SwHubConfigService, useValue: {} },
        { provide: MatIconRegistry, useClass: FakeMatIconRegistry }
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PrizeDropInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
