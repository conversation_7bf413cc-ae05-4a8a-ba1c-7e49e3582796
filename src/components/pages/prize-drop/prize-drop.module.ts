import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { GameService } from '../../../common/services/game.service';
import { PrizeDropService } from '../../../common/services/prize-drop.service';
import { PrizeDropUpdateModule } from './prize-drop-update/prize-drop-update.module';
import { PrizeDropsListModule } from './prize-drops-list/prize-drops-list.module';
import { PrizeDropRoutingModule } from './prize-drop-routing.module';
import { PrizeDropComponent } from './prize-drop.component';

@NgModule({
  imports: [
    CommonModule,
    PrizeDropRoutingModule,
    PrizeDropsListModule,
    PrizeDropUpdateModule,
  ],
  providers: [
    PrizeDropService,
    GameService,
  ],
  declarations: [
    PrizeDropComponent,
  ],
})
export class PrizeDropModule {
}
