import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PERMISSIONS_NAMES } from '@skywind-group/lib-swui';
import { CurrenciesResolver } from '../../../common/services/currency.service';
import { GamesResolver } from '../../../common/services/game.service';
import { BriefResolver } from '../../../common/services/resolvers/brief.resolver';
import { JackpotConfigurationResolver } from '../../../common/services/resolvers/jackpot-configuration.resolver';
import { JackpotResolver } from '../../../common/services/resolvers/jackpot.resolver';
import { JackpotListComponent } from './jackpot-list/jackpot-list.component';
import { JackpotUpdateComponent } from './jackpot-update/jackpot-update.component';
import { JackpotComponent } from './jackpot.component';

export const routes: Routes = [
  {
    path: '',
    component: JackpotComponent,
    children: [
      {
        path: '',
        component: JackpotListComponent,
        resolve: {
          brief: BriefResolver,
        },
        data: {
          permissions: [PERMISSIONS_NAMES.GRANTED_ALL],
          title: 'Jackpots'
        },
      },
      {
        path: 'create',
        component: JackpotUpdateComponent,
        resolve: {
          currencies: CurrenciesResolver,
          games: GamesResolver,
          configuration: JackpotConfigurationResolver,
        },
        data: {
          permissions: [PERMISSIONS_NAMES.GRANTED_ALL],
          title: 'Jackpots - Create'
        },
      },
      {
        path: 'clone/:id',
        component: JackpotUpdateComponent,
        resolve: {
          currencies: CurrenciesResolver,
          jackpot: JackpotResolver,
          brief: BriefResolver,
          games: GamesResolver,
          configuration: JackpotConfigurationResolver,
        },
        data: {
          duplicate: true,
          permissions: [PERMISSIONS_NAMES.GRANTED_ALL],
          title: 'Jackpots - Clone'
        },
      },
      {
        path: 'edit/:id',
        component: JackpotUpdateComponent,
        resolve: {
          currencies: CurrenciesResolver,
          jackpot: JackpotResolver,
          brief: BriefResolver,
          games: GamesResolver,
          configuration: JackpotConfigurationResolver,
        },
        data: {
          permissions: [PERMISSIONS_NAMES.GRANTED_ALL],
          title: 'Jackpots - Edit'
        },
      }
    ]
  }
];

@NgModule({
  imports: [
    RouterModule.forChild(routes),
  ],
  exports: [
    RouterModule,
  ],
  providers: [
    BriefResolver,
    JackpotResolver,
    JackpotConfigurationResolver
  ]
})
export class JackpotRoutingModule {
}
