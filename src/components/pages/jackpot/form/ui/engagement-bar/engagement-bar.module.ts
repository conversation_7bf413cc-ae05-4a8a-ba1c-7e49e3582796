import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { TranslationsManagerModule } from '../translations-manager/translations-manager.module';
import { UploadImageModule } from '../upload-image/upload-image.module';
import { EngagementBarComponent } from './engagement-bar.component';


@NgModule({
  declarations: [
    EngagementBarComponent
  ],
  exports: [
    EngagementBarComponent
  ],
  imports: [
    CommonModule,
    UploadImageModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    TranslationsManagerModule,
    TranslateModule,
    FlexModule,
  ]
})
export class EngagementBarModule { }
