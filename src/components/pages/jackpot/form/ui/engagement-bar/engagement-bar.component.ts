import { Component, forwardRef, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  ControlValueAccessor, FormBuilder, FormControl, FormGroup, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors, Validators
} from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { DEFAULT_IMAGES } from '../../../../../../app.constants';


@Component({
  selector: 'sw-engagement-bar',
  templateUrl: './engagement-bar.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => EngagementBarComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => EngagementBarComponent),
      multi: true
    }
  ]
})
export class EngagementBarComponent implements OnInit, OnDestroy, ControlValueAccessor {
  engagementBarDefaultLogo = DEFAULT_IMAGES.engagementBarLogoEu;
  defaultImages = DEFAULT_IMAGES;
  form: FormGroup = new FormGroup({});
  onChange: ( _: any ) => void = (() => {
  });
  isDisabled?: boolean;

  private destroy$ = new Subject();

  constructor(
    private fb: FormBuilder
  ) {
    this.form = this.initForm();
  }

  ngOnInit(): void {
    this.form.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(data => {
      this.onChange(data);
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  get imageUrlControl(): FormControl {
    return this.form.get('imageUrl') as FormControl;
  }

  get cssBarControl(): FormControl {
    return this.form.get('cssBar') as FormControl;
  }

  registerOnChange( fn: any ): void {
    this.onChange = fn;
  }

  onTouched: () => void = () => {
  };

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  writeValue( val: any ): void {
    if (!val) {
      return;
    }

    this.form.patchValue(val);
  }

  validate(): ValidationErrors | null {
    return this.form.valid ? null : { invalidForm: { valid: false } };
  }

  setDisabledState?( isDisabled: boolean ): void {
    this.isDisabled = isDisabled;
    isDisabled ? this.form.disable({ emitEvent: false }) : this.form.enable();
  }

  private initForm(): FormGroup {
    return this.fb.group({
      imageUrl: [null, Validators.required],
      cssBar: ['']
    });
  }
}
