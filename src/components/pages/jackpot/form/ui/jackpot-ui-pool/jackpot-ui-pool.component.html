<h3 class="no-margin-top mat-title margin-bottom24">Jackpot UI</h3>
<form [formGroup]="form">
  <div class="margin-bottom20">
    <sw-translations-manager [formControl]="infoControl"
                             [component]="childFormComponent"
                             [submitted]="submitted"
                             [withoutDefault]="true"
                             [isDisabled]="isDisabled">
      <sw-jackpot-ui-pool-info
        #childFormComponent
        [jackpotType]="typeControl?.value"
        [templateType]="templateType"
        [isDisabled]="isDisabled">
      </sw-jackpot-ui-pool-info>
    </sw-translations-manager>
  </div>
  <div fxLayout.lt-sm="column" fxLayout="row" fxLayoutAlign="start center" class="margin-bottom16">
    <div fxFlex.lt-sm="100" fxFlex="145px" fxFlexAlign="center">Priority</div>
    <mat-form-field fxFlex.lt-sm="100" fxFlex="200px" appearance="outline" class="no-field-padding">
      <lib-swui-select
        [formControl]="priorityControl"
        [data]="priorities"
        [disableEmptyOption]="true">
      </lib-swui-select>
    </mat-form-field>
  </div>

  <div fxLayout.lt-sm="column" fxLayout="row">
    <div fxFlex.lt-sm="100" fxFlex="145px" style="padding-top: 14px">Jackpot color</div>
    <mat-form-field fxFlex.lt-sm="100" fxFlex="200px" appearance="outline">
      <input matInput type="text" [formControl]="colorControl" placeholder="eg. #cdcdcd">
      <mat-error *ngIf="colorControl?.hasError('invalidColorHexFormat')">
        {{ 'VALIDATION.invalidColorHexFormat' | translate }}
      </mat-error>
    </mat-form-field>
  </div>

  <div fxLayout.lt-sm="column" fxLayout="row">
    <div fxFlex.lt-sm="100" fxFlex="145px">Heat animation color</div>
    <mat-form-field fxFlex.lt-sm="100" fxFlex="200px" appearance="outline">
      <input matInput type="text" [formControl]="animationColorControl" placeholder="eg. #cdcdcd">
      <mat-error *ngIf="animationColorControl?.hasError('invalidColorHexFormat')">
        {{ 'VALIDATION.invalidColorHexFormat' | translate }}
      </mat-error>
    </mat-form-field>
  </div>
  <div class="jp-pool-tickup">
    <div class="jp-pool-tickup__header">
      {{'JACKPOT.FORM.UI.tickupTitle' | translate}}
      <div class="jp-pool-tickup__toggle">
        <mat-slide-toggle
          [disabled]="isDisabled"
          [checked]="!!tickupControl.value"
          (change)="onToggleTickup($event)">
        </mat-slide-toggle>
      </div>
    </div>
    <div class="jp-pool-tickup__note">
      {{'JACKPOT.FORM.UI.tickupNote' | translate}}
    </div>
    <div class="jp-pool-tickup__body" *ngIf="tickupControl.value">
      <sw-jackpot-ui-pool-tickup [formControl]="tickupControl"></sw-jackpot-ui-pool-tickup>
    </div>
  </div>

</form>
