import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { filter, takeUntil, tap } from 'rxjs/operators';
import { DEFAULT_IMAGES } from '../../../../../../../app.constants';

import { BaseComponent } from '../../../../../../../common/components/base.component';
import { JACKPOT_NAMES } from '../../../../../interfaces/jackpot';

@Component({
  selector: 'sw-jackpot-ui-pool-info',
  templateUrl: './jackpot-ui-pool-info.component.html',
  styleUrls: ['./jackpot-ui-pool-info.component.scss']
})
export class JackpotUiPoolInfoComponent extends BaseComponent implements OnInit, OnD<PERSON>roy {
  defaultLogo: string | undefined;
  defaultImages = DEFAULT_IMAGES;

  @Input() title?: string;

  @Input()
  set jackpotType( val: string ) {
    this._jackpotType$.next(val || '');
  }

  get jackpotType(): string {
    return this._jackpotType$.value;
  }

  @Input() isDisabled?: boolean;

  @Input() set templateType( val: string | undefined ) {
    if (!val) {
      return;
    }
    this._templateType$.next(val);
  }

  get templateType(): string | undefined {
    return this._templateType$.value;
  }

  readonly form: FormGroup;
  private _jackpotType$ = new BehaviorSubject('');
  private _templateType$ = new BehaviorSubject('');

  constructor( private fb: FormBuilder,
               private settings: TranslateService
  ) {
    super();
    this.form = this.initForm();
  }

  ngOnInit(): void {
    this.defaultLogo = this.getDefaultLogoImage(this.jackpotType, this.settings.currentLang);

    combineLatest([
      this._jackpotType$,
      this.settings.onLangChange
    ]).pipe(
      tap(( [jackpotType, language] ) => {
        const { lang } = language as LangChangeEvent;
        this.defaultLogo = this.getDefaultLogoImage(jackpotType, lang);
      }),
      takeUntil(this.destroyed)
    ).subscribe();

    this._templateType$
      .pipe(
        filter(() => !!this.textControl),
        takeUntil(this.destroyed)
      )
      .subscribe(( val: string ) => {
        if (val !== 'asia') {
          this.textControl.clearValidators();
          this.textControl.patchValue('');
        } else {
          this.textControl.setValidators(Validators.required);
          this.textControl.markAsUntouched();
        }
        this.textControl.updateValueAndValidity();
      });

    if (this.isDisabled) {
      this.form.disable({ emitEvent: false });
    }
  }

  initForm( val?: any ): FormGroup {
    const title = this.getJackpotTitle(this.jackpotType);

    const group = this.fb.group({
      id: [''],
      logo: [],
      name: [title, Validators.required],
      text: [''],
    });
    if (val) {
      group.patchValue(val);
    }
    return group;
  }

  getJackpotTitle( jackpot: any ): string {
    const jp = JACKPOT_NAMES.find(el => el.id === jackpot);
    return jp ? jp.title : '';
  }

  get logoControl(): FormControl {
    return this.form.get('logo') as FormControl;
  }

  get nameControl(): FormControl {
    return this.form.get('name') as FormControl;
  }

  get textControl(): FormControl {
    return this.form.get('text') as FormControl;
  }

  setValue( val: any ) {
    if (val) {
      this.form.patchValue({ logo: '', name: '', text: '', ...val });
    }
  }

  getDefaultLogoImage( jackpotType: string, currentLanguage?: string ): string {
    switch (jackpotType) {
      case 'daily': {
        return DEFAULT_IMAGES.daily;
      }
      case 'hourly': {
        return DEFAULT_IMAGES.hourly;
      }
      case 'grand': {
        return DEFAULT_IMAGES.mega;
      }
      case 'minor': {
        return currentLanguage === 'en' ?
          DEFAULT_IMAGES.minorEng :
          DEFAULT_IMAGES.minorCn;
      }
      case 'major': {
        return currentLanguage === 'en' ?
          DEFAULT_IMAGES.majorEng :
          DEFAULT_IMAGES.majorCn;
      }
      case 'mega': {
        return currentLanguage === 'en' ?
          DEFAULT_IMAGES.grandEng :
          DEFAULT_IMAGES.grandCn;
      }
      default:
        return '';
    }
  }
}
