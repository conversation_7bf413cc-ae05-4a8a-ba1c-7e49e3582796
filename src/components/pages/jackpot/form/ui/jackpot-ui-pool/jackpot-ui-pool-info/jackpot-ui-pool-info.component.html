<div fxLayout="column" [formGroup]="form">

  <div fxLayout.lt-sm="column" fxLayout="row">
    <div fxFlex.lt-sm="100" fxFlex="145px" style="padding-top: 14px">Display name*</div>
    <mat-form-field fxFlex.lt-sm="100" fxFlex="200px" appearance="outline">
      <input matInput type="text"
             [formControl]="nameControl"
             placeholder="{{'eg. ' + getJackpotTitle(jackpotType)}}"
             swTrimFormatter>
      <mat-error>
        <lib-swui-control-messages [control]="nameControl"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </div>

  <div fxLayout.lt-sm="column" fxLayout="row">
    <div fxFlex.lt-sm="100" fxFlex="145px" style="padding-top: 32px">
      {{'JACKPOT.UI.POOL.titleLogo' | translate}}
    </div>
    <div fxFlex.lt-sm="100" fxFlex="680px">
      <sw-image-url [formControl]="logoControl" [defaultLogoImage]="defaultLogo"
                    [defaultImages]="defaultImages"></sw-image-url>
    </div>
  </div>

  <div fxLayout.lt-sm="column" fxLayout="row" *ngIf="templateType === 'asia'">
    <div fxFlex.lt-sm="100" fxFlex="145px" style="padding-top: 16px">Popup text*</div>
    <mat-form-field fxFlex.lt-sm="100" fxFlex="500px" appearance="outline">
      <textarea
        matInput
        rows="5"
        cols="40"
        [formControl]="textControl"
        placeholder="Text description that will be shown in the info popup for this jackpot pool">
      </textarea>
      <mat-error>
        <lib-swui-control-messages [control]="textControl"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </div>

</div>
