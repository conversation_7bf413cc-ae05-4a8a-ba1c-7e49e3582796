.file-control {
  display: flex;
  flex-direction: column;
  &.removable {
    .file-control {
      &__label {
        position: relative;
        padding-right: 64px;
      }
      &__remove {
        display: flex;
      }
      &__img {
        display: block;
      }
    }
  }
  &__img {
    user-select: none;
    img {
      display: block;
      max-width: 264px;
      max-height: 140px;
      margin-left: 30px;
      margin-top: unset !important;
      &.loaded {
        margin-top: 24px;
      }
    }
  }
  &__wrapper {
    display: flex;
  }
  &__remove {
    position: absolute;
    top: 0;
    right: 0;
    display: none;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    line-height: 44px;
    background: rgba(0, 0, 0, .12);
    cursor: pointer;
  }
  &__button {
    position: relative;
    button {
      padding: 0 !important;
    }
  }
  &__error {
    position: absolute;
    bottom: -20px;
    left: 0;
    font-size: 12px;
    padding-left: 12px;
    line-height: 1.6;
  }
  &__label {
    display: block;
    height: 100%;
    width: 100%;
    margin: 0 !important;
    padding: 0 22px;
    cursor: pointer;
  }
  &__input {
    display: none;
  }
  &__hint {
    padding-top: 10px;
    margin-left: 8px;
    font-size: 14px;
  }
}

.input-row {
  padding-bottom: unset;
  margin-left: 10px;
}
