import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { MatSlideToggleChange } from '@angular/material/slide-toggle';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { filter, takeUntil } from 'rxjs/operators';

import {
  AbstractFormValueAccessor, formValueProviders
} from '../../../../../../common/lib/abstract-form-value-accessor';
import { colorHexValidator } from '../../../../../../common/lib/validators';
import { JackpotUiPool, JackpotUiPoolInfo } from '../../../../interfaces/jackpot';
import { JackpotFormService } from '../../../jackpot-update/jackpot-form-service/jackpot-form.service';


@Component({
  selector: 'sw-jackpot-ui-pool',
  templateUrl: './jackpot-ui-pool.component.html',
  styleUrls: ['./jackpot-ui-pool.component.scss'],
  providers: formValueProviders(JackpotUiPoolComponent),
})
export class JackpotUiPoolComponent extends AbstractFormValueAccessor<JackpotUiPool> implements OnInit {
  @Input()
  set templateType( type: string | undefined ) {
    if (!type) {
      return;
    }

    if (this.infoControl.value) {
      let infoControlValue = Object.entries(this.infoControl.value as { [lang: string]: JackpotUiPoolInfo })?.reduce(
        ( res: any, [key, value] ) => {
          if (type !== 'asia') {
            value.text = '';
          }

          res[key] = value;

          return res;
        }, {});

      this.infoControl.patchValue(infoControlValue);
    }

    this._templateType = type;
  }

  get templateType(): string | undefined {
    return this._templateType;
  }


  @Input() priorities: SwuiSelectOption[] = [];
  @Input() isDisabled?: boolean;
  @Input() index = 0;

  submitted: boolean | undefined;

  readonly form: FormGroup;

  private _templateType?: string;

  constructor( private readonly fb: FormBuilder,
               private readonly formService: JackpotFormService
  ) {
    super();
    this.form = this.initForm();
  }

  ngOnInit(): void {
    this.form.valueChanges.pipe(
      takeUntil(this.destroyed)
    ).subscribe(( value: any ) => {
      this.form.updateValueAndValidity({ emitEvent: false });
      if (this.onChange) {
        this.onChange(this.transformForm(value));
      }
    });

    this.formService.formSubmitted$.pipe(
      filter(( val: any ) => val),
      takeUntil(this.destroyed)
    ).subscribe(() => {
      this.submitted = true;
    });
  }

  get priorityControl(): FormControl {
    return this.form.get('priority') as FormControl;
  }

  get colorControl(): FormControl {
    return this.form.get('color') as FormControl;
  }

  get animationColorControl(): FormControl {
    return this.form.get('animationColor') as FormControl;
  }

  get infoControl(): FormControl {
    return this.form.get('info') as FormControl;
  }

  get typeControl(): FormControl {
    return this.form.get('type') as FormControl;
  }

  get tickupControl(): FormControl {
    return this.form.get('tickup') as FormControl;
  }

  onToggleTickup( event: MatSlideToggleChange ) {
    this.tickupControl.setValue(event.checked ? { interval: 20, decrease: 2 } : null);
  }

  validate(): ValidationErrors | null {
    return this.form.valid ? null : { invalidForm: { valid: false } };
  }

  protected transformValue( value: JackpotUiPool ): any {
    let result = {};
    if (value) {
      const { priority, color, animationColor, info, type, assets, tickup } = value;
      result = {
        type,
        priority: priority ? priority.toString() : (this.index + 1).toString(),
        color,
        animationColor,
        info,
        assets,
        tickup
      };
    }
    return result;
  }

  protected transformForm( value: any ): JackpotUiPool | undefined {
    let result = {} as JackpotUiPool;

    if (value) {
      const { type, priority, color, animationColor, info, assets, tickup } = value;
      if (info && Object.keys(info).length) {
        Object.keys(info).forEach(key => {
          if (!info[key].logo) {
            delete info[key].logo;
          }
        });
      }
      result = {
        type,
        priority: parseInt(priority, 10),
        color,
        animationColor,
        info,
        assets,
        tickup
      };
    }
    return result;
  }

  private initForm(): FormGroup {
    return this.fb.group({
      type: [],
      assets: [],
      priority: ['1'],
      color: ['', colorHexValidator],
      animationColor: ['', colorHexValidator],
      info: [null, Validators.required],
      tickup: [null]
    });
  }
}
