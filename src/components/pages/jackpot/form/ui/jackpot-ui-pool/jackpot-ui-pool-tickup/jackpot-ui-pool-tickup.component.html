<form [formGroup]="form" class="jp-tickup">
  <div class="jp-tickup__item">
    <mat-form-field appearance="outline">
      <mat-label>{{'JACKPOT.FORM.UI.interval' | translate}}</mat-label>
      <input matInput type="number" min="0" [formControl]="intervalControl">
      <mat-error>
        <lib-swui-control-messages [control]="intervalControl" [messages]="errorMessages">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
    <div class="jp-tickup__text">{{'COMMON.seconds' | translate}}</div>
  </div>

  <div class="jp-tickup__item">
    <mat-form-field appearance="outline">
      <mat-label>{{'JACKPOT.FORM.UI.decrease' | translate}}</mat-label>
      <input matInput type="number" min="0" max="100" [formControl]="decreaseControl">
      <mat-error>
        <lib-swui-control-messages [control]="decreaseControl" [messages]="errorMessages">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
    <div class="jp-tickup__text">%</div>
  </div>
</form>
