import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { takeUntil } from 'rxjs/operators';

import { AbstractFormValueAccessor, formValueProviders } from '../../../../../../../common/lib/abstract-form-value-accessor';
import { JackpotUiPoolTickup } from '../../../../../interfaces/jackpot';


@Component({
  selector: 'sw-jackpot-ui-pool-tickup',
  templateUrl: './jackpot-ui-pool-tickup.component.html',
  styleUrls: ['./jackpot-ui-pool-tickup.component.scss'],
  providers: formValueProviders(JackpotUiPoolTickupComponent),
})
export class JackpotUiPoolTickupComponent extends AbstractFormValueAccessor<JackpotUiPoolTickup> implements OnInit {
  readonly form = new FormGroup({});
  errorMessages: { [key: string]: any } = {
    required: 'VALIDATION.fieldRequired'
  };

  constructor( private fb: FormBuilder ) {
    super();
    this.form = this.initForm();
  }

  ngOnInit(): void {
    this.form.valueChanges.pipe(
      takeUntil(this.destroyed)
    ).subscribe(value => {
      this.onChange(this.transformForm(value));
    });

    this.form.statusChanges.pipe(
      takeUntil(this.destroyed)
    ).subscribe(() => {
      this.onValidatorChange();
    });
  }

  get intervalControl(): FormControl {
    return this.form.get('interval') as FormControl;
  }

  get decreaseControl(): FormControl {
    return this.form.get('decrease') as FormControl;
  }

  private initForm(): FormGroup {
    return this.fb.group({
      interval: [null, Validators.compose([
        Validators.required,
        Validators.min(0)
      ])],
      decrease: [null, Validators.compose([
        Validators.required,
        Validators.min(0),
        Validators.max(100)
      ])]
    });
  }
}
