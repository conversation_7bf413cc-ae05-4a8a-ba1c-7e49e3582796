import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

@Injectable()
export class JackpotUiLanguageService {
  private _languages$ = new BehaviorSubject<string[]>([]);

  get languages$(): Observable<string[]> {
    return this._languages$.asObservable()
      .pipe(
        debounceTime(20),
      );
  }

  get languages(): string[] {
    return this._languages$.value;
  }

  addLanguages( langs: string[] ) {
    const uniqueLanguages = new Set<string>([...this._languages$.value, ...langs]);

    this._languages$.next(Array.from(uniqueLanguages));
  }

  setLanguages( langs: string[] ) {
    const uniqueLanguages = new Set<string>(langs);

    this._languages$.next(Array.from(uniqueLanguages));
  }

  removeLanguage( lang: string ) {
    const languages = this._languages$.value;
    const index = languages.indexOf(lang);

    languages.splice(index, 1);

    this._languages$.next([...languages]);
  }
}
