import { Component, Input } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';

import { AbstractFormValueAccessor, formValueProviders } from '../../../../../../common/lib/abstract-form-value-accessor';
import { GameInfo } from '../../../../../../common/models/game';
import { JackpotUiLobby } from '../../../../interfaces/jackpot';


@Component({
  selector: 'sw-jackpot-ui-lobby',
  templateUrl: './jackpot-ui-lobby.component.html',
  providers: formValueProviders(JackpotUiLobbyComponent),
})
export class JackpotUiLobbyComponent extends AbstractFormValueAccessor<JackpotUiLobby> {
  @Input() games: string[] = [];
  @Input() sourceGames: GameInfo[] = [];
  form = new FormGroup({});

  constructor( private fb: FormBuilder ) {
    super();
    this.form = this.initForm();
  }

  protected transformForm( value: JackpotUiLobby ): JackpotUiLobby | undefined {
    return {
      games: value && value.games ? value.games : [],
      css: value && value.css ? value.css : ''
    };
  }

  get gamesControl(): FormControl {
    return this.form.get('games') as FormControl;
  }

  get cssControl(): FormControl {
    return this.form.get('css') as FormControl;
  }

  private initForm(): FormGroup {
    return this.fb.group({
      games: [],
      css: []
    });
  }
}
