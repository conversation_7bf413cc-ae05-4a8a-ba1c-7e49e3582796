import { AfterViewInit, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup } from '@angular/forms';
import { DragulaOptions, DragulaService } from 'ng2-dragula';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { debounceTime, map, takeUntil } from 'rxjs/operators';

import { AbstractFormValueAccessor, formValueProviders } from '../../../../../../../common/lib/abstract-form-value-accessor';
import { GameInfo } from '../../../../../../../common/models/game';
import { JackpotUiLobbyGame } from '../../../../../interfaces/jackpot';


@Component({
  selector: 'sw-jackpot-ui-lobby-games',
  templateUrl: './jackpot-ui-lobby-games.component.html',
  styleUrls: ['./jackpot-ui-lobby-games.component.scss'],
  providers: [
    ...formValueProviders(JackpotUiLobbyGamesComponent),
    DragulaService,
  ],
})
export class JackpotUiLobbyGamesComponent extends AbstractFormValueAccessor<JackpotUiLobbyGame[]> implements OnInit, AfterViewInit {
  @Input() sourceGames: GameInfo[] = [];

  @Input()
  set selectedGames( val: string[] ) {
    if (val && Array.isArray(val)) {
      this._selectedGames$.next(val);
    }
  }

  get selectedGames(): string[] {
    return this._selectedGames$.value;
  }

  @ViewChild('container', { static: false, read: ElementRef }) container?: ElementRef<HTMLDivElement>;

  form = new FormGroup({});
  gamesGroup = 'gamesGroup';
  dragItems: JackpotUiLobbyGame[] = [];
  isDisabled = false;

  private _selectedGames$ = new BehaviorSubject<string[]>([]);
  private _games$ = new BehaviorSubject<JackpotUiLobbyGame[]>([]);
  private gamesOptions: DragulaOptions = {
    moves: ( _, __, handle: Element | undefined ) => {
      return !!handle && handle.classList.contains('handle-game');
    }
  };

  constructor( private fb: FormBuilder,
               private dragulaService: DragulaService
  ) {
    super();
    this.form = this.initForm();

    this.dragulaService.createGroup(this.gamesGroup, this.gamesOptions);
  }

  ngOnInit(): void {
    super.ngOnInit();

    this.dragulaService.dropModel(this.gamesGroup)
      .pipe(takeUntil(this.destroyed))
      .subscribe(( { targetModel } ) => {
        this._games$.next(targetModel);
      });

    this.gamesControl.valueChanges
      .pipe(
        debounceTime(1),
        takeUntil(this.destroyed)
      )
      .subscribe(data => {
        this.dragItems = data;
      });

    combineLatest([this._selectedGames$, this._games$])
      .pipe(
        map(( [selected, games] ) => {
          return this.processGames(selected, games);
        }),
        takeUntil(this.destroyed)
      )
      .subscribe(( games: JackpotUiLobbyGame[] ) => {
        this.gamesControl.clear();
        games.forEach(( game: JackpotUiLobbyGame ) => {
          const control = this.initGameGroup();
          control.patchValue(game);
          this.gamesControl.push(control);
        });
        this.dragItems = games;
      });
  }

  get gamesControl(): FormArray {
    return this.form.get('games') as FormArray;
  }

  ngAfterViewInit() {
    if (!this.container) {
      return;
    }

    Object.assign(this.gamesOptions, {
      mirrorContainer: this.container.nativeElement
    });
  }

  writeValue( value: JackpotUiLobbyGame[] | undefined ): void {
    if (!value || !Array.isArray(value)) {
      return;
    }
    this._games$.next(value);
  }

  setDisabledState( isDisabled: boolean ): void {
    isDisabled ? this.form.disable({ emitEvent: false }) : this.form.enable();
    this.isDisabled = !!isDisabled;
  }

  getGameName( code: string ): string {
    const game = this.sourceGames.find(( item: GameInfo ) => item.code === code);
    return game ? game.title : '';
  }

  protected transformForm( value: any ): JackpotUiLobbyGame[] | undefined {
    return value['games'];
  }

  private processGames( selected: string[], games: JackpotUiLobbyGame[] ): JackpotUiLobbyGame[] {
    let processed: JackpotUiLobbyGame[] = selected.map(( item: string ) => {
      return {
        gameCode: item,
        isVisible: true,
      };
    });

    const sortedGames = games.filter(( { gameCode } ) => {
      return selected.some(code => code === gameCode);
    });

    processed = processed.filter(( { gameCode } ) => {
      return !games.some(game => game.gameCode === gameCode);
    });

    return [...sortedGames, ...processed];
  }

  private initForm(): FormGroup {
    return this.fb.group({
      games: this.fb.array([]),
    });
  }

  private initGameGroup(): FormGroup {
    return this.fb.group({
      gameCode: [],
      isVisible: [],
    });
  }
}
