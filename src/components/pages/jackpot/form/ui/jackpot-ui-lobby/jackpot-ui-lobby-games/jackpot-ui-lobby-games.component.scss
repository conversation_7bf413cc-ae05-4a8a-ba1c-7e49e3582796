.lobby-games {
  height: 400px;
  overflow: auto;
}

table {
  width: 100%;
  border-spacing: 0;
  .mat-header-row {
    height: 56px;
  }
  .mat-row {
    height: 48px;
  }
  .mat-header-cell {
    top: 0;
    position: sticky;
    z-index: 100;
    background: #fff;
    font-weight: 500;
  }
  .mat-header-cell,
  .mat-cell {
    text-align: left;
    border-bottom: 1px solid rgba(0, 0, 0, .12);
    &:first-child {
      padding-left: 24px;
    }
    &:last-child {
      padding-right: 24px;
    }
  }

  .action {
    visibility: hidden;
    opacity: 0;
    mat-icon {
      color: #9DA6BA;
    }
  }

  tr {
    &:hover {
      .action {
        opacity: 1;
        visibility: visible;
        transition: opacity 0.15s ease-in-out;
      }
    }
  }
}

.game-name {
  display: flex;
  align-items: center;
}

.handle-game {
  flex-shrink: 0;
  display: block;
  padding: 0;
  width: 20px;
  height: 50px;
  background: #F8F8F8 url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAANCAYAAACKCx+LAAABS2lUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4KPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS42LWMxNDIgNzkuMTYwOTI0LCAyMDE3LzA3LzEzLTAxOjA2OjM5ICAgICAgICAiPgogPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4KICA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIi8+CiA8L3JkZjpSREY+CjwveDp4bXBtZXRhPgo8P3hwYWNrZXQgZW5kPSJyIj8+nhxg7wAAACZJREFUGJVj/P//vwkDNlBiXPJ/4/+NJqj0fxNGOumIHZSuwqoDAMKBbKoe7/dzAAAAAElFTkSuQmCC') no-repeat center;
  cursor: grab;
  margin-right: 10px;
}
