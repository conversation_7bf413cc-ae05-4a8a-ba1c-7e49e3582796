<form [formGroup]="form" class="lobby-games">
  <table formArrayName="games" class="mat-table">
    <thead>
    <tr class="mat-header-row">
      <th class="mat-header-cell">Game</th>
      <th class="mat-header-cell" style="text-align: center">Show thumbnail</th>
      <th class="mat-header-cell"></th>
    </tr>
    </thead>
    <tbody #container
           [dragula]="gamesGroup"
           [(dragulaModel)]="dragItems">
    <tr *ngFor="let group of gamesControl?.controls" class="mat-row" [formGroup]="group">
      <td class="mat-cell">
        <div class="game-name">
          <span class="handle-game" *ngIf="!isDisabled"></span>
          {{getGameName(group?.get('gameCode')?.value)}}
        </div>
      </td>
      <td class="mat-cell" style="text-align: center">
        <mat-checkbox formControlName="isVisible"></mat-checkbox>
      </td>
    </tr>
    </tbody>
  </table>
</form>
