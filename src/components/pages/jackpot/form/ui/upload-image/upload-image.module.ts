import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule } from '@skywind-group/lib-swui';
import { ImageUrlModule } from '../image-url/image-url.module';
import { UploadImageComponent } from './upload-image.component';


@NgModule({
  declarations: [
    UploadImageComponent
  ],
  exports: [
    UploadImageComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatRadioModule,
    MatFormFieldModule,
    MatInputModule,
    TranslateModule,
    MatIconModule,
    FlexModule,
    MatButtonModule,
    MatCardModule,
    SwuiControlMessagesModule,
    FormsModule,
    ImageUrlModule
  ],
})
export class UploadImageModule { }
