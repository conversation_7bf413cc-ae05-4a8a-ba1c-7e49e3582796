import { Component, Input } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';

@Component({
  selector: 'sw-upload-image',
  templateUrl: './upload-image.component.html'
})
export class UploadImageComponent {
  @Input() defaultLogoImage: string | undefined;
  @Input() defaultImages: { [key: string]: string } = {};

  @Input() set isDisabled( isDisabled: boolean ) {
    this._isDisabled = isDisabled;
    isDisabled ? this.form.disable({ emitEvent: false }) : this.form.enable();
  }

  get isDisabled(): boolean {
    return this._isDisabled;
  }

  readonly form: FormGroup;

  private _isDisabled = false;

  constructor( private fb: FormBuilder ) {
    this.form = this.initForm();
  }

  get logoImagePathControl(): FormControl {
    return this.form.get('url') as FormControl;
  }

  initForm( val?: any ): FormGroup {
    const group = this.fb.group({
      id: [''],
      url: ['']
    });
    if (val) {
      group.patchValue(val);
    }
    return group;
  }

  setValue( val: any ) {
    if (val) {
      this.form.patchValue(val);
    }
  }
}
