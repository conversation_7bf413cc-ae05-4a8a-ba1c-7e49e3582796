<form [formGroup]="form">
  <div class="mat-card mat-elevation-z0 margin-bottom20">
    <h3 class="no-margin-top mat-title margin-bottom24">UI Template</h3>

    <div fxLayout.lt-sm="column" fxLayout="row">
      <div fxFlex.lt-sm="100" fxFlex="145px" fxFlexAlign="center">Select Template</div>
      <mat-form-field fxFlex.lt-sm="100" fxFlex="200px" appearance="outline" class="no-field-padding">
        <lib-swui-select
          [data]="availableTemplates"
          [disableEmptyOption]="true"
          [formControl]="templateTypeControl">
        </lib-swui-select>
      </mat-form-field>
    </div>

    <div fxLayout.lt-sm="column" fxLayout="row">
      <div fxFlex.lt-sm="100" fxFlex="145px" fxFlexAlign="center">Default language</div>
      <mat-form-field fxFlex.lt-sm="100" fxFlex="200px" appearance="outline" class="no-field-padding">
        <mat-select [formControl]="defaultLanguage">
          <mat-option *ngFor="let lang of languages$ | async" [value]="lang">
            {{ lang | uppercase }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>

  <mat-tab-group
    #tabs
    [formGroup]="featuresControl"
    class="nested"
    animationDuration="0ms"
    [selectedIndex]="selectedIndex">

    <ng-container formArrayName="pools">
      <mat-tab *ngFor="let control of poolsControl.controls; let index = index;">
        <ng-template matTabLabel>
          {{getJackpotTitle(control.value)}}
        </ng-template>
        <div class="mat-card mat-elevation-z0">
          <sw-jackpot-ui-pool
            [formControl]="control"
            [priorities]="priorities"
            [templateType]="templateTypeControl?.value"
            [isDisabled]="isDisabled"
            [index]="index">
          </sw-jackpot-ui-pool>
        </div>
      </mat-tab>
    </ng-container>

    <mat-tab>
      <ng-template matTabLabel>
        Engagement Bar
      </ng-template>
      <sw-engagement-bar [formControl]="engagementControl">
      </sw-engagement-bar>
    </mat-tab>

    <mat-tab *ngIf="infoPopupReadonly === false">
      <ng-template matTabLabel>
        Info Popup
      </ng-template>
      <div class="mat-card mat-elevation-z0">
        <h3 class="no-margin-top mat-title margin-bottom24">Info popup</h3>
        <mat-radio-group [formControl]="infoPopupControl" class="info-popup-radio">
          <ng-container [ngSwitch]="infoPopupControl.value">
            <div class="info-popup-radio__group" [ngClass]="{'hidden': templateTypeControl.value === 'asia'}">
              <mat-radio-button value="url">Info popup url</mat-radio-button>
              <ng-template ngSwitchCase="url">
                <mat-form-field appearance="outline">
                  <mat-label>Url</mat-label>
                  <input type="text" matInput [formControl]="infoUrlControl">
                  <mat-error>
                    <lib-swui-control-messages [control]="infoUrlControl"></lib-swui-control-messages>
                  </mat-error>
                </mat-form-field>
              </ng-template>
            </div>
            <div class="info-popup-radio__group">
              <mat-radio-button value="editor">Info popup rich text editor</mat-radio-button>
              <ng-template ngSwitchCase="editor">
                <sw-translations-manager
                  [formControl]="infoControl"
                  [component]="childFormComponent"
                  [isDisabled]="isDisabled"
                  [withoutDefault]="true"
                  [templateType]="templateTypeControl?.value"
                  (addLang)="onAddLang($event)">
                  <sw-jackpot-ui-info
                    #childFormComponent
                    [templateType]="templateTypeControl?.value"
                    [isDisabled]="isDisabled">
                  </sw-jackpot-ui-info>
                </sw-translations-manager>
              </ng-template>
            </div>
          </ng-container>
        </mat-radio-group>
      </div>
    </mat-tab>

    <mat-tab *ngIf="infoPopupReadonly">
      <ng-template matTabLabel>Info Popup</ng-template>
      <ng-template matTabContent>
        <div class="mat-card mat-elevation-z0">
          <h3 class="no-margin-top mat-title margin-bottom24">Info popup</h3>
          <sw-jackpot-ui-info-preview [uiConfig]="form.value"></sw-jackpot-ui-info-preview>
        </div>
      </ng-template>
    </mat-tab>

    <mat-tab>
      <ng-template matTabLabel>Lobby widget</ng-template>
      <div class="mat-card mat-elevation-z0">
        <h3 class="no-margin-top mat-title margin-bottom24">Lobby widget CSS</h3>
        <sw-jackpot-ui-lobby [formControl]="lobbyControl" [games]="games" [sourceGames]="sourceGames">
        </sw-jackpot-ui-lobby>
      </div>
    </mat-tab>

    <mat-tab>
      <ng-template matTabLabel>Advanced</ng-template>
      <ng-template matTabContent>
        <div class="mat-card mat-elevation-z0">
          <h3 class="no-margin-top mat-title margin-bottom24">JSON settings</h3>
          <sw-jackpot-ui-advanced [uiConfig]="form.value"
                                  [isDisabled]="isDisabled"
                                  (configChanged)="onConfigChange($event)">
          </sw-jackpot-ui-advanced>
        </div>
      </ng-template>
    </mat-tab>

  </mat-tab-group>
</form>
