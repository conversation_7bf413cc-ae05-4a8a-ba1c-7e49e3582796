import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { DomSanitizer } from '@angular/platform-browser';
import { filter, takeUntil } from 'rxjs/operators';

import { BaseComponent } from '../../../../../../common/components/base.component';
import { JackpotUiEditDialogComponent } from './jackpot-ui-edit-dialog/jackpot-ui-edit-dialog.component';
import { createNewWindow, WindowOptions } from '../../../../../../common/lib/window';

const WINDOW_OPTIONS: WindowOptions = { width: 500, height: 500, left: 0, top: 0, location: 0, toolbar: 0 };

@Component({
  selector: 'sw-jackpot-ui-info',
  templateUrl: './jackpot-ui-info.component.html',
  styleUrls: ['./jackpot-ui-info.component.scss'],
})
export class JackpotUiInfoComponent extends BaseComponent implements OnInit {
  @Input() set templateType( val: string | undefined ) {
    if (!val) {
      return;
    }
    this._templateType = val;
  }

  get templateType(): string | undefined {
    return this._templateType;
  }

  @Input() isDisabled?: boolean;
  @ViewChild('headerIframe') headerIframeRef?: ElementRef;
  @ViewChild('footerIframe') footerIframeRef?: ElementRef;
  readonly form: FormGroup;

  private _templateType?: string;
  private _windowHeaderHandle?: Window;
  private _windowFooterHandle?: Window;

  constructor( private fb: FormBuilder,
               private sanitiser: DomSanitizer,
               private readonly dialog: MatDialog
  ) {
    super();
    this.form = this.initForm();
  }

  ngOnInit() {
    if (this.isDisabled) {
      this.form.disable({ emitEvent: false });
    }

    this.headerControl.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: string ) => {
        if (this._windowHeaderHandle) {
          this._windowHeaderHandle.document.body.innerHTML = val;
        }
      });

    this.footerControl.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: string ) => {
        if (this._windowFooterHandle) {
          this._windowFooterHandle.document.body.innerHTML = val;
        }
      });
  }

  get idControl(): FormControl {
    return this.form.get('id') as FormControl;
  }

  get headerControl(): FormControl {
    return this.form.get('header') as FormControl;
  }

  get footerControl(): FormControl {
    return this.form.get('footer') as FormControl;
  }

  setValue( val: any ) {
    setTimeout(() => {
      if (val) {
        const { header, footer, id } = val;
        this.idControl.patchValue(id, { emitEvent: false });
        this.setSafeHTML(this.headerControl, this.headerIframeRef, header, false);
        this.setSafeHTML(this.footerControl, this.footerIframeRef, footer || '', false);
      }
    });
  }

  onPreview( event: Event, value: string, name: string ) {
    event.preventDefault();
    if (name === 'header') {
      this._windowHeaderHandle = createNewWindow('', name, WINDOW_OPTIONS);
      this.setupWindowHandle(value, this._windowHeaderHandle);
    } else {
      this._windowFooterHandle = createNewWindow('', name, WINDOW_OPTIONS);
      this.setupWindowHandle(value, this._windowFooterHandle);
    }
  }

  onEdit( event: Event, html: any, type: string ) {
    event.preventDefault();

    const dialogRef = this.dialog.open(JackpotUiEditDialogComponent, {
      height: '100vh',
      width: '100vw',
      maxWidth: '100vw',
      maxHeight: '100vh',
      panelClass: 'sw-jackpot-info-editor',
      data: { html }
    });

    dialogRef.afterClosed()
      .pipe(
        filter(data => !!data),
        takeUntil(this.destroyed)
      )
      .subscribe(( val: string ) => {
        if (type === 'header') {
          this.setSafeHTML(this.headerControl, this.headerIframeRef, val, true);
        } else {
          this.setSafeHTML(this.footerControl, this.footerIframeRef, val, true);
        }
      });
  }

  private initForm( val?: any ): FormGroup {
    const group = this.fb.group({
      id: [''],
      header: [''],
      footer: ['']
    });

    if (val) {
      group.patchValue(val);
    }
    return group;
  }

  private setupWindowHandle( value: string, window: Window ) {
    if (window) {
      const document = window.document;
      const body = document.body;
      document.title = 'Preview';

      body.innerHTML = this.sanitiser.sanitize(0, value) as string;
    }
  }

  private setSafeHTML( control: FormControl, ref: ElementRef | undefined, val: string, emitEvent: boolean ) {
    const safeVal = this.sanitiser.sanitize(0, val) as string;
    control.patchValue(safeVal, { emitEvent: emitEvent });
    if (ref) {
      const iframe = ref.nativeElement as HTMLIFrameElement;
      iframe.srcdoc = safeVal;
    }
  }
}
