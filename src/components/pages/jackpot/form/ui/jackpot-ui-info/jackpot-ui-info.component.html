<div fxLayout="column" [formGroup]="form" class="ui-info">
  <div class="ui-info__item">
    <div class="ui-info__label">
      {{templateType === 'asia' ? 'Header text contents of the info popup' : 'Rich text contents of the info popup'}}
    </div>
    <div class="ui-info__body">
      <div class="ui-info__actions">
        <button mat-icon-button [disabled]="isDisabled" (click)="onEdit($event, headerControl.value, 'header')">
          <mat-icon matTooltip="Edit" matTooltipPosition="above">tune</mat-icon>
        </button>
        <button mat-icon-button (click)="onPreview($event, headerControl.value, 'header')">
          <mat-icon matTooltip="Preview" matTooltipPosition="above">filter</mat-icon>
        </button>
      </div>
      <iframe #headerIframe class="ui-info__iframe"></iframe>
    </div>
  </div>

  <div class="ui-info__item" *ngIf="templateType === 'asia'">
    <div class="ui-info__label">Footer text contents of the info popup</div>
    <div class="ui-info__body">
      <div class="ui-info__actions">
        <button mat-icon-button [disabled]="isDisabled" (click)="onEdit($event, footerControl.value, 'footer')">
          <mat-icon matTooltip="Edit" matTooltipPosition="above">tune</mat-icon>
        </button>
        <button mat-icon-button (click)="onPreview($event, footerControl.value, 'footer')">
          <mat-icon matTooltip="Preview" matTooltipPosition="above">filter</mat-icon>
        </button>
      </div>
      <iframe #footerIframe class="ui-info__iframe"></iframe>
    </div>
  </div>
</div>
