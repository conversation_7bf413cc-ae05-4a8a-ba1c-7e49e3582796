.editor-modal {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  &__editor {
    flex: 1;
    display: block;
    height: calc(100% - 78px);
    width: 100%;
  }
  &__actions {
    display: flex;
    justify-content: flex-end;
    flex: 0;
    padding: 16px;
    height: 78px;
    button {
      margin-left: 16px;
    }
  }
  &__loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 11;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.7);
  }
}
