import { Component, Inject, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AngularEditorComponent, AngularEditorConfig } from '@kolkov/angular-editor';


@Component({
  selector: 'sw-jackpot-ui-edit-dialog',
  templateUrl: './jackpot-ui-edit-dialog.component.html',
  styleUrls: ['./jackpot-ui-edit-dialog.component.scss'],
})
export class JackpotUiEditDialogComponent {

  editorConfig: AngularEditorConfig = {
    editable: true,
    spellcheck: true,
    height: '100%',
    minHeight: '0',
    maxHeight: 'auto',
    width: '100%',
    minWidth: '0',
    translate: 'yes',
    enableToolbar: true,
    showToolbar: true,
    placeholder: '',
    defaultParagraphSeparator: '',
    defaultFontName: '',
    defaultFontSize: '',
    sanitize: false,
    toolbarPosition: 'top'
  };

  editorControl = new FormControl('');
  isViewMode = true;
  loading = false;

  @ViewChild('editor') editorRef?: AngularEditorComponent;

  constructor( public dialogRef: MatDialogRef<JackpotUiEditDialogComponent>,
               @Inject(MAT_DIALOG_DATA) public data: { html: string } ) {
    const { html } = data;
    this.editorControl.patchValue(html, { emitEvent: false });
  }


  save(event: Event) {
    event.preventDefault();
    this.loading = true;
    if (this.editorRef && !this.isViewMode) {
      this.editorRef.toggleEditorMode(false);
    }
    setTimeout( () => {
      this.loading = false;
      this.dialogRef.close(this.editorControl.value) ;
    }, 500);
  }

  close(event: Event) {
    event.preventDefault();
    this.dialogRef.close();
  }

  onViewModeChange(isViewMode: boolean) {
    this.isViewMode = isViewMode;
  }
}
