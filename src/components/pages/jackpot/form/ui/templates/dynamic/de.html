<style>
  [data-color=win] {
    color: #fdca13
  }

  [data-color=shared] {
    color: #ff4eb2
  }

  [data-color=amount] {
    color: #34c2ff
  }

  [data-color=time] {
    color: #dcf34c
  }

  body {
    margin: 0;
    padding: 0;
  }

  .ubo-popup {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #000;
    background-color: rgba(0, 0, 0, .4);
    font-family: Open Sans, sans-serif;
    font-size: 16px
  }

  @media (min-width: 667px) {
    .ubo-popup {
      font-size: calc(10px + 1vw)
    }
  }

  @media (min-width: 1001px) {
    .ubo-popup {
      font-size: 22px
    }
  }

  .ubo-popup-close {
    z-index: 1;
    position: absolute;
    right: 0;
    top: 0;
    background-color: #300131;
    border: 3px solid #ffef60;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 40px;
    height: 40px;
    border-radius: 12px;
    cursor: pointer;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
  }

  @media (min-width: 667px) {
    .ubo-popup-close {
      right: 25px;
      top: 15px
    }
  }

  @media (min-width: 1001px) {
    .ubo-popup-close {
      right: 0;
      width: 58px;
      height: 58px
    }
  }

  .ubo-popup-close .close {
    background-image: url(https://origin8.m27613.com/sweh-assets/jackpot-info-popup/x-icon.png);
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: contain;
    width: 13px;
    height: 13px
  }

  @media (min-width: 1001px) {
    .ubo-popup-close .close {
      width: 20px;
      height: 20px
    }
  }

  .ubo-popup-inner {
    position: relative;
    width: inherit;
    height: inherit;
    max-width: 998px;
    max-height: 580px;
    padding: 15px 74px;
    margin: auto;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
  }

  @media (max-width: 666px) {
    .ubo-popup-inner {
      height: 100%;
      max-width: calc(100% - 50px);
      max-height: calc(100% - 30px);
      padding: 51px 0 0
    }
  }

  .ubo-popup-content {
    margin: auto;
    overflow: hidden;
    overflow-y: auto;
    position: relative;
    max-width: 850px;
    width: 100%;
    height: 100%;
    color: #fff;
    padding: 9px 0 48px 18px;
    background: -webkit-gradient(linear, left bottom, left top, from(#090022), to(#2b062b));
    background: linear-gradient(0deg, #090022 0, #2b062b);
    border: 3px solid #ffef60;
    border-radius: 12px;
    -webkit-box-shadow: 0 -10px 50px #f6b31a;
    box-shadow: 0 -10px 50px #f6b31a;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }

  .ubo-popup-scroll {
    width: 100%;
    height: 100%;
    padding-right: 18px;
    overflow: auto;
    scrollbar-color: rgba(255, 255, 255, .1) transparent;
    scrollbar-width: thin;
    box-sizing: border-box;
  }

  .ubo-popup-scroll::-webkit-scrollbar {
    width: 21px;
    background-color: transparent
  }

  .ubo-popup-scroll::-webkit-scrollbar-button:end, .ubo-popup-content::-webkit-scrollbar-button:start {
    display: block
  }

  .ubo-popup-scroll::-webkit-scrollbar-thumb {
    background-clip: padding-box;
    background-color: rgba(255, 255, 255, .1);
    border: 7px solid transparent;
    border-radius: 10px
  }

  .ubo-popup-scroll::-webkit-scrollbar-track {
    border: 6px solid transparent
  }

  @media (min-width: 667px) {
    .ubo-popup-scroll {
      padding-right: 24px;
    }
    .ubo-popup-content {
      padding: 12px 0 24px 24px;
    }
  }

  @media (min-width: 1001px) {
    .ubo-popup-scroll {
      padding-right: 50px;
    }
    .ubo-popup-content {
      padding: 25px 0 50px 50px;
    }
  }

  img {
    display: block;
    max-width: 100%;
    height: auto;
    pointer-events: none
  }

  .ubo-popup-mainlogo {
    max-width: 500px;
    margin: auto auto 17px
  }

  @media (max-width: 666px),(min-width: 667px) and (max-width: 900px) and (orientation: landscape) {
    .ubo-popup-mainlogo {
      margin-bottom: 12px;
      max-width: 350px
    }
  }

  .ubo-popup-textlogo {
    text-transform: uppercase;
    line-height: 1.36;
    font-size: 1.63em;
    font-weight: 700;
    text-align: center;
    letter-spacing: 1px;
    margin: 40px auto 12px
  }

  @media (max-width: 666px),(min-width: 667px) and (max-width: 900px) and (orientation: landscape) {
    .ubo-popup-textlogo {
      font-size: 1.5em;
      margin: 32px auto 8px
    }
  }

  @media (max-width: 420px) {
    .ubo-popup-textlogo {
      font-size: 5vw
    }
  }

  .ubo-popup-textlogo:nth-child(2) {
    margin-top: 12px
  }

  .ubo-popup-text {
    font-size: .82em;
    line-height: 1.38
  }

  @media (min-width: 1001px) {
    .ubo-popup-text {
      line-height: 1.33
    }
  }

  .ubo-popup-text .progress-bar {
    display: inline-block;
    max-width: 115px;
    margin: 0 7px
  }

  @media (max-width: 666px),(min-width: 667px) and (max-width: 900px) and (orientation: landscape) {
    .ubo-popup-text .progress-bar {
      max-width: 80px
    }
  }

  .ubo-popup-patterns {
    background-image: url(https://origin8.m27613.com/sweh-assets/jackpot-info-popup/stars.png);
    width: 150px;
    height: 150px;
    position: absolute;
    bottom: 0;
    background-position: 50%;
    background-size: contain;
    background-repeat: no-repeat;
    -webkit-transform: translateY(-15px);
    transform: translateY(-15px)
  }

  @media (max-width: 666px) {
    .ubo-popup-patterns {
      -webkit-transform: translateY(0);
      transform: translateY(0);
      width: 100px;
      height: 100px
    }
  }

  .ubo-popup-patterns.right {
    right: 74px;
    -webkit-transform: translateY(-15px) scaleX(-1);
    transform: translateY(-15px) scaleX(-1)
  }

  @media (max-width: 666px) {
    .ubo-popup-patterns.right {
      right: 0;
      -webkit-transform: translateY(0) scaleX(-1);
      transform: translateY(0) scaleX(-1)
    }
  }

  @media (-ms-high-contrast: none), screen and (-ms-high-contrast: active) {
    .ubo-popup-scroll {
      margin-right: -5px;
      scrollbar-base-color: #1d1d1d;
      scrollbar-face-color: rgba(255, 255, 255, .1);
      scrollbar-3dlight-color: rgba(255, 255, 255, .1);
      scrollbar-highlight-color: rgba(255, 255, 255, .1);
      scrollbar-track-color: #1d1d1d;
      scrollbar-arrow-color: #1d1d1d;
      scrollbar-shadow-color: #1d1d1d
    }

    .ubo-popup-scroll:after {
      content: "";
      height: 32px;
      display: block
    }
  }</style>
<div class="ubo-popup">
    <div class="ubo-popup-inner">
        <div class="ubo-popup-close" data-ok="1"><i class="close" data-ok="1"></i></div>
        <div class="ubo-popup-content">
          <div class="ubo-popup-scroll allow-swipe">
            <div class="ubo-popup-mainlogo"><img
                    src="https://origin8.m27613.com/sweh-assets/jackpot-info-popup/logo.png" alt=""></div>
            <div class="ubo-popup-textlogo" data-color="win">Must-Win Jackpot</div>
            <div class="ubo-popup-text">Jackpots mit garantiertem Gewinn sind progressive Jackpots, die von jedem teilnehmendem Spieler gewonnen werden können. Die Jackpots werden zufällig durch jeden Betrag bei jedem der teilnehmenden Spiele ausgelöst. Je höher Sie setzen, desto besser sind die Gewinnchancen auf den Jackpot. Allerdings wird jeder Einsatz über [maxBet] Ihre Gewinnchancen nicht weiter verbessern.
              <br><br>Die Jackpots können nicht während der Freispiele, Bonusrunden oder Einsätzen mit Bonusmünzen gewonnen werden.
              <br><br>Der Spieler, der gewinnt, erhält eine Mitteilung und sein Konto erhält den entsprechenden Betrag gutgeschrieben.
            </div>
            <div class="ubo-popup-textlogo" data-color="shared">Shared Prize Jackpot</div>
            <div class="ubo-popup-text">Wenn dieser Jackpot geknackt wird, erhält der Gewinner [mainPecent] des Pots, während die verbleibenden [sharedPercent] unter allen qualifizierten Spielern wie folgt aufgeteilt wird: [sharedEven] zu gleichen Teilen und [sharedRelative] proportional zum Einsatz jedes Spielers während der letzten [eligibilityPeriod] Stunden.
              <br><br>Die Fortschrittsleiste <span class="progress-bar"><img src="https://origin8.m27613.com/sweh-assets/jackpot-info-popup/progress.png" alt=""> </span>unter dem Jackpot-Ticker zeigt Ihren Qualifikationsstatus entsprechend Ihrer Einsätze der letzten [eligibilityPeriod] Stunden an. Sie haben sich qualifiziert, wenn die Anzeige voll ist.
              <br><br>Die Leiste wird gefüllt, wenn Ihre Gesamteinsätze in den letzten [eligibilityPeriod] Stunden bei allen teilnehmenden Spielen [eligibilityThreshold] oder mehr sind.
              <br><br>Sie gewinnen einen Anteil vom Jackpot, selbst wenn Sie offline sind, vorausgesetzt dass Sie sich bei Gewinn des Jackpots dafür qualifiziert haben.
              <br><br>Dieser Jackpot kann jederzeit gewonnen werden. Sobald dieser Jackpot gewonnen wurde, steht ein neuer progressiver Jackpot innerhalb von 10 Sekunden zur Verfügung.
              <br><br>Der Jackpot-Startbetrag und garantierte Mindestpreis ist [seed]
            </div>
            <div class="ubo-popup-textlogo" data-color="amount">Amount Jackpot</div>
            <div class="ubo-popup-text">Dieser Jackpot wird garantiert vor folgendem Betrag ausgespielt: [amountLimit]. Die Gewinnchancen erhöhen sich mit dem höheren Betrag im Pot, bis zu 100 %, sobald ein bestimmter Betrag erreicht wurde.
              <br><br>Dieser Jackpot kann jederzeit und nur durch einen einzelnen Spieler gewonnen werden. Sobald dieser Jackpot gewonnen wurde, steht ein Jackpot mit neuem Betrag zur Verfügung.
              <br><br>Startbetrag und garantierter Mindestpreis des Jackpots: [seed]
            </div>
            <div class="ubo-popup-textlogo" data-color="time">Time Jackpot</div>
            <div class="ubo-popup-text">Der Jackpot wird garantiert vor einem bestimmten Datum und einer bestimmten Zeit ausgespielt. Die Gewinnchancen erhöhen sich im Laufe der Zeit, bis zu 100 % nach einer bestimmten Zeit.
              <br><br>Dieser Jackpot kann jederzeit und nur durch einen einzelnen Spieler gewonnen werden. Sobald dieser Jackpot gewonnen wurde, steht ein neuer Jackpot zur Verfügung.
              <br><br>Startbetrag und garantierter Mindestpreis des Jackpots: [seed]
            </div>
          </div>
        </div>
        <div class="ubo-popup-patterns"></div>
        <div class="ubo-popup-patterns right"></div>
    </div>
</div>
