<style>
  [data-color=win] {
    color: #fdca13
  }

  [data-color=shared] {
    color: #ff4eb2
  }

  [data-color=amount] {
    color: #34c2ff
  }

  [data-color=time] {
    color: #dcf34c
  }

  body {
    margin: 0;
    padding: 0;
  }

  .ubo-popup {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #000;
    background-color: rgba(0, 0, 0, .4);
    font-family: Open Sans, sans-serif;
    font-size: 16px
  }

  @media (min-width: 667px) {
    .ubo-popup {
      font-size: calc(10px + 1vw)
    }
  }

  @media (min-width: 1001px) {
    .ubo-popup {
      font-size: 22px
    }
  }

  .ubo-popup-close {
    z-index: 1;
    position: absolute;
    right: 0;
    top: 0;
    background-color: #300131;
    border: 3px solid #ffef60;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 40px;
    height: 40px;
    border-radius: 12px;
    cursor: pointer;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
  }

  @media (min-width: 667px) {
    .ubo-popup-close {
      right: 25px;
      top: 15px
    }
  }

  @media (min-width: 1001px) {
    .ubo-popup-close {
      right: 0;
      width: 58px;
      height: 58px
    }
  }

  .ubo-popup-close .close {
    background-image: url(https://origin8.m27613.com/sweh-assets/jackpot-info-popup/x-icon.png);
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: contain;
    width: 13px;
    height: 13px
  }

  @media (min-width: 1001px) {
    .ubo-popup-close .close {
      width: 20px;
      height: 20px
    }
  }

  .ubo-popup-inner {
    position: relative;
    width: inherit;
    height: inherit;
    max-width: 998px;
    max-height: 580px;
    padding: 15px 74px;
    margin: auto;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
  }

  @media (max-width: 666px) {
    .ubo-popup-inner {
      height: 100%;
      max-width: calc(100% - 50px);
      max-height: calc(100% - 30px);
      padding: 51px 0 0
    }
  }

  .ubo-popup-content {
    margin: auto;
    overflow: hidden;
    overflow-y: auto;
    position: relative;
    max-width: 850px;
    width: 100%;
    height: 100%;
    color: #fff;
    padding: 9px 0 48px 18px;
    background: -webkit-gradient(linear, left bottom, left top, from(#090022), to(#2b062b));
    background: linear-gradient(0deg, #090022 0, #2b062b);
    border: 3px solid #ffef60;
    border-radius: 12px;
    -webkit-box-shadow: 0 -10px 50px #f6b31a;
    box-shadow: 0 -10px 50px #f6b31a;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }

  .ubo-popup-scroll {
    width: 100%;
    height: 100%;
    padding-right: 18px;
    overflow: auto;
    scrollbar-color: rgba(255, 255, 255, .1) transparent;
    scrollbar-width: thin;
    box-sizing: border-box;
  }

  .ubo-popup-scroll::-webkit-scrollbar {
    width: 21px;
    background-color: transparent
  }

  .ubo-popup-scroll::-webkit-scrollbar-button:end, .ubo-popup-content::-webkit-scrollbar-button:start {
    display: block
  }

  .ubo-popup-scroll::-webkit-scrollbar-thumb {
    background-clip: padding-box;
    background-color: rgba(255, 255, 255, .1);
    border: 7px solid transparent;
    border-radius: 10px
  }

  .ubo-popup-scroll::-webkit-scrollbar-track {
    border: 6px solid transparent
  }

  @media (min-width: 667px) {
    .ubo-popup-scroll {
      padding-right: 24px;
    }
    .ubo-popup-content {
      padding: 12px 0 24px 24px;
    }
  }

  @media (min-width: 1001px) {
    .ubo-popup-scroll {
      padding-right: 50px;
    }
    .ubo-popup-content {
      padding: 25px 0 50px 50px;
    }
  }

  img {
    display: block;
    max-width: 100%;
    height: auto;
    pointer-events: none
  }

  .ubo-popup-mainlogo {
    max-width: 500px;
    margin: auto auto 17px
  }

  @media (max-width: 666px),(min-width: 667px) and (max-width: 900px) and (orientation: landscape) {
    .ubo-popup-mainlogo {
      margin-bottom: 12px;
      max-width: 350px
    }
  }

  .ubo-popup-textlogo {
    text-transform: uppercase;
    line-height: 1.36;
    font-size: 1.63em;
    font-weight: 700;
    text-align: center;
    letter-spacing: 1px;
    margin: 40px auto 12px
  }

  @media (max-width: 666px),(min-width: 667px) and (max-width: 900px) and (orientation: landscape) {
    .ubo-popup-textlogo {
      font-size: 1.5em;
      margin: 32px auto 8px
    }
  }

  @media (max-width: 420px) {
    .ubo-popup-textlogo {
      font-size: 5vw
    }
  }

  .ubo-popup-textlogo:nth-child(2) {
    margin-top: 12px
  }

  .ubo-popup-text {
    font-size: .82em;
    line-height: 1.38
  }

  @media (min-width: 1001px) {
    .ubo-popup-text {
      line-height: 1.33
    }
  }

  .ubo-popup-text .progress-bar {
    display: inline-block;
    max-width: 115px;
    margin: 0 7px
  }

  @media (max-width: 666px),(min-width: 667px) and (max-width: 900px) and (orientation: landscape) {
    .ubo-popup-text .progress-bar {
      max-width: 80px
    }
  }

  .ubo-popup-patterns {
    background-image: url(https://origin8.m27613.com/sweh-assets/jackpot-info-popup/stars.png);
    width: 150px;
    height: 150px;
    position: absolute;
    bottom: 0;
    background-position: 50%;
    background-size: contain;
    background-repeat: no-repeat;
    -webkit-transform: translateY(-15px);
    transform: translateY(-15px)
  }

  @media (max-width: 666px) {
    .ubo-popup-patterns {
      -webkit-transform: translateY(0);
      transform: translateY(0);
      width: 100px;
      height: 100px
    }
  }

  .ubo-popup-patterns.right {
    right: 74px;
    -webkit-transform: translateY(-15px) scaleX(-1);
    transform: translateY(-15px) scaleX(-1)
  }

  @media (max-width: 666px) {
    .ubo-popup-patterns.right {
      right: 0;
      -webkit-transform: translateY(0) scaleX(-1);
      transform: translateY(0) scaleX(-1)
    }
  }

  @media (-ms-high-contrast: none), screen and (-ms-high-contrast: active) {
    .ubo-popup-scroll {
      margin-right: -5px;
      scrollbar-base-color: #1d1d1d;
      scrollbar-face-color: rgba(255, 255, 255, .1);
      scrollbar-3dlight-color: rgba(255, 255, 255, .1);
      scrollbar-highlight-color: rgba(255, 255, 255, .1);
      scrollbar-track-color: #1d1d1d;
      scrollbar-arrow-color: #1d1d1d;
      scrollbar-shadow-color: #1d1d1d
    }

    .ubo-popup-scroll:after {
      content: "";
      height: 32px;
      display: block
    }
  }</style>
<div class="ubo-popup">
  <div class="ubo-popup-inner">
    <div class="ubo-popup-close" data-ok="1"><i class="close" data-ok="1"></i></div>
    <div class="ubo-popup-content">
      <div class="ubo-popup-scroll allow-swipe">
        <div class="ubo-popup-mainlogo"><img
          src="https://origin8.m27613.com/sweh-assets/jackpot-info-popup/logo.png" alt=""></div>
        <div class="ubo-popup-textlogo" data-color="win">Must-Win Jackpot</div>
        <div class="ubo-popup-text">
          必贏累積獎金是漸進式累積獎金，可能被任何參與活動的玩家贏走。任何參與活動的遊戲中的任何投注都可能觸發累積獎金。您的投注金額越高，贏得累積獎金的概率也越高。然而，任何高於[maxBet]的投注都不會進一步提高您的概率。
          <br><br>
          免費旋轉、紅利回合或使用紅利硬幣投下的投注都無法贏得累積獎金。
          <br><br>
          贏得累積獎金的玩家會看到通知消息，他們的帳戶也會存入相應資金。
        </div>
        <div class="ubo-popup-textlogo" data-color="shared">Shared Prize Jackpot</div>
        <div class="ubo-popup-text">
          當該累積獎金被贏走時，贏獎玩家獲得獎池的[mainPecent]，而勝於的[sharedPercent]則如下分配給所有符合資格的玩家：[sharedEven]平分，[sharedRelative]根據最近[eligibilityPeriod]個小時內每位玩家的投注按比例分配。
          <br><br>
          累積獎金金額下的累積條 <span class="progress-bar"><img src="https://origin8.m27613.com/sweh-assets/jackpot-info-popup/progress.png" alt=""> </span>
          顯示根據您在最近[eligibilityPeriod]個小時內的投注，您是否符合資格。如果累積條是滿的，您就有資格。
          <br><br>
          累積條會在您在最近[eligibilityPeriod]個小時內在所有參與活動的遊戲中的總投注為[eligibilityThreshold]或更多時被填滿。
          <br><br>
          即使您離線，您也會獲得累積獎金的一部分，只要您在累積獎金被贏走時滿足資格。
          <br><br>
          該累積獎金隨時都可能被贏走。該累積獎金被贏走後，新的漸進式累積獎金會在10秒內可用。
          <br><br>
          累積獎金其實和保證最低金額為[seed]
        </div>
        <div class="ubo-popup-textlogo" data-color="amount">Amount Jackpot</div>
        <div class="ubo-popup-text">
          累積獎金一定會達到[amountLimit]之前被贏走。贏得累積獎金的概率會隨著獎池中的金額而增加，達到指定金額時為100%。
          <br><br>
          該累積獎金在任何時候都可以被贏走，且只可以被一位玩家贏走。該累積獎金被贏走後，新的金額累積獎金將激活。
          <br><br>
          累積獎金的起始和保證最低金額為[seed]
        </div>
        <div class="ubo-popup-textlogo" data-color="time">Time Jackpot</div>
        <div class="ubo-popup-text">
          累積獎金一定會在指定日期和時間之前被贏走。贏得累積獎金的概率會隨著時間增加，達到指定時間時為100%。
          <br><br>
          該累積獎金在任何時候都可以被贏走，且只可以被一位玩家贏走。該累積獎金被贏走後，新的時間累積獎金將激活。
          <br><br>
          累積獎金的起始和保證最低金額為[seed]
        </div>
      </div>
    </div>
    <div class="ubo-popup-patterns"></div>
    <div class="ubo-popup-patterns right"></div>
  </div>
</div>
