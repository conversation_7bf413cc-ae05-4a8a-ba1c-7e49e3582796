import {
  JACKPOT_NAMES,
  JackpotUi,
  JackpotUiInfo,
  JackpotUiInfoPreviewItem,
  JackpotUiInfoPreviewPool,
  JackpotUiInfoTemplate,
  PayoutItem
} from '../../../../interfaces/jackpot';
import template from 'lodash.template';
import { SwuiCurrencySymbolPipe } from '@skywind-group/lib-swui';

export const DYNAMIC_TEMPLATES: Record<string, JackpotUiInfo> = {
  'en': { header: require('!raw-loader!../templates/dynamic/en.html').default, footer: '' },
  'id': { header: require('!raw-loader!../templates/dynamic/id.html').default, footer: '' },
  'it': { header: require('!raw-loader!../templates/dynamic/it.html').default, footer: '' },
  'ko': { header: require('!raw-loader!../templates/dynamic/ko.html').default, footer: '' },
  'ro': { header: require('!raw-loader!../templates/dynamic/ro.html').default, footer: '' },
  'zh-cn': { header: require('!raw-loader!../templates/dynamic/zh-cn.html').default, footer: '' },
  'es': { header: require('!raw-loader!../templates/dynamic/es.html').default, footer: '' },
  'th': { header: require('!raw-loader!../templates/dynamic/th.html').default, footer: '' },
  'vi': { header: require('!raw-loader!../templates/dynamic/vi.html').default, footer: '' },
  'da': { header: require('!raw-loader!../templates/dynamic/da.html').default, footer: '' },
  'de': { header: require('!raw-loader!../templates/dynamic/de.html').default, footer: '' },
  'fr': { header: require('!raw-loader!../templates/dynamic/fr.html').default, footer: '' },
  'ms': { header: require('!raw-loader!../templates/dynamic/ms.html').default, footer: '' },
  'sv': { header: require('!raw-loader!../templates/dynamic/sv.html').default, footer: '' },
  'sr': { header: require('!raw-loader!../templates/dynamic/sr.html').default, footer: '' },
  'el': { header: require('!raw-loader!../templates/dynamic/el.html').default, footer: '' },
  'ru': { header: require('!raw-loader!../templates/dynamic/ru.html').default, footer: '' },
  'uk': { header: require('!raw-loader!../templates/dynamic/uk.html').default, footer: '' },
  'ka': { header: require('!raw-loader!../templates/dynamic/ka.html').default, footer: '' },
  'pt': { header: require('!raw-loader!../templates/dynamic/pt.html').default, footer: '' },
  'pt-br': { header: require('!raw-loader!../templates/dynamic/pt-br.html').default, footer: '' },
  'zh-tw': { header: require('!raw-loader!../templates/dynamic/zh-tw.html').default, footer: '' },
  'nl': { header: require('!raw-loader!../templates/dynamic/nl.html').default, footer: '' },
  'ja': { header: require('!raw-loader!../templates/dynamic/ja.html').default, footer: '' },
};

export const TEMPLATES: Record<string, JackpotUiInfoTemplate> = {
  'en': {
    main: require('!raw-loader!../templates/static/en/main.html').default,
    amount: require('!raw-loader!../templates/static/en/amount.html').default,
    noLimit: require('!raw-loader!../templates/static/en/nolimit.html').default,
    time: require('!raw-loader!../templates/static/en/time.html').default
  },
  'it': {
    main: require('!raw-loader!../templates/static/it/main.html').default,
    amount: require('!raw-loader!../templates/static/it/amount.html').default,
    noLimit: require('!raw-loader!../templates/static/it/nolimit.html').default,
    time: require('!raw-loader!../templates/static/it/time.html').default
  }
};

export const TEMPLATE_LANGUAGES = Object.keys(TEMPLATES);

export function getTemplateName( poolName: string ): string | undefined {
  if (poolName === 'grand') {
    return 'noLimit';
  }
  if (['hourly', 'daily'].includes(poolName)) {
    return 'time';
  }
  if (['minor', 'major', 'mega'].includes(poolName)) {
    return 'amount';
  }
}

export function buildJackpotUiInfoPreviewItems( pools: string[], payout: PayoutItem, baseCurrency?: string,
                                                uiConfig?: JackpotUi ): JackpotUiInfoPreviewItem[] {
  const currencySymbol = baseCurrency ? new SwuiCurrencySymbolPipe().transform(baseCurrency) : '';
  return TEMPLATE_LANGUAGES.map(lang => {
    const sections: JackpotUiInfoPreviewPool[] = [];
    pools.forEach(( pool, index ) => {
      const pool_name = JACKPOT_NAMES.find(( { id } ) => id === pool)?.title;
      const config = uiConfig?.features?.pools.find(( { type } ) => type === pool);
      const item = payout.option.find(( { subType } ) => subType === pool);
      const name = getTemplateName(pool);
      if (name && item?.seed && item?.avgWin) {
        sections.push({
          name,
          priority: config?.priority ?? index,
          data: {
            pool_name: config?.info?.[lang]?.name || pool_name || '',
            seed: item.seed,
            avg_win: item.avgWin,
            currency_symbol: currencySymbol
          }
        });
      }
    });
    return {
      lang,
      rtp: payout.rtp,
      pools: sections.sort(( a, b ) => a.priority - b.priority)
    };
  });
}

export function buildJackpotUiInfo( value: JackpotUiInfoPreviewItem ): string | undefined {
  const { main, amount, noLimit, time } = TEMPLATES[value.lang];
  if (main) {
    const pools: string[] = [];
    value.pools.forEach(pool => {
      if (pool.name === 'amount' && amount) {
        pools.push(template(amount)(pool.data));
      }
      if (pool.name === 'noLimit' && noLimit) {
        pools.push(template(noLimit)(pool.data));
      }
      if (pool.name === 'time' && time) {
        pools.push(template(time)(pool.data));
      }
    });
    return template(main)({
      rtp: value.rtp,
      sections: pools.join('')
    });
  }
}
