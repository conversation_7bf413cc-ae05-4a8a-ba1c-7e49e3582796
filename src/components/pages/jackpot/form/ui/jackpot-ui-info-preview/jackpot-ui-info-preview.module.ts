import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { JackpotUiInfoPreviewComponent } from './jackpot-ui-info-preview.component';
import {
  JackpotUiInfoPreviewItemComponent
} from './jackpot-ui-info-preview-item/jackpot-ui-info-preview-item.component';
import { MatTabsModule } from '@angular/material/tabs';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatIconModule } from '@angular/material/icon';


@NgModule({
  declarations: [
    JackpotUiInfoPreviewComponent,
    JackpotUiInfoPreviewItemComponent
  ],
  exports: [
    JackpotUiInfoPreviewComponent
  ],
  imports: [
    CommonModule,
    MatTabsModule,
    MatButtonModule,
    MatTooltipModule,
    MatIconModule
  ]
})
export class JackpotUiInfoPreviewModule {
}
