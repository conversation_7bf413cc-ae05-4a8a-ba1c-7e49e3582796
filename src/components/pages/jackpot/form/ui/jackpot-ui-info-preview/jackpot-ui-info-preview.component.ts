import { Component, ElementRef, Input, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { JackpotPollDataOption, JackpotUi, JackpotUiInfoPreviewItem } from '../../../../interfaces/jackpot';
import { JackpotFormService } from '../../../jackpot-update/jackpot-form-service/jackpot-form.service';
import { takeUntil } from 'rxjs/operators';
import { ActivatedRoute } from '@angular/router';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { buildJackpotUiInfoPreviewItems } from '../templates';

@Component({
  selector: 'sw-jackpot-ui-info-preview',
  templateUrl: './jackpot-ui-info-preview.component.html'
})
export class JackpotUiInfoPreviewComponent implements OnInit, OnDestroy {
  @Input()
  set uiConfig( value: JackpotUi | undefined ) {
    this.uiConfig$.next(value);
  }

  @ViewChild('previewIframe') iframeRef?: ElementRef;

  contents: JackpotUiInfoPreviewItem[] = [];

  private readonly jackpotPools: JackpotPollDataOption[];
  private readonly uiConfig$ = new BehaviorSubject<JackpotUi | undefined>(undefined);
  private readonly destroyed = new Subject<void>();

  constructor( { snapshot: { data: { configuration } } }: ActivatedRoute,
               private readonly formService: JackpotFormService ) {
    this.jackpotPools = configuration?.pools ?? [];
  }

  ngOnInit(): void {
    combineLatest([
      this.formService.poolType$,
      this.formService.payout$,
      this.uiConfig$
    ]).pipe(
      takeUntil(this.destroyed)
    ).subscribe(( [poolType, { payout, baseCurrency }, uiConfig] ) => {
      const subTypes: string[] = this.jackpotPools.find(( { id } ) => id === poolType)?.subType ?? [];
      uiConfig?.features.pools.forEach(( { type }, index ) => {
        const option = payout?.option[index];
        if (option) {
          option.subType ??= type;
        }
      });
      if (payout && subTypes.length) {
        this.contents = buildJackpotUiInfoPreviewItems(subTypes, payout, baseCurrency, uiConfig);
      } else {
        this.contents = [];
      }
    });
  }

  ngOnDestroy() {
    this.destroyed.next();
    this.destroyed.complete();
  }
}
