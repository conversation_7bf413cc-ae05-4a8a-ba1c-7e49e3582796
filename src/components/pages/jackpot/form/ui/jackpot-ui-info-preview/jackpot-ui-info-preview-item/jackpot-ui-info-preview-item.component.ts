import { AfterViewInit, Component, ElementRef, Input, OnDestroy, ViewChild } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { buildJackpotUiInfo } from '../../templates';
import { BehaviorSubject, Subject } from 'rxjs';
import { createNewWindow, WindowOptions } from '../../../../../../../common/lib/window';
import { JackpotUiInfoPreviewItem } from '../../../../../interfaces/jackpot';

const WINDOW_OPTIONS: WindowOptions = { width: 500, height: 500, left: 0, top: 0, location: 0, toolbar: 0 };

@Component({
  selector: 'sw-jackpot-ui-info-preview-item',
  templateUrl: './jackpot-ui-info-preview-item.component.html',
  styleUrls: ['./jackpot-ui-info-preview-item.component.scss'],
})
export class JackpotUiInfoPreviewItemComponent implements AfterViewInit, OnDestroy {
  @Input()
  set value( value: JackpotUiInfoPreviewItem | undefined ) {
    this.value$.next(value);
  }

  @ViewChild('previewIframe') iframeRef?: ElementRef;

  private windowHandle?: Window;
  private doc?: string;

  private readonly value$ = new BehaviorSubject<JackpotUiInfoPreviewItem | undefined>(undefined);
  private readonly destroyed = new Subject<void>();

  constructor( private readonly sanitiser: DomSanitizer ) {
  }

  ngAfterViewInit() {
    this.value$.subscribe(value => {
      if (this.iframeRef && value) {
        const page = buildJackpotUiInfo(value);
        if (page) {
          this.doc = this.sanitiser.sanitize(0, page) as string;
          const iframe = this.iframeRef.nativeElement as HTMLIFrameElement;
          iframe.srcdoc = this.doc;
          if (this.windowHandle) {
            this.windowHandle.document.body.innerHTML = this.doc;
          }
        }
      }
    });
  }

  ngOnDestroy() {
    this.destroyed.next();
    this.destroyed.complete();
  }

  onPreview( event: Event ) {
    event.preventDefault();
    this.windowHandle = createNewWindow('', 'preview', WINDOW_OPTIONS);
    if (this.windowHandle) {
      const document = this.windowHandle.document;
      document.title = 'Preview';
      if (this.doc) {
        document.body.innerHTML = this.doc;
      }
    }
  }
}
