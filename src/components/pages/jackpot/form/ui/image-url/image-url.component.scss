.image-loader {
  display: flex;
  &__main {
    flex: 1;
    padding: 22px 32px 0 0;
  }
  &__group {
    display: flex;
    flex-direction: column;
  }
  &__item {
    display: flex;
    height: 48px;
  }
  &__radio {
    margin-right: 32px;
    padding-top: 8px;
    &--url {
      padding-top: 12px;
    }
  }
  &__preview-title {
    color: #a7a7a7;
    font-size: 14px;
    text-align: center;
  }
  &__input {
    width: 100%;
  }
  &__field {
    width: 100%;
  }
}

.file-control {
  display: flex;
  flex-direction: column;
  &.removable {
    .file-control {
      &__label {
        position: relative;
      }
      &__remove {
        display: flex;
      }
      &__img {
        display: block;
      }
    }
  }
  &__img {
    user-select: none;
  }
  &__wrapper {
    display: flex;
  }
  &__remove {
    position: absolute;
    top: 0;
    right: 0;
    display: none;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    line-height: 44px;
    background: rgba(0, 0, 0, .12);
    cursor: pointer;
  }
  &__button {
    position: relative;
    button {
      padding: 0 !important;
    }
  }
  &__error {
    position: absolute;
    bottom: -20px;
    left: 0;
    font-size: 12px;
    padding-left: 12px;
    line-height: 1.6;
  }
  &__label {
    display: block;
    height: 100%;
    width: 100%;
    margin: 0 !important;
    padding: 0 22px;
    cursor: pointer;
  }
  &__input {
    display: none;
  }
  &__hint {
    padding-top: 10px;
    margin-left: 8px;
    font-size: 14px;
  }
}

.image-area {
  height: 115px;
  &__body {
    position: relative;
    height: 90px;
    width: 190px;
    border: 1px solid rgba(0, 0, 0, 0.12);;
    border-radius: 3px;
    padding-top: 40%;
  }
  &__img {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    img {
      display: block;
      width: auto;
      max-width: 100%;
      height: 100%;
    }
    &.loaded {
      opacity: 1;
      transition: opacity 0.15s ease-in-out;
    }
  }
  &__spinner {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 3px;
    overflow: hidden;
  }
  &__clear {
    display: block;
    width: 100%;
    font-size: 14px;
    height: 24px;
    line-height: 24px;
    color: #1468cf;
  }
  &__load-error {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 500;
    color: #f44336;
    text-transform: uppercase;
    background-color: #fafafa;
    border-radius: 3px;
    overflow: hidden;
  }
}
