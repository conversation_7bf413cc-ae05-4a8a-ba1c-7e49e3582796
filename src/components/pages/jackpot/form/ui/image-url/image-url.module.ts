import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';

import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule } from '@skywind-group/lib-swui';
import { ImageUrlComponent } from './image-url.component';


@NgModule({
  declarations: [
    ImageUrlComponent
  ],
  exports: [
    ImageUrlComponent
  ],
  imports: [
    CommonModule,
    MatRadioModule,
    TranslateModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    FormsModule,
    SwuiControlMessagesModule,
    MatButtonModule,
    MatProgressSpinnerModule,
  ]
})
export class ImageUrlModule {
}
