import { Component, ElementRef, forwardRef, Input, OnD<PERSON>roy, ViewChild } from '@angular/core';
import { ControlValueAccessor, FormControl, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors } from '@angular/forms';
import { MatRadioChange } from '@angular/material/radio';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { debounceTime, map, takeUntil, tap } from 'rxjs/operators';
import { ImageUrlValidator } from '../../../../../../common/lib/validators';


@Component({
  selector: 'sw-image-url',
  templateUrl: './image-url.component.html',
  styleUrls: ['./image-url.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ImageUrlComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => ImageUrlComponent),
      multi: true
    }
  ]
})
export class ImageUrlComponent implements OnD<PERSON>roy, ControlValueAccessor {
  @Input() set isDisabled( isDisabled: boolean ) {
    this._isDisabled = isDisabled;
    isDisabled ? this.logoImagePathControl.disable({ emitEvent: false }) : this.logoImagePathControl.enable();
  }

  get isDisabled(): boolean {
    return this._isDisabled;
  }

  @Input()
  set defaultLogoImage( val: string | undefined ) {
    this._defaultLogoImage$.next(val || '');
  }

  @Input() defaultImages: { [key: string]: string } = {};


  @ViewChild('logo', { static: true }) logoRef?: ElementRef;
  @ViewChild('file', { static: true }) fileRef?: ElementRef;

  type: 'urlImage' | 'uploadImage' = 'urlImage';
  isUrlImage = true;
  loading = false;
  loadError = false;
  logoImagePathControl = new FormControl('');
  onChange: ( _: any ) => void = (() => {
  });

  private _isDisabled = false;
  private destroy$ = new Subject();
  private _defaultLogoImage$ = new BehaviorSubject<string>('');

  get image(): HTMLImageElement {
    return this.logoRef?.nativeElement;
  }

  constructor() {
    this.logoImagePathControl.setValidators(ImageUrlValidator);

    combineLatest([this._defaultLogoImage$, this.logoImagePathControl.valueChanges])
      .pipe(
        tap(() => this.image.removeAttribute('src')),
        tap(() => this.loading = true),
        debounceTime(300),
        map(( [defaultImage, logoImage] ) => {
          if (logoImage && !this.isImageUrlValid()) {
            this.loadError = true;
            logoImage = '';
          }

          const img = logoImage || defaultImage;
          this.onChange(img);

          return img;
        }),
        takeUntil(this.destroy$)
      )
      .subscribe(( imageSrc: string ) => {
        if (this.isImageUrlValid()) {
          this.image.src = imageSrc;
        } else {
          this.loading = false;
        }
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  triggerIsImageUrlType( event: MatRadioChange ) {
    this.logoImagePathControl.reset('');
    this.logoImagePathControl.updateValueAndValidity();
    this.isUrlImage = event.value === 'urlImage';
    this.type = this.isUrlImage ? 'urlImage' : 'uploadImage';
  }

  onIconFileChange( event: Event ) {
    const reader = new FileReader();
    const target = event.target as any;

    if (target.files && target.files.length) {
      const [file] = target.files;
      if (file.type === 'image/png' || file.type === 'image/jpeg') {
        reader.readAsDataURL(file);
      } else {
        this.logoImagePathControl.setErrors({ requiredFileType: true });
      }

      reader.onload = () => {
        this.logoImagePathControl.patchValue(reader.result);
        const imgValid = file.size <= 500000;
        if (!imgValid) {
          this.logoImagePathControl.setErrors({ invalidSize: true });
        } else {
          this.logoImagePathControl.setErrors(null);
        }
      };

      target.value = '';
    }
  }

  onLoad( event: Event ) {
    if (event.type === 'load') {
      this.loadError = false;
    }
    this.loading = false;
  }

  onError() {
    this.loadError = true;
    this.loading = false;
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  writeValue( val: string ): void {
    if (val) {
      this.isUrlImage = !val.startsWith('data') || this.isDefaultImage(val);
      this.type = this.isUrlImage ? 'urlImage' : 'uploadImage';
    }

    this.logoImagePathControl.setValue(val && !this.isDefaultImage(val) ? val : '');
  }

  setDisabledState( isDisabled: boolean ) {
    this.isDisabled = isDisabled;
  }

  validate(): ValidationErrors | null {
    return this.logoImagePathControl.valid ? null : { invalidImage: true };
  }

  private isDefaultImage( image: string ): boolean {
    return !!Object.keys(this.defaultImages).find(( key: string ) => this.defaultImages[key] === image);
  }

  private isImageUrlValid() {
    const regexpUrl = /(((http:\/\/www)|(http:\/\/)|(https:\/\/)|(www))[-a-zA-Z0-9@:%_\+.~#?&//=]+)\.(jpg|jpeg|gif|png|bmp|tiff|tga|svg)/g;
    const regexpBase64 = /^\s*data:([image])([a-z]+\/[a-z]+(;[a-z\-]+\=[a-z\-]+)?)?(;base64)?,[a-z0-9\!\$\&\'\,\(\)\*\+\,\;\=\-\.\_\~\:\@\/\?\%\s]*\s*$/i;
    const value = this.logoImagePathControl.value === null ? '' : this.logoImagePathControl.value.toString();
    return !value || value.match(regexpUrl) || value.match(regexpBase64);
  }
}
