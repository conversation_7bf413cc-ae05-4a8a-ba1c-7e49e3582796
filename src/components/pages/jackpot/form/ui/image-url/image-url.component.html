<div class="image-loader">
  <div class="image-loader__main">
    <mat-radio-group
      class="image-loader__group"
      [value]="type"
      [disabled]="!!logoImagePathControl.value || isDisabled"
      fxLayout="column">
      <div class="image-loader__item">
        <mat-radio-button
          class="image-loader__radio image-loader__radio--url"
          [value]="'urlImage'"
          (change)="triggerIsImageUrlType($event)">
          {{ 'JACKPOT.UI.imageURL' | translate }}
        </mat-radio-button>
        <mat-form-field *ngIf="isUrlImage" class="image-loader__input" appearance="outline">
          <input
            matInput
            type="text"
            [placeholder]="'JACKPOT.UI.imageURL' | translate"
            [formControl]="logoImagePathControl">
          <mat-error>
            <lib-swui-control-messages [control]="logoImagePathControl"></lib-swui-control-messages>
          </mat-error>
        </mat-form-field>
      </div>

      <div class="image-loader__item">
        <mat-radio-button
          class="image-loader__radio"
          [value]="'uploadImage'"
          (change)="triggerIsImageUrlType($event)">
          {{ 'JACKPOT.UI.uploadImage' | translate }}
        </mat-radio-button>

        <div *ngIf="!isUrlImage" class="file-control" [ngClass]="{ 'removable': logoImagePathControl.value }">
          <div class="file-control__wrapper">
            <div class="file-control__button">
              <button
                mat-flat-button
                color="primary"
                [disabled]="isDisabled">
                <label class="file-control__label">
                  {{ 'JACKPOT.UI.browse' | translate }}
                  <input
                    #file
                    type="file"
                    accept="image/jpeg,image/png"
                    class="file-control__input"
                    [disabled]="isDisabled"
                    (change)="onIconFileChange($event)">
                </label>
              </button>
              <mat-error class="file-control__error" *ngIf="logoImagePathControl.hasError('invalidSize')">
                {{'VALIDATION.invalidSize' | translate}}
              </mat-error>
              <mat-error class="file-control__error" *ngIf="logoImagePathControl.hasError('requiredFileType')">
                {{'VALIDATION.requiredFileType' | translate}}
              </mat-error>
            </div>
          </div>
        </div>
      </div>
    </mat-radio-group>
  </div>

  <div class="image-loader__image">
    <div class="image-loader__preview-title">
      Preview
    </div>
    <div class="image-area">
      <div class="image-area__body">
        <div class="image-area__img" [ngClass]="{'loaded': !loading}">
          <img #logo alt (load)="onLoad($event)" (error)="onError()">
        </div>
        <div class="image-area__spinner" *ngIf="loading">
          <mat-spinner [diameter]="24"></mat-spinner>
        </div>
        <div class="image-area__load-error" *ngIf="loadError">
          Image not found
        </div>
      </div>

      <div class="image-area__footer">
        <button
          *ngIf="logoImagePathControl.value"
          mat-button
          class="image-area__clear"
          [disabled]="isDisabled"
          (click)="logoImagePathControl.reset('')">
          {{ 'JACKPOT.UI.deleteImage' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>
