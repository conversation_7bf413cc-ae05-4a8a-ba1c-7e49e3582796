import { AfterViewChecked, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, ValidationErrors } from '@angular/forms';
import { MatTabGroup } from '@angular/material/tabs';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { filter, takeUntil } from 'rxjs/operators';
import {
  AbstractFormValueAccessor,
  formValueProviders
} from '../../../../../../common/lib/abstract-form-value-accessor';
import { JackpotUiLanguageService } from '../jackpot-ui-language.service';
import { TranslateService } from '@ngx-translate/core';


export const AVAILABLE_LANGUAGES: SwuiSelectOption[] = [
  { id: 'en', text: 'LANGUAGES.ENGLISH.en' },
  { id: 'id', text: 'LANGUAGES.ENGLISH.id' },
  { id: 'ms', text: 'LANGUAGES.ENGLISH.ms' },
  { id: 'da', text: 'LANGUAGES.ENGLISH.da' },
  { id: 'it', text: 'LANGUAGES.ENGLISH.it' },
  { id: 'ko', text: 'LANGUAGES.ENGLISH.ko' },
  { id: 'ro', text: 'LANGUAGES.ENGLISH.ro' },
  { id: 'zh-cn', text: 'LANGUAGES.ENGLISH.zh-cn' },
  { id: 'es', text: 'LANGUAGES.ENGLISH.es' },
  { id: 'sv', text: 'LANGUAGES.ENGLISH.sv' },
  { id: 'th', text: 'LANGUAGES.ENGLISH.th' },
  { id: 'vi', text: 'LANGUAGES.ENGLISH.vi' },
  { id: 'fr', text: 'LANGUAGES.ENGLISH.fr' },
  { id: 'sr', text: 'LANGUAGES.ENGLISH.sr' },
  { id: 'el', text: 'LANGUAGES.ENGLISH.el' },
  { id: 'ru', text: 'LANGUAGES.ENGLISH.ru' },
  { id: 'de', text: 'LANGUAGES.ENGLISH.de' },
  { id: 'uk', text: 'LANGUAGES.ENGLISH.uk' },
  { id: 'ka', text: 'LANGUAGES.ENGLISH.ka' },
  { id: 'pt', text: 'LANGUAGES.ENGLISH.pt' },
  { id: 'pt-br', text: 'LANGUAGES.ENGLISH.pt-br' },
  { id: 'zh-tw', text: 'LANGUAGES.ENGLISH.zh-tw' },
  { id: 'nl', text: 'LANGUAGES.ENGLISH.nl' },
  { id: 'ja', text: 'LANGUAGES.ENGLISH.ja' }
];

@Component({
  selector: 'sw-translations-manager',
  templateUrl: './translations-manager.component.html',
  styleUrls: ['./translations-manager.component.scss'],
  providers: formValueProviders(TranslationsManagerComponent)
})
export class TranslationsManagerComponent extends AbstractFormValueAccessor<{ [lang: string]: any }>
  implements OnInit, AfterViewChecked {

  @Input() set templateType( val: string | undefined ) {
    if (!val) {
      return;
    }
    this.infoArray?.controls.forEach((control) => {
      control.get('footer')?.patchValue(null);
    });
  }

  @Input()
  set languages( val: SwuiSelectOption[] ) {
    if (!val || Array.isArray(val)) {
      return;
    }
    this._languages = val;
  }

  get languages(): SwuiSelectOption[] {
    return this._languages.sort(( a, b ) => {
      return (this.translateService.instant(a.text)).localeCompare(this.translateService.instant(b.text));
    });
  }

  @Input('component')
  set childComponent( value: any ) {
    if (!value) {
      return;
    }
    this._childFormComponent = value;
  }

  get childComponent(): any {
    return this._childFormComponent;
  }

  @Input() set submitted( val: boolean | undefined ) {
    if (!val) {
      return;
    }
    this._submitted = val;
    this.switchToFirstInvalidTab();
  }

  get submitted(): boolean | undefined {
    return this._submitted;
  }

  @Input() isDisabled?: boolean;
  @Input() withoutDefault = false;

  @Output() addLang = new EventEmitter<AbstractControl>();

  readonly form: FormGroup;

  selectedTabIndex = -1;
  availableLanguages: SwuiSelectOption[] = [];
  @ViewChild('tabSet', { static: true }) tabsRef: MatTabGroup | undefined;

  private _submitted = false;
  private _childFormComponent: any;
  private _languages: SwuiSelectOption[] = AVAILABLE_LANGUAGES;

  constructor(
    private fb: FormBuilder,
    private uiLanguageService: JackpotUiLanguageService,
    private translateService: TranslateService
  ) {
    super();
    this.availableLanguages = this.languages;
    this.form = this.initForm();
  }

  ngOnInit(): void {
    super.ngOnInit();
    this.listenLanguages();

    if (!this.withoutDefault) {
      this.setDefaultLang('en');
    }

    if (this.childComponent) {
      this.childComponent.form.valueChanges
        .pipe(
          filter(val => !!val),
          takeUntil(this.destroyed)
        ).subscribe(( val: any ) => {
        const childControl = this.infoArray.controls.find(( control: AbstractControl ) => {
          const childIdControl = control.get('id') as FormControl;
          return childIdControl.value === val.id;
        });

        if (childControl) {
          childControl.patchValue(val);
        }
      });

      this.childComponent.form.statusChanges
        .pipe(
          takeUntil(this.destroyed)
        )
        .subscribe(() => {
          if (this.childComponent.form.invalid) {
            if (this.infoArray.controls.length && this.selectedTabIndex !== -1) {
              this.infoArray.controls[this.selectedTabIndex].setErrors({ invalid: true });
            }
          } else {
            if (this.infoArray.controls.length && this.selectedTabIndex !== -1) {
              this.infoArray.controls[this.selectedTabIndex].setErrors(null);
            }
          }
          if (this.childComponent.form.touched) {
            this.onTouched();
            if (this.infoArray.controls.length && this.selectedTabIndex !== -1) {
              this.infoArray.controls[this.selectedTabIndex].markAsTouched();
            }
          }
        });
    }
  }

  ngAfterViewChecked(): void {
    if (this.tabsRef) {
      this.tabsRef.realignInkBar();
    }
  }

  get infoArray(): FormArray {
    return this.form.get('info') as FormArray;
  }

  writeValue( value: { [lang: string]: any } | undefined ): void {
    if (typeof value === 'undefined' || value === null || (value && !Object.keys(value).length)) {
      return;
    }

    this.patchForm(value);
  }

  validate(): ValidationErrors | null {
    if (!this.infoArray.controls.length) {
      return { invalidForm: { valid: false } };
    }
    return super.validate();
  }

  addTab( lang: string ) {
    this.infoArray.push(this.initLangGroup({ id: lang }));
    this.selectedTabIndex = this.infoArray.controls.length - 1;
    this.uiLanguageService.addLanguages([lang]);
    this.addLang.emit(this.infoArray.controls[this.selectedTabIndex]);
  }

  removeTab( index?: number ) {
    if (!this.isDisabled) {
      const indexToRemove = index === undefined ? this.selectedTabIndex : index;

      const control = this.infoArray.controls[indexToRemove].get('id') as FormControl;
      const removedLang = control.value;

      const langSet = new Set(this.availableLanguages.map(( lang: SwuiSelectOption ) => lang.id));
      langSet.add(removedLang);

      this.availableLanguages = this.languages.reduce(( result: SwuiSelectOption[], option: SwuiSelectOption ) => {
        if (langSet.has(option.id)) {
          result.push(option);
        }
        return result;
      }, []);

      if (this.selectedTabIndex === indexToRemove) {
        if (this.tabsRef && (this.selectedTabIndex === this.tabsRef._tabs.length - 2 || this.availableLanguages.length === 1)) {
          this.selectedTabIndex = indexToRemove - 1;
        } else {
          const val = this.infoArray.at(indexToRemove + 1).value;
          if (this.childComponent) {
            this.childComponent.setValue(val);
          }
        }
      }

      const { id } = this.infoArray.controls[indexToRemove].value;
      this.infoArray.removeAt(indexToRemove);

      if (index === undefined) {
        this.uiLanguageService.removeLanguage(id);
      }
    }
  }

  prevent( event: Event ) {
    event.preventDefault();
    event.stopPropagation();
  }

  onSelectedIndexChange( index: number ) {
    this.selectedTabIndex = index;

    if (index !== -1 && this.infoArray.at(index).value && this.childComponent) {
      const val = this.infoArray.at(index).value;
      this.childComponent.setValue(val);
    }
  }

  trackByFn( _: number, control: AbstractControl ): string {
    return control.value.id;
  }

  protected transformForm( { info }: any ): { [lang: string]: any } {
    return info.reduce(( result: { [lang: string]: any }, { id, ...item }: any ) => ({ ...result, [id]: item }), {});
  }

  private setDefaultLang( lang: string ) {
    this.infoArray.push(this.initLangGroup({ id: lang }));
    this.selectedTabIndex = this.infoArray.controls.length - 1;

    if (this.childComponent) {
      this.childComponent.setValue({ id: lang });
    }
  }

  private patchForm( value: { [lang: string]: any } ) {
    const processedValue = Object.entries(value).map(( [id, data] ) => ({ ...data, id }));

    this.infoArray.clear();
    processedValue.forEach(val => {
      this.infoArray.push(this.initLangGroup(val));
    });

    this.form.patchValue({ info: processedValue }, { emitEvent: false });

    if (this.childComponent) {
      this.childComponent.setValue(processedValue[0]);
    }
    this.selectedTabIndex = 0;
    this.form.markAsPristine();
    this.form.markAsUntouched();
  }

  private initForm(): FormGroup {
    return this.fb.group({
      info: this.fb.array([])
    });
  }

  private initLangGroup( val: any ): FormGroup {
    this.availableLanguages = this.availableLanguages.filter(( language: SwuiSelectOption ) => language.id !== val.id);
    return this.childComponent ? this.childComponent.initForm(val) : this.fb.group({ id: val.id });
  }

  private switchToFirstInvalidTab() {
    const controls = this.infoArray.controls;
    const firstInvalid = controls.find(control => control.status === 'INVALID');
    if (this.tabsRef && firstInvalid) {
      this.tabsRef.selectedIndex = controls.indexOf(firstInvalid);
    }
  }

  private listenLanguages() {
    this.uiLanguageService.languages$
      .pipe(takeUntil(this.destroyed))
      .subscribe(languages => {
        const indexes = this.infoArray.controls
          .reduce(( res: number[], { value }, index ) => {
            if (!languages.includes(value.id)) {
              res.unshift(index);
            }
            return res;
          }, []);

        indexes.forEach(index => {
          this.removeTab(index);
        });

        languages.forEach(language => {
          const availableLanguages = this.availableLanguages.map(( { id } ) => id);
          if (availableLanguages.includes(language)) {
            this.addTab(language);
          }
        });
      });
  }
}
