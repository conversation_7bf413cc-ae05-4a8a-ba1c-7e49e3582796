<form [formGroup]="form" class="sw-translations">
  <div class="sw-translations__languages">
    <mat-tab-group
      #tabSet
      class="nested"
      animationDuration="0ms"
      formArrayName="info"
      [style.display]="!infoArray.controls.length ? 'none' : 'flex'"
      [selectedIndex]="selectedTabIndex"
      [disableRipple]="true"
      (selectedIndexChange)="onSelectedIndexChange($event)">

      <mat-tab *ngFor="let lang of infoArray.controls; trackBy: trackByFn; let index = index"
               [formGroup]="infoArray.controls[index]">
        <ng-template matTabLabel>
        <span [ngClass]="{'invalid': lang | swIsControlInvalid}">
          {{lang?.get('id')?.value | uppercase}}
        </span>
        </ng-template>
      </mat-tab>
      <mat-tab *ngIf="infoArray.controls.length && availableLanguages.length">
        <ng-template matTabLabel>
          <ng-container *ngTemplateOutlet="add"></ng-container>
        </ng-template>
      </mat-tab>
    </mat-tab-group>
    <div *ngIf="!infoArray.controls.length">
      <ng-container *ngTemplateOutlet="add"></ng-container>
      <span class="invalid" *ngIf="submitted">At least one language should be added</span>
    </div>
  </div>
  <div class="sw-translations__body" *ngIf="this.selectedTabIndex !== -1">
    <button
      mat-button
      color="primary"
      (click)="removeTab()"
      class="sw-translations__action sw-translations__action--delete mat-button-xs"
      [ngClass]="{'disabled': isDisabled}">
      <mat-icon>remove</mat-icon>
      Delete language
    </button>
    <ng-content></ng-content>
  </div>
</form>

<ng-template #add>
  <button
    mat-button
    color="primary"
    [matMenuTriggerFor]="menu"
    [disabled]="isDisabled"
    (click)="prevent($event)"
    class="sw-translations__action sw-translations__action--add">
    <mat-icon>add</mat-icon>
    Add language
  </button>
  <mat-menu #menu>
    <div class="sw-translations__menu">
      <button mat-menu-item *ngFor="let lang of availableLanguages" (click)="addTab(lang.id)">
        {{lang.text | translate}}
      </button>
    </div>
  </mat-menu>
</ng-template>
