import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl } from '@angular/forms';

import { BaseComponent } from '../../../../../../common/components/base.component';
import { JSONValidator } from '../../../../../../common/lib/validators';
import { JackpotUi } from '../../../../interfaces/jackpot';

@Component({
  selector: 'sw-jackpot-ui-advanced',
  templateUrl: './jackpot-ui-advanced.component.html',
})
export class JackpotUiAdvancedComponent extends BaseComponent implements OnInit {
  jsonControl: FormControl;

  @Input()
  set submitted( val: boolean ) {
    if (!!val) {
      this.jsonControl.markAsTouched();
    }
  }

  @Input()
  set uiConfig( val: JackpotUi | undefined ) {
    this.jsonControl.patchValue(JSON.stringify(val, undefined, 4), { emitEvent: false });
  }

  @Input() isDisabled?: boolean;

  @Output() configChanged: EventEmitter<JackpotUi> = new EventEmitter<JackpotUi>();

  constructor( private fb: FormBuilder ) {
    super();
    this.jsonControl = this.fb.control('', JSONValidator);
  }

  ngOnInit(): void {
    if (this.isDisabled) {
      this.jsonControl.disable({ emitEvent: false });
    }
  }

  onConfigChanged() {
    let value;

    try {
      value = JSON.parse(this.jsonControl.value);
    } catch (e) {
    }

    if (value) {
      this.configChanged.emit(value);
    }
  }
}
