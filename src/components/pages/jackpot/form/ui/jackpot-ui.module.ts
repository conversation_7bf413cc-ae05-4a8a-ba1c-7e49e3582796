import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AngularEditorModule } from '@kolkov/angular-editor';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiIsControlInvalidModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { DragulaModule } from 'ng2-dragula';
import { TrimFormatterModule } from '../../../../../common/directives/trimFormatter/trimFormatter.module';
import { EngagementBarModule } from './engagement-bar/engagement-bar.module';
import { ImageUrlModule } from './image-url/image-url.module';

import { JackpotUiAdvancedComponent } from './jackpot-ui-advanced/jackpot-ui-advanced.component';
import {
  JackpotUiEditDialogComponent
} from './jackpot-ui-info/jackpot-ui-edit-dialog/jackpot-ui-edit-dialog.component';
import { JackpotUiInfoComponent } from './jackpot-ui-info/jackpot-ui-info.component';
import {
  JackpotUiLobbyGamesComponent
} from './jackpot-ui-lobby/jackpot-ui-lobby-games/jackpot-ui-lobby-games.component';
import { JackpotUiLobbyComponent } from './jackpot-ui-lobby/jackpot-ui-lobby.component';
import { JackpotUiPoolInfoComponent } from './jackpot-ui-pool/jackpot-ui-pool-info/jackpot-ui-pool-info.component';
import { JackpotUiPoolComponent } from './jackpot-ui-pool/jackpot-ui-pool.component';
import { JackpotUiComponent } from './jackpot-ui.component';
import { TranslationsManagerModule } from './translations-manager/translations-manager.module';
import { UploadImageModule } from './upload-image/upload-image.module';
import {
  JackpotUiPoolTickupComponent
} from './jackpot-ui-pool/jackpot-ui-pool-tickup/jackpot-ui-pool-tickup.component';
import { JackpotUiInfoPreviewModule } from './jackpot-ui-info-preview/jackpot-ui-info-preview.module';


@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    SwuiSelectModule,
    FlexLayoutModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    MatTabsModule,
    MatCheckboxModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    TranslationsManagerModule,
    SwuiControlMessagesModule,
    DragulaModule,
    AngularEditorModule,
    MatRadioModule,
    UploadImageModule,
    EngagementBarModule,
    ImageUrlModule,
    MatSelectModule,
    TrimFormatterModule,
    SwuiIsControlInvalidModule,
    MatSlideToggleModule,
    JackpotUiInfoPreviewModule,
  ],
  exports: [
    JackpotUiComponent,
  ],
  declarations: [
    JackpotUiComponent,
    JackpotUiPoolComponent,
    JackpotUiPoolInfoComponent,
    JackpotUiPoolTickupComponent,
    JackpotUiInfoComponent,
    JackpotUiLobbyComponent,
    JackpotUiLobbyGamesComponent,
    JackpotUiAdvancedComponent,
    JackpotUiEditDialogComponent,
  ],
  entryComponents: [
    JackpotUiEditDialogComponent,
  ]
})
export class JackpotUiModule {
}
