import { AfterViewChecked, Component, Input, OnInit, ViewChild } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatTabGroup } from '@angular/material/tabs';
import { ActivatedRoute } from '@angular/router';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { debounceTime, delay, filter, takeUntil } from 'rxjs/operators';
import { AbstractFormValueAccessor, formValueProviders } from '../../../../../common/lib/abstract-form-value-accessor';
import { urlValidator } from '../../../../../common/lib/validators';
import { GameInfo } from '../../../../../common/models/game';
import {
  JACKPOT_NAMES,
  JackpotPollDataOption,
  JackpotUi,
  JackpotUiPool,
  READONLY_JURISDICTION_ID,
} from '../../../interfaces/jackpot';
import { JackpotFormService } from '../../jackpot-update/jackpot-form-service/jackpot-form.service';
import { JackpotUiLanguageService } from './jackpot-ui-language.service';
import { buildJackpotUiInfo, buildJackpotUiInfoPreviewItems, DYNAMIC_TEMPLATES } from './templates';

@Component({
  selector: 'sw-jackpot-ui',
  templateUrl: './jackpot-ui.component.html',
  styleUrls: ['./jackpot-ui.component.scss'],
  providers: [
    JackpotUiLanguageService,
    ...formValueProviders(JackpotUiComponent)
  ],
})
export class JackpotUiComponent extends AbstractFormValueAccessor<JackpotUi> implements OnInit, AfterViewChecked {
  @Input() games: string[] = [];
  @Input() sourceGames: GameInfo[] = [];

  @Input('jurisdiction') set setJurisdiction( value: string ) {
    this.infoPopupReadonly = value === READONLY_JURISDICTION_ID;
    if (this.infoPopupReadonly) {
      this.infoControl.disable({ emitEvent: false });
      this.infoUrlControl.disable({ emitEvent: false });
    } else {
      this.infoControl.enable({ emitEvent: false });
      this.infoUrlControl.enable({ emitEvent: false });
    }
  }

  isDisabled?: boolean;
  pools: string[] = [];
  priorities: SwuiSelectOption[] = [];
  selectedIndex = 0;

  readonly form: FormGroup;
  readonly infoPopupControl = new FormControl('editor');

  readonly availableTemplates = [
    { id: 'asia', text: 'Asia' },
    { id: 'eu', text: 'Europe' }
  ];

  readonly languages$ = this.uiLanguageService.languages$;
  @ViewChild('tabs', { static: false }) readonly tabs?: MatTabGroup;
  infoPopupReadonly = false;

  private readonly jackpotPools: JackpotPollDataOption[];

  constructor( private readonly fb: FormBuilder,
               private readonly formService: JackpotFormService,
               { snapshot: { data: { configuration } } }: ActivatedRoute,
               private readonly uiLanguageService: JackpotUiLanguageService,
  ) {
    super();
    this.jackpotPools = configuration?.pools || [];
    this.form = this.initForm();
  }

  ngOnInit(): void {
    this.form.valueChanges.pipe(
      debounceTime(100),
      delay(0),
      takeUntil(this.destroyed)
    ).subscribe(value => {
      if (this.onChange) {
        this.onChange(this.transformForm(value));
      }
    });
    this.form.statusChanges.pipe(
      debounceTime(100),
      delay(0),
      takeUntil(this.destroyed)
    ).subscribe(() => {
      if (this.onValidatorChange) {
        this.onValidatorChange();
      }
    });

    this.languages$
      .pipe(takeUntil(this.destroyed))
      .subscribe(langs => {
        if (!langs.includes(this.defaultLanguage.value)) {
          this.defaultLanguage.setValue(langs[0]);
        }
      });

    this.formService.poolType$
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: string ) => {
        this.poolsControl.clear();
        const jackpotsPoolData = this.jackpotPools.find(( el: JackpotPollDataOption ) => el.id === val);
        this.pools = jackpotsPoolData ? jackpotsPoolData.subType : [];

        this.pools.forEach(( item: string ) => {
          const control = this.fb.control('');
          control.patchValue({ type: item });
          this.poolsControl.push(control);
        });

        this.updateTabIndex();
        this.setupPriorities(this.pools);
      });

    this.formService.formSubmitted$.pipe(
      filter(value => value),
      takeUntil(this.destroyed)
    ).subscribe(() => {
      this.form.markAllAsTouched();
      this.switchToFirstInvalidTab();
    });

    this.infoPopupControl.valueChanges
      .pipe(
        debounceTime(100),
        delay(0),
        takeUntil(this.destroyed)
      )
      .subscribe(val => {
        if (val === 'editor') {
          this.infoUrlControl.reset();
          this.infoUrlControl.clearValidators();
        } else {
          this.infoControl.reset();
          this.infoUrlControl.setValidators(Validators.compose([Validators.required, urlValidator]));
        }
        this.infoUrlControl.updateValueAndValidity();
      });

    this.templateTypeControl.valueChanges
      .pipe(takeUntil(this.destroyed))
      .subscribe(value => {
        if (value === 'asia') {
          this.infoPopupControl.setValue('editor');
        }
      });
  }

  writeValue( value: JackpotUi | undefined ): void {
    this.updateForm(value);
  }

  onConfigChange( config: JackpotUi ) {
    if (config) {
      this.form.patchValue(config, { emitEvent: false });
      this.updateForm(config);
    }
  }

  getJackpotTitle( jackpot: JackpotUiPool ): string {
    const jp = JACKPOT_NAMES.find(el => el.id === jackpot.type);
    return jp ? jp.title : '';
  }

  ngAfterViewChecked() {
    if (this.tabs) {
      this.tabs.realignInkBar();
    }
  }

  onAddLang( formControl: AbstractControl ) {
    const tmpl = DYNAMIC_TEMPLATES[formControl.value.id];
    if (tmpl) {
      formControl.patchValue(tmpl);
    }
  }

  get templateTypeControl(): FormControl {
    return this.form.get('type') as FormControl;
  }

  get defaultLanguage(): FormControl {
    return this.form.get('defaultLanguage') as FormControl;
  }

  get featuresControl(): FormGroup {
    return this.form.get('features') as FormGroup;
  }

  get poolsControl(): FormArray {
    return this.featuresControl.get('pools') as FormArray;
  }

  get engagementControl(): FormControl {
    return this.featuresControl.get('engagement') as FormControl;
  }

  get infoControl(): FormControl {
    return this.featuresControl.get('info') as FormControl;
  }

  get infoUrlControl(): FormControl {
    return this.featuresControl.get('infoUrl') as FormControl;
  }

  get lobbyControl(): FormControl {
    return this.featuresControl.get('lobby') as FormControl;
  }

  setDisabledState( isDisabled: boolean ): void {
    if (isDisabled) {
      this.form.disable({ emitEvent: false });
      this.infoPopupControl.disable();
    } else {
      this.form.enable({ emitEvent: false });
      this.infoPopupControl.enable();
    }
    this.isDisabled = isDisabled;
  }

  replaceJackpotPoolsType( value: JackpotUi ): JackpotUi {
    const subType = this.jackpotPools.find(item => item.id === value.poolId)?.subType;
    if (value && value.features.pools) {
      value.features.pools = value.features.pools.reduce(( acc: any, cur: any, index: number ) => {
        if (subType && subType[index]) {
          cur.type = subType[index];
          acc.push(cur);
        }
        return acc;
      }, []);
    }
    return value;
  }

  protected transformForm( value: any ): JackpotUi | undefined {
    if (this.infoPopupReadonly) {
      const poolType = this.formService.poolType$.value;
      const { payout, baseCurrency } = this.formService.payout$.value;
      const pools: string[] = this.jackpotPools.find(( { id } ) => id === poolType)?.subType ?? [];
      if (payout && pools.length) {
        const contents = buildJackpotUiInfoPreviewItems(pools, payout, baseCurrency, value);
        const info = contents.reduce(( result, content ) => ({
          ...result,
          [content.lang]: {
            header: buildJackpotUiInfo(content)
          }
        }), {});
        return {
          ...value,
          features: {
            ...value.features,
            info
          }
        };
      }
    }
    return value;
  }

  protected transformValue( value: JackpotUi ): any {
    value = this.replaceJackpotPoolsType(value);
    return {
      ...value,
      features: {
        ...value.features,
        info: {
          ...(value.features?.info || {})
        }
      }
    };
  }

  private updateForm( value: JackpotUi | undefined ) {
    if (typeof value === 'undefined' || value === null) {
      return;
    }
    const languages = [];
    if (value.features) {
      this.infoPopupControl.setValue(value.features.infoUrl ? 'url' : 'editor');
      languages.push(...Object.keys(value.features.info || {}));
      languages.push(...Object.keys(value.features.engagement?.imageUrl || {}));

      (value.features.pools || []).forEach(pool => {
        languages.push(...Object.keys(pool.info || {}));
      });
    }
    this.uiLanguageService.setLanguages(languages);
    this.form.patchValue(this.transformValue(value), { emitEvent: false });
  }

  private setupPriorities( pools: string[] ) {
    this.priorities = [];
    for (let i = 1; i <= pools.length; i++) {
      this.priorities.push({ id: i.toString(), text: i.toString() });
    }
  }

  private switchToFirstInvalidTab() {
    const featureControls: AbstractControl[] = [
      this.poolsControl,
      this.engagementControl,
      this.infoUrlControl,
      this.lobbyControl,
    ];
    const firstInvalid = featureControls.find(control => !!control.invalid);
    if (this.tabs && firstInvalid) {
      if (this.poolsControl.invalid) {
        const poolsControls = this.poolsControl.controls;
        const firstPoolsInvalid = this.poolsControl.controls.find(control => control.invalid);
        if (firstPoolsInvalid) {
          this.tabs.selectedIndex = poolsControls.indexOf(firstPoolsInvalid);
        }
      } else {
        this.tabs.selectedIndex = this.poolsControl.controls.length - 1 + featureControls.indexOf(firstInvalid);
      }
    }
  }

  private updateTabIndex() {
    this.selectedIndex = 1;
    setTimeout(() => {
      this.selectedIndex = 0;
    }, 0);
  }

  private initForm(): FormGroup {
    return this.fb.group({
      type: ['eu'],
      defaultLanguage: [],
      assets: [],
      features: this.fb.group({
        pools: this.fb.array([]),
        engagement: [],
        infoUrl: [],
        info: [],
        lobby: [],
      })
    });
  }
}
