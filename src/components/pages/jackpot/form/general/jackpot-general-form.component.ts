import { Component, forwardRef, Input, OnChanges, OnInit } from '@angular/core';
import {
  ControlValueAccessor,
  FormBuilder,
  FormControl,
  FormGroup,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ValidationErrors,
  Validator,
  Validators
} from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { delay, takeUntil } from 'rxjs/operators';

import { BaseComponent } from '../../../../../common/components/base.component';
import { FeatureGeneral } from '../../../interfaces/feature';
import { JackpotJurisdictionDataOption } from '../../../interfaces/jackpot';

function jurisdictionsToSelectOptions( jurisdictions: JackpotJurisdictionDataOption[] ): SwuiSelectOption[] {
  return jurisdictions ? jurisdictions.map(( el: JackpotJurisdictionDataOption ) => {
    return { id: el.id || '', text: el.title || '' };
  }) : [];
}

const UNREGULATED = 'ph-must-win-multi-type';

@Component({
  selector: 'sw-jackpot-general-form',
  templateUrl: './jackpot-general-form.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => JackpotGeneralFormComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => JackpotGeneralFormComponent),
      multi: true
    }
  ]
})
export class JackpotGeneralFormComponent extends BaseComponent implements ControlValueAccessor, Validator, OnInit,
  OnChanges {
  @Input() status: string | undefined;

  readonly form: FormGroup;
  onChange: ( _: any ) => void = (() => {
  });

  maxNumberInputLength = 60;

  readonly jurisdictions: SwuiSelectOption[];

  constructor( private readonly fb: FormBuilder,
               { snapshot: { data: { configuration } } }: ActivatedRoute,
  ) {
    super();
    this.jurisdictions = jurisdictionsToSelectOptions(configuration?.jurisdictions);
    this.form = this.initForm();
  }

  ngOnChanges() {
    if (this.status === 'running' || this.status === 'pending') {
      this.jurisdictionControl.disable({ emitEvent: false });
    }
  }

  ngOnInit() {
    this.form.valueChanges
      .pipe(
        delay(0),
        takeUntil(this.destroyed)
      )
      .subscribe(
        ( val: any ) => {
          this.onChange(val);
        });
  }

  onTouched: () => void = () => {
  };

  writeValue( val: FeatureGeneral ): void {
    if (!val) {
      return;
    }
    this.form.patchValue(val, { emitEvent: true });
  }

  registerOnChange( fn: any ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState?( isDisabled: boolean ): void {
    isDisabled ? this.form.disable({ emitEvent: false }) : this.form.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid ? null : { invalidForm: { valid: false } };
  }

  get nameControl(): FormControl {
    return this.form.get('name') as FormControl;
  }

  get jurisdictionControl(): FormControl {
    return this.form.get('jurisdiction') as FormControl;
  }

  private initForm(): FormGroup {
    return this.fb.group({
      name: [
        '', Validators.compose([
          Validators.required,
          Validators.maxLength(this.maxNumberInputLength),
        ])
      ],
      description: [''],
      jurisdiction: [UNREGULATED, Validators.required],
      expandEngagementBar: [false],
    });
  }
}
