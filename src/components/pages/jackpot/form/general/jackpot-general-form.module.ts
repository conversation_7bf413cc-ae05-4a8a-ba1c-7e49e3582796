import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiChipsAutocompleteModule, SwuiControlMessagesModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { TrimFormatterModule } from '../../../../../common/directives/trimFormatter/trimFormatter.module';

import { CurrencyService } from '../../../../../common/services/currency.service';
import { JackpotGeneralFormComponent } from './jackpot-general-form.component';

export const MODULES = [
  MatChipsModule,
  MatFormFieldModule,
  MatIconModule,
  MatInputModule,
  MatRadioModule,
  MatSelectModule,
  MatSlideToggleModule,
  MatCheckboxModule,
  ReactiveFormsModule,
  SwuiChipsAutocompleteModule,
];

@NgModule({
  imports: [
    CommonModule,
    MatProgressSpinnerModule,
    TranslateModule.forChild(),
    ...MODULES,
    SwuiControlMessagesModule,
    SwuiSelectModule,
    TrimFormatterModule,
  ],
  declarations: [
    JackpotGeneralFormComponent,
  ],
  exports: [
    JackpotGeneralFormComponent,
  ],
  providers: [
    CurrencyService
  ],
})
export class JackpotGeneralFormModule {
}
