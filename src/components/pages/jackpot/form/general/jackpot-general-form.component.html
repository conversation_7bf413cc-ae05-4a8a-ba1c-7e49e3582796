<form [formGroup]="form">
  <div class="mat-card mat-elevation-z0">
    <div class="vertical-form">
      <mat-form-field appearance="outline">
        <mat-label>{{'JACKPOT.FORM.GENERAL.NAME.title' | translate}}</mat-label>
        <input formControlName="name" matInput required swTrimFormatter>
        <mat-error>
          <lib-swui-control-messages [control]="nameControl"></lib-swui-control-messages>
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="margin-top12">
        <mat-label>{{'JACKPOT.FORM.GENERAL.DESCRIPTION.title' | translate}}</mat-label>
        <textarea
          formControlName="description"
          matInput
          matTextareaAutosize
          matAutosizeMinRows="1"
          matAutosizeMaxRows="5">
      </textarea>
      </mat-form-field>
    </div>
  </div>

  <div class="mat-card mat-elevation-z0 margin-top12">
    <h2>{{'JACKPOT.FORM.GENERAL.regulation' | translate}}</h2>
    <mat-form-field appearance="outline" style="display: block">
      <mat-label>{{ 'JACKPOT.FORM.GENERAL.jurisdiction' | translate }}</mat-label>
      <lib-swui-select
        required
        [formControl]="jurisdictionControl"
        [data]="jurisdictions"
        [showSearch]="true"
        [disableEmptyOption]="true">
      </lib-swui-select>
      <mat-error>
        <lib-swui-control-messages [control]="jurisdictionControl"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </div>

  <div class="mat-card mat-elevation-z0 margin-top12">
    <h2>{{'JACKPOT.FORM.GENERAL.options' | translate}}</h2>
    <div>
      <mat-checkbox formControlName="expandEngagementBar">
        <span>{{'JACKPOT.FORM.GENERAL.allowExpandEngagementBar' | translate}}</span>
      </mat-checkbox>
    </div>
  </div>
</form>
