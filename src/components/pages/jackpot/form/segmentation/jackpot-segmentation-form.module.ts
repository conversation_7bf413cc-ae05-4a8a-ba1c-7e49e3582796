import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiChipsAutocompleteModule } from '@skywind-group/lib-swui';

import { BrandsManagerModule } from '../../../../../common/components/brands-manager/brands-manager.module';
import { PlayersFilterModule } from '../../../../../common/components/players-filter/players-filter.module';
import { RestrictedCountriesModule } from '../../../../../common/components/restricted-countries/restricted-countries.module';
import { JackpotSegmentationFormComponent } from './jackpot-segmentation-form.component';


export const SEGMENTATION_MODULES = [
  MatCardModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...SEGMENTATION_MODULES,
    BrandsManagerModule,
    MatFormFieldModule,
    MatSelectModule,
    ReactiveFormsModule,
    SwuiChipsAutocompleteModule,
    PlayersFilterModule,
    RestrictedCountriesModule,
  ],
  exports: [
    JackpotSegmentationFormComponent,
  ],
  declarations: [
    JackpotSegmentationFormComponent,
  ],
})
export class JackpotSegmentationFormModule {
}
