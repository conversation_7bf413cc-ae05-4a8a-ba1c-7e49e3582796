import {
  ChangeDetectionStrategy, ChangeDetectorRef, Component, forwardRef, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit
} from '@angular/core';
import {
  ControlValueAccessor, FormBuilder, FormControl, FormGroup, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors,
  Validator, Validators
} from '@angular/forms';
import { SWUI_CONTROL_MESSAGES } from '@skywind-group/lib-swui';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { distinctUntilChanged, filter, takeUntil } from 'rxjs/operators';
import { playersLengthValidator } from '../../../../../common/lib/validators';
import { CurrencyModel } from '../../../../../common/models/currency.model';
import { FeatureSegmentation } from '../../../interfaces/feature';

import { JackpotFormService } from '../../jackpot-update/jackpot-form-service/jackpot-form.service';

@Component({
  selector: 'sw-jackpot-segmentation-form',
  templateUrl: './jackpot-segmentation-form.component.html',
  styleUrls: ['./jackpot-segmentation-form.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => JackpotSegmentationFormComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => JackpotSegmentationFormComponent),
      multi: true
    },
    {
      provide: SWUI_CONTROL_MESSAGES,
      useValue: {
        required: 'VALIDATION.currenciesRequired',
      }
    },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class JackpotSegmentationFormComponent implements OnDestroy, OnInit,
  ControlValueAccessor, Validator {

  @Input('currenciesList')
  set currenciesList( value: CurrencyModel[] | undefined ) {
    if (!value) {
      return;
    }

    this.processedCurrencies = value.map(( { code } ) => ({ id: code, text: code }));
  }

  @Input()
  set canPlayersEdit( val: boolean ) {
    this._canPlayersEdit.next(val);
  }

  @Input()
  set status( val: string | undefined ) {
    this._status.next(val);
  }

  importDescription = 'JACKPOT.FORM.SEGMENTATION.importDescription';

  form: FormGroup = new FormGroup({});
  processedCurrencies: { id: string, text: string }[] = [];
  onChange: ( _: any ) => void = (() => {
  });
  private _canPlayersEdit = new BehaviorSubject<boolean>(true);
  private _status = new BehaviorSubject<string | undefined>(undefined);
  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly formService: JackpotFormService,
               private readonly fb: FormBuilder,
               private cdr: ChangeDetectorRef,
  ) {
    this.initForm();
  }

  ngOnInit() {
    combineLatest([this._canPlayersEdit, this._status])
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe(( [canEdit, status] ) => {
        if (status === 'expired' || !canEdit) {
          this.brandsControl.disable();
          this.restrictedCountriesControl.disable();
        } else {
          this.brandsControl.enable();
          this.restrictedCountriesControl.enable();
        }
        this.cdr.detectChanges();
      });

    this.form.valueChanges
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe(() => {
        this.onChange(this.form.getRawValue());
        this.cdr.detectChanges();
      });

    this.formService.formSubmitted$
      .pipe(
        filter(val => !!val),
        takeUntil(this.destroyed$)
      )
      .subscribe(() => {
        this.cdr.detectChanges();
      });

    this.currenciesControl.valueChanges
      .pipe(
        distinctUntilChanged(( a, b ) => JSON.stringify(a) === JSON.stringify(b)),
        takeUntil(this.destroyed$)
      )
      .subscribe(( val: string[] ) => {
        this.formService.currencies$.next(val);
      });
  }

  get currenciesControl(): FormControl {
    return this.form.get('currencies') as FormControl;
  }

  get brandsControl(): FormControl {
    return this.form.get('brands') as FormControl;
  }

  get restrictedCountriesControl(): FormControl {
    return this.form.get('restrictedCountries') as FormControl;
  }

  initForm() {
    this.form = this.fb.group({
      currencies: [null, Validators.required],
      brands: [[], playersLengthValidator],
      restrictedCountries: []
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  writeValue( val: FeatureSegmentation ): void {
    if (!val) {
      return;
    }
    this.form.patchValue(val);
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState?( disabled: boolean ): void {
    disabled ? this.currenciesControl.disable() : this.currenciesControl.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid || this.form.status === 'DISABLED' ? null : { invalidForm: { valid: false } };
  }
}
