import {
  ChangeDetectionStrategy, ChangeDetectorRef, Component, forwardRef, HostBinding, HostListener, OnD<PERSON>roy, OnInit
} from '@angular/core';
import { ControlValueAccessor, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors } from '@angular/forms';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';

import { FeatureOperators } from '../../../interfaces/feature';
import { JackpotFormService } from '../../jackpot-update/jackpot-form-service/jackpot-form.service';

@Component({
  selector: 'sw-jackpot-operators-form',
  templateUrl: './jackpot-operators-form.component.html',
  styleUrls: ['./jackpot-operators-form.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => JackpotOperatorsFormComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => JackpotOperatorsFormComponent),
      multi: true
    },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class JackpotOperatorsFormComponent implements ControlValueAccessor, OnInit, OnDestroy {
  onChange: ( _: any ) => void = (() => {
  });
  disabled = false;
  isValid = true;
  operators?: FeatureOperators;
  @HostBinding('attr.tabindex') tabindex = 0;

  private readonly destroyed$ = new Subject<void>();
  private fundingValid$ = new BehaviorSubject<boolean>(true);
  private value$ = new BehaviorSubject<FeatureOperators | undefined>(undefined);

  constructor( private readonly formService: JackpotFormService,
               private cdr: ChangeDetectorRef, ) {
  }

  @HostListener('blur') onblur() {
    this.onTouched();
  }

  ngOnInit() {
    combineLatest([this.fundingValid$, this.value$]).pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( [valid, operators] ) => {
      this.operators = Object.assign({}, operators);
      this.isValid = valid && !!operators && !!operators.brands && !!operators.brands.length;
      this.onChange(operators);
      this.cdr.detectChanges();
    });

    this.formService.formSubmitted$
      .pipe(
        filter( val => !!val),
        takeUntil(this.destroyed$)
      )
      .subscribe( () => {
        this.cdr.detectChanges();
      });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  writeValue( val: FeatureOperators ): void {
    this.value$.next(val);
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState?( isDisabled: boolean ): void {
    this.disabled = isDisabled;
  }

  validate(): ValidationErrors | null {
    return this.isValid ? null : { invalidForm: { valid: false } };
  }

  onSelectData( featureOperators: FeatureOperators ) {
    this.value$.next(featureOperators);
  }

  onValidationCheck( val: boolean ) {
    this.fundingValid$.next(val);
  }

  get isErrorState(): boolean {
    if (!this.disabled && this.formService.formSubmitted) {
      return !this.isValid;
    }
    return false;
  }
}
