import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';

import { BrandsManagerModule } from '../../../../../common/components/brands-manager/brands-manager.module';
import { JackpotOperatorsFormComponent } from './jackpot-operators-form.component';


@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    BrandsManagerModule,
    MatCardModule,
  ],
  exports: [
    JackpotOperatorsFormComponent,
  ],
  declarations: [
    JackpotOperatorsFormComponent,
  ],
})
export class JackpotOperatorsFormModule {
}
