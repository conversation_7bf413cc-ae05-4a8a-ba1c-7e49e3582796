$color-error: #f44336;
$color-border: #d9dbdf;

.form-field {
  position: relative;
  padding-left: 47px;
  padding-right: 32px;

  &__input {
    margin-top: 12px;
  }

  &__prefix {
    position: absolute;
    left: 0;
    top: 26px;
    width: 47px;
    padding-right: 10px;
    padding-left: 5px;
    text-align: right;
  }

  &__suffix {
    position: absolute;
    top: 26px;
    right: 0;
    width: 32px;
    padding-left: 10px;
  }

  &__mat-field {
    width: 100%;
  }
}

.inline-field {
  display: flex;
  align-items: center;

  &__prefix {
    padding: 0 10px 6px;
  }

  &__input {
    width: 100px;
    position: relative;
    top: 3px;
  }
}

.divider {
  margin-top: 10px;
}

.options-table {
  display: inline-block;
  margin-top: 24px;
  &.full {
    @media (max-width: 1440px) {
      width: 100%;
    }
  }
  &__error {
    position: absolute;
    bottom: 10px;
    left: 0;
    width: 250px;
    font-size: 14px;
    color: $color-error;
  }
  &__header {
    position: relative;
  }
  &__row {
    display: flex;
    flex-direction: row;
    text-align: center;
  }
  &__cell {
    width: 150px;
    flex: 1;
    padding: 10px;
    text-align: center;
    &--value {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &.small {
      font-size: 14px;
    }
  }
  &__title {
    font-size: 16px;
    font-weight: 500;
    line-height: 1;
    white-space: nowrap;
    margin-bottom: 2px;
  }
  &__link {
    font-size: 14px;
    &:hover {
      cursor: pointer;
    }
  }
  &__option {
    padding: 16px 0;
    border-top: 1px solid $color-border;
    &:hover {
      transition: background-color 0.15s ease-in-out;
      background-color: $color-border;
    }
  }
  &__label {
    font-size: 15px;
    font-weight: 600;
    margin-top: 4px;
  }
}

.error {
  color: $color-error;
}

.payout-option-error {
  font-size: 14px;
  line-height: 1;
  color: #f44336;
}

.form-field {
  &__mat-field {
    width: 100%;
  }
  &__input {
    position: relative;
    padding-bottom: 12px;
  }
  &__error {
    position: absolute;
    bottom: 0;
    left: 0;
    font-size: 12px;
    line-height: 1;
    white-space: nowrap;
  }
}

.total-options {
  margin-top: 8px;
  font-size: 14px;
  line-height: 1;
}
