<form [formGroup]="form">
  <div class="mat-card mat-elevation-z0 margin-bottom20">
    <div fxLayout.lt-sm="column" fxLayout="row">
      <div fxFlex.lt-sm="100" fxFlex="170px" fxFlexAlign="center">
        {{ 'JACKPOT.FORM.CONFIGURATION.jackpotPools' | translate }}
      </div>
      <div class="margin-right24 margin-left32 margin-top4" fxFlexAlign="center" fxFlex="24px">
        <mat-icon
          matTooltip="{{ 'JACKPOT.FORM.CONFIGURATION.jackpotPools' | translate }}"
          class="help-icon"
          svgIcon="question_mark">
        </mat-icon>
      </div>
      <div class="form-field" fxFlex.lt-sm="150" fxFlex="360px">
        <div class="form-field__input">
          <mat-form-field class="form-field__mat-field no-field-padding" appearance="outline">
            <lib-swui-select
              [formControl]="poolIdControl"
              [data]="availablePools"
              [showSearch]="true"
              [disableEmptyOption]="true">
            </lib-swui-select>
          </mat-form-field>
          <mat-error class="form-field__error">
            <lib-swui-control-messages [control]="poolIdControl"></lib-swui-control-messages>
          </mat-error>
        </div>
      </div>
    </div>

    <div fxLayout.lt-sm="column" fxLayout="row">
      <div fxFlex.lt-sm="100" fxFlex="170px" fxFlexAlign="center">
        {{'TOURNAMENT.FORM.PAYOUT.baseCurrency' | translate}}
      </div>
      <div class="margin-right24 margin-left32 margin-top4" fxFlexAlign="center" fxFlex="24px">
        <mat-icon
          matTooltip="{{'TOURNAMENT.FORM.PAYOUT.baseCurrency' | translate}}"
          class="help-icon"
          svgIcon="question_mark">
        </mat-icon>
      </div>
      <div class="form-field" fxFlex.lt-sm="150" fxFlex="180px">
        <div class="form-field__input">
          <mat-form-field class="form-field__mat-field no-field-padding" appearance="outline">
            <lib-swui-select
              [formControl]="baseCurrencyControl"
              [showSearch]="true"
              [disableEmptyOption]="true"
              [data]="availableCurrencies">
            </lib-swui-select>
          </mat-form-field>
          <mat-error class="form-field__error">
            <lib-swui-control-messages [control]="baseCurrencyControl"></lib-swui-control-messages>
          </mat-error>
        </div>
      </div>
    </div>
    <div *ngIf="payouts && baseCurrency" class="total-options">
      {{payouts.length}} {{ payouts.length | i18nPlural: totalOptionsMessageMapping }}
    </div>
    <div *ngIf="payouts && baseCurrency && !payoutControl.value" class="payout-option-error">
      {{ 'VALIDATION.optionShouldBeSelected' | translate }}
    </div>
    <sw-jackpot-configuration-payout *ngIf="payouts && baseCurrency"
                                     (openSettings)="onOpenSettings($event)"
                                     [formControl]="payoutControl"
                                     [baseCurrency]="baseCurrencyControl.value"
                                     [payouts]="payouts"
                                     [status]="status"
                                     [settingsControl]="settingsControl"
                                     [isDuplicate]="isDuplicate">
    </sw-jackpot-configuration-payout>
  </div>

  <div class="mat-card mat-elevation-z0 margin-bottom20">
    <div fxLayout.lt-sm="column" fxLayout="row">
      <div fxFlex.lt-sm="100" fxFlex="170px" fxFlexAlign="center">
        {{ 'JACKPOT.FORM.CONFIGURATION.minQualifyingBet' | translate }}
      </div>
      <div class="margin-right24 margin-left32 margin-top4" fxFlexAlign="center" fxFlex="24px">
        <mat-icon
          matTooltip="{{ 'JACKPOT.FORM.CONFIGURATION.minQualifyingBet' | translate }}"
          class="help-icon"
          svgIcon="question_mark">
        </mat-icon>
      </div>
      <div class="form-field" fxFlex.lt-sm="150" fxFlex="200px">
        <div class="form-field__prefix">{{ baseCurrencyControl.value | currencySymbol }}</div>
        <div class="form-field__input">
          <mat-form-field class="form-field__mat-field no-field-padding" appearance="outline">
            <input type="number" min="0" placeholder="0" matInput [formControl]="minQualifyingBetControl"
                   swFloatFormatter>
          </mat-form-field>
          <mat-error class="form-field__error">
            <lib-swui-control-messages [control]="minQualifyingBetControl"></lib-swui-control-messages>
          </mat-error>
        </div>
      </div>
    </div>

    <div fxLayout.lt-sm="column" fxLayout="row">
      <div fxFlex.lt-sm="100" fxFlex="170px" fxFlexAlign="center">
        {{ 'JACKPOT.FORM.CONFIGURATION.maxEligibleStakeAmount' | translate }}
      </div>
      <div class="margin-right24 margin-left32 margin-top4" fxFlexAlign="center" fxFlex="24px">
        <mat-icon
          matTooltip="{{ 'JACKPOT.FORM.CONFIGURATION.maxEligibleStakeAmount' | translate }}"
          class="help-icon"
          svgIcon="question_mark">
        </mat-icon>
      </div>
      <div class="form-field" fxFlex.lt-sm="150" fxFlex="200px">
        <div class="form-field__prefix">{{ baseCurrencyControl.value | currencySymbol }}</div>
        <div class="form-field__input">
          <mat-form-field class="form-field__mat-field no-field-padding" appearance="outline">
            <input type="number" min="0" placeholder="0" matInput [formControl]="maxEligibleStakeAmountControl"
                   swFloatFormatter>
          </mat-form-field>
          <mat-error class="form-field__error">
            <lib-swui-control-messages [control]="maxEligibleStakeAmountControl"></lib-swui-control-messages>
          </mat-error>
        </div>
      </div>
    </div>
  </div>
</form>
