<mat-radio-group [formControl]="payoutControl">
  <div class="table-payout">
    <table class="table-payout__main">
      <thead>
      <tr>
        <th class="table-payout__th">
          <div class="table-payout__th-inner">
            Payout option
          </div>
        </th>
        <th class="table-payout__th">
          <div class="table-payout__th-inner">
            RTP
          </div>
        </th>
        <th class="table-payout__th">
          <div class="table-payout__th-inner">
            Daily Avg. Bets
          </div>
        </th>
        <th class="table-payout__th">
          <div class="table-payout__th-inner">
            Pools
          </div>
        </th>
        <th class="table-payout__th">
          <div class="table-payout__th-inner">
            Seed
          </div>
        </th>
        <th class="table-payout__th">
          <div class="table-payout__th-inner">
            Avg. win /<br> Drop amount
          </div>
        </th>
        <th class="table-payout__th">
          <div class="table-payout__th-inner">
            Daily Drops
          </div>
        </th>
        <th class="table-payout__th">
          <div class="table-payout__th-inner"></div>
        </th>
      </tr>
      </thead>
      <tbody class="table-payout__body">
      <tr *ngFor="let payout of payouts">
        <td class="table-payout__td table-payout__td--radio">
          <div class="table-payout__title">{{ payout.title }}</div>
          <div class="table-payout__radio">
            <mat-radio-button [value]="payout"
                              [checked]="(payout.id === payoutControl?.value?.id) || (payout.id === payoutControl?.value)"
                              [disabled]="!isDuplicate && (isDisabled || needDisableStatus)">
            </mat-radio-button>
          </div>
          <div class="table-payout__math">Math: {{ payout.mathId }}</div>
        </td>
        <td class="table-payout__td  table-payout__td--rtp">
          <table class="table-payout__sub">
            <tr>
              <td>{{ payout.rtp }}%</td>
            </tr>
          </table>
        </td>
        <td class="table-payout__td  table-payout__td--daily-avg-bets">
          <table class="table-payout__sub">
            <tr>
              <td>
                <ng-container *ngIf="payout.dailyAvgBets; else noDailyAvgBets">
                  {{ baseCurrency | currencySymbol }} {{ formatCurrency(payout.dailyAvgBets) }}
                </ng-container>
                <ng-template #noDailyAvgBets>--</ng-template>
              </td>
            </tr>
          </table>
        </td>
        <td class="table-payout__td table-payout__td--type">
          <table class="table-payout__sub">
            <tr *ngFor="let option of payout.option">
              <td>
                {{getJackpotName(option.subType)}}
              </td>
            </tr>
          </table>
        </td>
        <td class="table-payout__td table-payout__td--seed">
          <table class="table-payout__sub">
            <tr *ngFor="let option of payout.option">
              <td>
                {{baseCurrency | currencySymbol}}{{ formatCurrency(option.seed) }}
              </td>
            </tr>
          </table>
        </td>
        <td class="table-payout__td table-payout__td--avg">
          <table class="table-payout__sub">
            <tr *ngFor="let option of payout.option">
              <td>
                {{baseCurrency | currencySymbol}}{{ formatCurrency(option.avgWin) }}
              </td>
            </tr>
          </table>
        </td>
        <td class="table-payout__td  table-payout__td--drops">
          <table class="table-payout__sub">
            <tr *ngFor="let option of payout.option">
              <td>
                {{option.dailyDrops}}
              </td>
            </tr>
          </table>
        </td>
        <td class="table-payout__td table-payout__td--settings">
          <table class="table-payout__sub">
            <tr *ngFor="let option of payout.option; let index = index">
              <td>
                  <span
                    *ngIf="(payout.id === payoutControl?.value?.id) || (payout.id === payoutControl?.value)"
                    class="table-payout__link"
                    [ngClass]="{'invalid-option': isPayoutOptionInvalid(index)}"
                    (click)="onSettingsClick(index, option)">
                    Settings
                  </span>
              </td>
            </tr>
          </table>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</mat-radio-group>
