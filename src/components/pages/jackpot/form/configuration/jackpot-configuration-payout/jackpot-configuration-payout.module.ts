import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatRadioModule } from '@angular/material/radio';
import { SwuiCurrencySymbolModule } from '@skywind-group/lib-swui';

import { FormatFloatPipe } from '../../../../../../common/pipes/format-float.pipe';
import { AmountLimitSettingsComponent } from '../dialogs/amount-limit-settings/amount-limit-settings.component';
import { AmountLimitSettingsModule } from '../dialogs/amount-limit-settings/amount-limit-settings.module';
import { BaseSettingsComponent } from '../dialogs/base-settings/base-settings.component';
import { BaseSettingsModule } from '../dialogs/base-settings/base-settings.module';
import { DailySettingsComponent } from '../dialogs/daily-settings/daily-settings.component';
import { DailySettingsModule } from '../dialogs/daily-settings/daily-settings.module';
import { HourlySettingsComponent } from '../dialogs/hourly-settings/hourly-settings.component';
import { HourlySettingsModule } from '../dialogs/hourly-settings/hourly-settings.module';
import { NoLimitSettingsComponent } from '../dialogs/no-limit-settings/no-limit-settings.component';
import { NoLimitSettingsModule } from '../dialogs/no-limit-settings/no-limit-settings.module';
import { JackpotConfigurationPayoutComponent } from './jackpot-configuration-payout.component';


@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatRadioModule,
    SwuiCurrencySymbolModule,
    AmountLimitSettingsModule,
    BaseSettingsModule,
    DailySettingsModule,
    HourlySettingsModule,
    NoLimitSettingsModule,
  ],
  declarations: [
    JackpotConfigurationPayoutComponent,
  ],
  exports: [
    JackpotConfigurationPayoutComponent,
  ],
  entryComponents: [
    AmountLimitSettingsComponent,
    BaseSettingsComponent,
    DailySettingsComponent,
    HourlySettingsComponent,
    NoLimitSettingsComponent,
  ],
  providers: [
    FormatFloatPipe,
  ],
})
export class JackpotConfigurationPayoutModule {
}
