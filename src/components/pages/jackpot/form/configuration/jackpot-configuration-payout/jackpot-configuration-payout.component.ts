import {
  Component,
  EventEmitter,
  forwardRef,
  HostBinding,
  HostListener,
  Input,
  OnDestroy,
  OnInit,
  Output
} from '@angular/core';
import {
  ControlValueAccessor,
  FormArray,
  FormControl,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ValidationErrors,
  Validator,
} from '@angular/forms';
import { takeUntil } from 'rxjs/operators';

import { BaseComponent } from '../../../../../../common/components/base.component';
import { JACKPOT_NAMES, PayoutItem, PayoutItemOption } from '../../../../interfaces/jackpot';
import { JackpotFormService } from '../../../jackpot-update/jackpot-form-service/jackpot-form.service';


@Component({
  selector: 'sw-jackpot-configuration-payout',
  templateUrl: './jackpot-configuration-payout.component.html',
  styleUrls: ['./jackpot-configuration-payout.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => JackpotConfigurationPayoutComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => JackpotConfigurationPayoutComponent),
      multi: true
    }
  ]
})
export class JackpotConfigurationPayoutComponent extends BaseComponent
  implements ControlValueAccessor, Validator, OnInit, OnDestroy {

  @Output() openSettings = new EventEmitter<any>();

  @Input() baseCurrency?: string;
  @Input() status?: string;

  @Input() payouts?: PayoutItem[];

  @Input() settingsControl?: FormArray;

  @Input() isDuplicate = false;

  isDisabled?: boolean;
  payoutControl: FormControl = new FormControl('');
  onChange: ( _: any ) => void = (() => {
  });

  @HostBinding('attr.tabindex') tabindex = 0;

  constructor( private readonly formService: JackpotFormService ) {
    super();
  }

  @HostListener('blur') onblur() {
    this.onTouched();
  }

  get needDisableStatus(): boolean {
    return this.status === 'running' || this.status === 'pending';
  }

  ngOnInit() {
    this.payoutControl.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: PayoutItem ) => {
        this.onChange(val);
      });
  }

  writeValue( value: PayoutItem ) {
    this.payoutControl.setValue(value, { emitEvent: false });
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: any ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( disabled: boolean ) {
    this.isDisabled = disabled;
    disabled ? this.payoutControl.disable({ emitEvent: false }) : this.payoutControl.enable();
  }

  validate(): ValidationErrors | null {
    return this.payoutControl.valid ? null : { invalidForm: { valid: false } };
  }

  getJackpotName( type: string ) {
    const jp = JACKPOT_NAMES.find(( jackpot: { id: string, title: string } ) => jackpot.id === type);
    return jp ? jp.title : type;
  }

  onSettingsClick( index: number, option: PayoutItemOption ) {
    const subType = option && option.subType ? option.subType : '';
    const row = {
      index: index,
      title: this.getJackpotName(subType)
    };

    this.openSettings.emit(row);
  }

  isPayoutOptionInvalid( index: number ): boolean {
    return this.formService.isControlInvalid(this.settingsControl?.controls[index]);
  }

  formatCurrency( value: number ) {
    if (value) {
      return value?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  }
}
