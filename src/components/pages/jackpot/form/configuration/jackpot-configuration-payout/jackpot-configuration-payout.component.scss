$color-border: #d9dbdf;
:host {
  &:focus {
    outline: none !important;
  }
}

.table-payout {
  width: 100%;
  max-height: 544px;
  overflow: auto;
  border-bottom: 1px solid $color-border;

  &__main {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    text-align: center;
  }

  &__sub {
    border-collapse: collapse;
    width: 100%;

    td {
      height: 28px;
    }
  }

  &__td {
    padding: 8px;
    border-bottom: 1px solid $color-border;

    &--type {
      font-weight: 500;
    }

    &--daily-avg-bets,
    &--seed,
    &--avg {
      min-width: 80px;
    }

    &--rtp,
    &--drops {
      width: 60px;
    }

    &--settings {
      width: 100px;
      font-size: 13px;
    }

    &--radio {
      vertical-align: top;
    }
  }

  &__th {
    position: sticky;
    top: 0;
    z-index: 1;
    height: 44px;
    padding: 0;
  }

  &__th-inner {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 13px;
    font-weight: 400;
    line-height: 1.4em;
    text-align: center;
    border-bottom: 1px solid $color-border;
    background: #fff;
  }

  &__body {
    & > tr {
      &:last-child {
        & > td {
          border-bottom: none;
        }
      }

      &:hover {
        & > td {
          transition: background-color 0.15s ease-in-out;
          background-color: $color-border;
        }
      }
    }
  }

  &__link {
    font-weight: 500;
    color: #1468cf;
    cursor: pointer;
  }

  &__title {
    margin: 5px 0 10px 0;
    font-size: 13px;
    line-height: 1;
  }

  &__math {
    margin-top: 5px;
  }
}

.invalid-option {
  color: red;
}
