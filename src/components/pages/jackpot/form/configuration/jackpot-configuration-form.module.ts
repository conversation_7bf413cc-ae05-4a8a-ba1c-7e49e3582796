import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiCurrencySymbolModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { FloatFormatterModule } from '../../../../../common/directives/floatFormatter/floatFormatter.module';
import { FormatFloatPipe } from '../../../../../common/pipes/format-float.pipe';
import { JpnService } from '../../../../../common/services/jpn.service';
import { AmountLimitSettingsComponent } from './dialogs/amount-limit-settings/amount-limit-settings.component';
import { AmountLimitSettingsModule } from './dialogs/amount-limit-settings/amount-limit-settings.module';
import { BaseSettingsComponent } from './dialogs/base-settings/base-settings.component';
import { BaseSettingsModule } from './dialogs/base-settings/base-settings.module';
import { DailySettingsComponent } from './dialogs/daily-settings/daily-settings.component';
import { DailySettingsModule } from './dialogs/daily-settings/daily-settings.module';
import { HourlySettingsComponent } from './dialogs/hourly-settings/hourly-settings.component';
import { HourlySettingsModule } from './dialogs/hourly-settings/hourly-settings.module';
import { NoLimitSettingsComponent } from './dialogs/no-limit-settings/no-limit-settings.component';
import { NoLimitSettingsModule } from './dialogs/no-limit-settings/no-limit-settings.module';
import { SplitPrizeFormModule } from './dialogs/split-prize-form/split-prize-form.module';

import { JackpotConfigurationFormComponent } from './jackpot-configuration-form.component';
import { JackpotConfigurationPayoutModule } from './jackpot-configuration-payout/jackpot-configuration-payout.module';


@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    FlexModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatRadioModule,
    MatTooltipModule,
    SplitPrizeFormModule,
    FloatFormatterModule,
    SwuiSelectModule,
    SwuiCurrencySymbolModule,
    SwuiControlMessagesModule,
    JackpotConfigurationPayoutModule,

    AmountLimitSettingsModule,
    BaseSettingsModule,
    DailySettingsModule,
    HourlySettingsModule,
    NoLimitSettingsModule,
  ],
  declarations: [
    JackpotConfigurationFormComponent,
  ],
  exports: [
    JackpotConfigurationFormComponent,
  ],
  providers: [
    FormatFloatPipe,
    JpnService,
  ],
  entryComponents: [
    AmountLimitSettingsComponent,
    BaseSettingsComponent,
    DailySettingsComponent,
    HourlySettingsComponent,
    NoLimitSettingsComponent,
  ],
})
export class JackpotConfigurationFormModule {
}
