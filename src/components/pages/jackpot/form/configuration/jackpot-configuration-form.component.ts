import { ComponentType } from '@angular/cdk/portal';
import { Component, forwardRef, Input, OnDestroy, OnInit } from '@angular/core';
import {
  ControlValueAccessor,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ValidationErrors,
  Validator,
  Validators,
} from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { combineLatest, forkJoin, of } from 'rxjs';
import { filter, startWith, switchMap, takeUntil, tap } from 'rxjs/operators';

import { BaseComponent } from '../../../../../common/components/base.component';
import { CurrencyModel } from '../../../../../common/models/currency.model';
import { JackpotService } from '../../../../../common/services/jackpot.service';
import { JpnService } from '../../../../../common/services/jpn.service';
import {
  AmountSettings,
  BaseSettings,
  DailySettings,
  JackpotConfiguration,
  JackpotPollDataOption,
  PayoutItem,
  PayoutItemOption,
} from '../../../interfaces/jackpot';
import { JackpotFormService } from '../../jackpot-update/jackpot-form-service/jackpot-form.service';

import { AmountLimitSettingsComponent } from './dialogs/amount-limit-settings/amount-limit-settings.component';
import { DailySettingsComponent } from './dialogs/daily-settings/daily-settings.component';
import { HourlySettingsComponent } from './dialogs/hourly-settings/hourly-settings.component';
import { NoLimitSettingsComponent } from './dialogs/no-limit-settings/no-limit-settings.component';
import { initSplitPrizeForm } from './dialogs/split-prize-form/split-prize-form.component';

@Component({
  selector: 'sw-jackpot-configuration-form',
  templateUrl: './jackpot-configuration-form.component.html',
  styleUrls: ['./jackpot-configuration-form.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => JackpotConfigurationFormComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => JackpotConfigurationFormComponent),
      multi: true
    }
  ]
})
export class JackpotConfigurationFormComponent extends BaseComponent
  implements ControlValueAccessor, Validator, OnInit, OnDestroy {

  @Input() userType: string | undefined;
  @Input() status = '';
  @Input() isDuplicate = false;
  @Input() isOwner = false;
  @Input() jurisdiction?: string;

  configuration: any;
  isDisabled?: boolean;
  form: FormGroup;
  availablePools: SwuiSelectOption[] = [];
  currencies: CurrencyModel[] = [];
  availableCurrencies: SwuiSelectOption[] = [];
  onChange: ( _: any ) => void = (() => {
  });
  payouts: PayoutItem[] = [];
  totalOptionsMessageMapping: any = {
    '=1': 'payout option found',
    'other': 'payout options found'
  };

  rates: any[] | undefined;

  baseCurrency?: string;
  segmentationCurrencies: string[] = [];

  constructor( private readonly formService: JackpotFormService,
               private readonly jackpotService: JackpotService,
               private readonly fb: FormBuilder,
               { snapshot: { data: { configuration, currencies } } }: ActivatedRoute,
               private readonly dialog: MatDialog,
               private readonly jpnService: JpnService,
  ) {
    super();
    this.configuration = configuration;
    this.currencies = currencies;
    this.availablePools = this.poolsToSelectOption(this.configuration.pools);
    this.availableCurrencies = this.currenciesToSelectOption(this.currencies);
    this.form = this.initForm();
  }

  ngOnInit() {
    combineLatest([this.formService.currencies$, this.baseCurrencyControl.valueChanges])
      .pipe(
        switchMap(( [currencies, baseCurrency] ) => {
          if (currencies.length) {
            this.segmentationCurrencies = currencies;
            let rates = currencies.map(currency => this.jpnService.getExchangeRate(baseCurrency, currency));

            return forkJoin(rates);
          } else {
            return of([]);
          }
        }),
      )
      .subscribe(result => {
        this.rates = result;

        this.initDropAmountDisplayPerCurrency(this.rates);
      });

    this.poolIdControl.valueChanges
      .pipe(
        startWith(undefined),
        tap(( val: string ) => {
          if (this.poolIdControl.enabled) {
            this.payoutControl.patchValue(null);
          }
          this.formService.poolType$.next(val);
        }),
        filter(pool => !!pool),
        switchMap(( val: string ) => this.jackpotService.getOptions(val)),
        takeUntil(this.destroyed)
      )
      .subscribe(( val: { payouts: PayoutItem[] } ) => {
        this.payouts = val.payouts;
      });

    this.payoutControl.valueChanges
      .pipe(
        filter(( val: PayoutItem ) => !!val && !!val.option),
        takeUntil(this.destroyed)
      )
      .subscribe(( val: PayoutItem ) => {
        this.formService.payout$.next({
          payout: val,
          baseCurrency: this.formService.payout$.value.baseCurrency
        });
        this.settingsControl.clear();
        val.option.forEach(( el: PayoutItemOption ) => this.settingsControl.push(this.initSettingsItem(el.type)));

        let dailyDrops: any[] = [];
        if (Array.isArray(val.option) && val.option.length) {
          dailyDrops = val.option.map(item => {
            return { dropAmountDisplay: item.avgWin };
          });
        }

        this.settingsControl.patchValue(dailyDrops, { emitEvent: false });

        this.initDropAmountDisplayPerCurrency(this.rates);
      });

    this.baseCurrencyControl.valueChanges.pipe(
      takeUntil(this.destroyed)
    ).subscribe(baseCurrency => {
      this.baseCurrency = baseCurrency;
      this.formService.payout$.next({
        payout: this.formService.payout$.value.payout,
        baseCurrency
      });
    });

    this.form.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(() => {
        const val = this.form.getRawValue();
        this.onChange(val);
      });
  }

  writeValue( value: JackpotConfiguration ) {
    if (!value) {
      return;
    }

    this.form.patchValue(value);
    this.patchSettings(value.settings);
  }

  initDropAmountDisplayPerCurrency( rates: any[] | undefined ) {
    if (Array.isArray(rates) && rates.length && this.settingsControl.controls && this.settingsControl.controls.length) {
      this.settingsControl.controls.forEach(item => {
        if (item.value && item.value.type === 'amount') {
          let dropAmountDisplayPerCurrencyArray = item.get('dropAmountDisplayPerCurrency') as FormArray;
          let values = dropAmountDisplayPerCurrencyArray.value;
          dropAmountDisplayPerCurrencyArray?.clear();

          rates.forEach(rate => {
            if (rate.currency !== this.baseCurrency) {
              dropAmountDisplayPerCurrencyArray?.push(
                this.initDropAmountDisplayPerCurrencyItem(rate.currency, values.find(( dropAmountItem: any ) =>
                  dropAmountItem.currency === rate.currency)?.value)
              );
            }
          });
        }
      });
    }
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: any ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( disabled: boolean ) {
    this.isDisabled = disabled;

    if (disabled) {
      this.form.disable({ emitEvent: false });
    } else {
      this.form.enable();
    }
  }

  validate(): ValidationErrors | null {
    return this.form.valid ? null : { invalidForm: { valid: false } };
  }

  onOpenSettings( selectedSetting: any ) {
    const dialogConfig = new MatDialogConfig();
    const { index } = selectedSetting;
    const { title } = selectedSetting;

    dialogConfig.disableClose = true;
    dialogConfig.width = '750px';
    dialogConfig.panelClass = 'import-dialog';

    const settingsItem = this.settingsControl.controls[index] as FormGroup;

    dialogConfig.data = {
      settingsItem: settingsItem,
      baseCurrency: this.baseCurrencyControl.value,
      currencies: this.segmentationCurrencies,
      isDisabled: this.isDisabled,
      status: this.status,
      rates: this.rates,
      title: title,
      isOwner: this.isOwner,
      jurisdiction: this.jurisdiction
    };

    let componentType: ComponentType<any> | undefined;

    switch (settingsItem.getRawValue().type) {
      case 'amount':
        componentType = AmountLimitSettingsComponent;
        break;
      case 'grand':
        componentType = NoLimitSettingsComponent;
        break;
      case 'daily':
        componentType = DailySettingsComponent;
        break;
      case 'hourly':
        componentType = HourlySettingsComponent;
        break;
    }

    if (componentType) {
      this.dialog.open(componentType, dialogConfig);
    }
  }

  get poolIdControl(): FormControl {
    return this.form.get('poolId') as FormControl;
  }

  get baseCurrencyControl(): FormControl {
    return this.form.get('baseCurrency') as FormControl;
  }

  get minQualifyingBetControl(): FormControl {
    return this.form.get('minQualifyingBet') as FormControl;
  }

  get maxEligibleStakeAmountControl(): FormControl {
    return this.form.get('maxEligibleStakeAmount') as FormControl;
  }

  get payoutControl(): FormControl {
    return this.form.get('payout') as FormControl;
  }

  get settingsControl(): FormArray {
    return this.form.get('settings') as FormArray;
  }

  private initForm(): FormGroup {
    return this.fb.group({
      poolId: ['', Validators.required],
      baseCurrency: ['', Validators.required],
      minQualifyingBet: [
        '', [
          Validators.min(0),
          Validators.max(999999),
        ]
      ],
      maxEligibleStakeAmount: [
        '', [
          Validators.min(0),
          Validators.max(999999),
        ]
      ],
      payout: ['', Validators.required],
      settings: this.fb.array([])
    });
  }

  private initDropAmountDisplayPerCurrencyItem( currency: string, value?: number ): FormGroup {
    return this.fb.group({
      currency: [currency],
      value: [value ? value : null, Validators.min(0)],
    });
  }

  private poolsToSelectOption( pools: JackpotPollDataOption[] ): SwuiSelectOption[] {
    return pools ? pools.map(( el: JackpotPollDataOption ) => {
      return {
        id: el.id || '',
        text: el.title || ''
      };
    }) : [];
  }

  private currenciesToSelectOption( currencies: CurrencyModel[] ): SwuiSelectOption[] {
    return currencies ? currencies.map(( el: CurrencyModel ) => {
      return {
        id: el.code || '',
        text: el.code || ''
      };
    }) : [];
  }

  private initSettingsItem( type: string, item?: BaseSettings | AmountSettings | DailySettings ): FormGroup {
    let itemForm = this.fb.group({
      type: [''],
      winNotification: [{ type: 'animation', amount: 0 }]
    });

    switch (type) {
      case 'amount': {
        itemForm.addControl('dropAmountDisplay', this.fb.control(''));
        itemForm.addControl('dropAmountDisplayPerCurrency', this.fb.array([]));
        itemForm.patchValue({ type: 'amount' }, { emitEvent: false });
        break;
      }
      case 'daily': {
        itemForm.addControl('dropTime', this.fb.control(0, Validators.required));
        itemForm.addControl('timezone', this.fb.control('', Validators.required));
        itemForm.patchValue({ type: 'daily' }, { emitEvent: false });
        break;
      }
      case 'grand': {
        itemForm.patchValue({ type: 'grand' }, { emitEvent: false });
        break;
      }
      case 'hourly': {
        itemForm.patchValue({ type: 'hourly' }, { emitEvent: false });
        break;
      }
    }

    if (item) {
      if (item.splitPrize) {
        itemForm.addControl('splitPrize', initSplitPrizeForm());
      }

      if (item.type === 'amount' && Array.isArray((item as AmountSettings).dropAmountDisplayPerCurrency) &&
        (item as AmountSettings).dropAmountDisplayPerCurrency.length) {
        (itemForm.get('dropAmountDisplayPerCurrency') as FormArray).clear();

        (item as AmountSettings).dropAmountDisplayPerCurrency.forEach(obj => {
          if (obj.currency !== this.baseCurrency) {
            (itemForm.get('dropAmountDisplayPerCurrency') as FormArray).push(
              this.initDropAmountDisplayPerCurrencyItem(obj.currency)
            );
          }
        });
      }

      itemForm.patchValue(item, { emitEvent: false });
    }

    return itemForm;
  }

  private patchSettings( value: BaseSettings[] ) {
    this.settingsControl.clear();

    value.forEach(item => {
      if (item && item.type) {
        this.settingsControl.push(this.initSettingsItem(item.type, item));
      }
    });
  }
}
