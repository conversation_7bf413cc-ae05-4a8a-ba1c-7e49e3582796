import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { BaseSettingsModule } from '../base-settings/base-settings.module';
import { NoLimitSettingsComponent } from './no-limit-settings.component';
import { CommonModule } from '@angular/common';

@NgModule({
    imports: [
        MatIconModule,
        MatButtonModule,
        MatDialogModule,
        TranslateModule,
        BaseSettingsModule,
        CommonModule
    ],
  declarations: [
    NoLimitSettingsComponent,
  ],
  exports: [
    NoLimitSettingsComponent,
  ],
})
export class NoLimitSettingsModule {
}
