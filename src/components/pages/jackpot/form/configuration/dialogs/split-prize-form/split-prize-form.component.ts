import { Component, Input } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import {
  digitsOnlyValidator,
  fractionsNumbersLengthValidator,
  positiveNumbers
} from '../../../../../../../common/lib/validators';

export function initSplitPrizeForm(): FormGroup {
  return new FormGroup({
    potDistribution: new FormGroup({
      main: new FormControl('', Validators.compose([
        Validators.required,
        Validators.min(0),
        Validators.max(100),
        fractionsNumbersLengthValidator,
        positiveNumbers
      ])),
      sharedEven: new FormControl('', Validators.compose([
        Validators.required,
        Validators.min(0),
        Validators.max(100),
        fractionsNumbersLengthValidator,
        positiveNumbers
      ])),
      sharedRelative: new FormControl('', Validators.compose([
        Validators.required,
        Validators.min(0),
        Validators.max(100),
        fractionsNumbersLengthValidator
      ])),
    }, ( control: AbstractControl ): ValidationErrors | null => {
      if (control instanceof FormGroup) {
        const formGroup = control as FormGroup;
        const { main, sharedEven, sharedRelative } = formGroup.value;
        const actual = (main || 0) + (sharedEven || 0) + (sharedRelative || 0);
        if (actual !== 100) {
          return { max: { max: 100, actual } };
        }
      }
      return null;
    }),
    eligibility: new FormGroup({
      period: new FormControl('', [
        Validators.required,
        Validators.min(1),
        Validators.max(999),
        digitsOnlyValidator,
      ]),
      betsThreshold: new FormControl('', [
        Validators.required,
        Validators.min(0.01),
        positiveNumbers,
      ]),
    }),
    showProgressBar: new FormControl(false),
  });
}

@Component({
  selector: 'sw-split-prize-form',
  templateUrl: './split-prize-form.component.html',
  styleUrls: ['../../jackpot-configuration-form.component.scss'],
})
export class SplitPrizeFormComponent {
  @Input() form?: FormGroup;
  @Input() baseCurrency?: string;
  @Input() isDisabled?: boolean;

  get potDistributionValue(): string {
    if (this.potDistribution) {
      const { main, sharedEven, sharedRelative } = this.potDistribution.value;
      const value = 100 - main - sharedEven - sharedRelative;

      if (value % 1) {
        return value.toFixed(2);
      }

      return `${value}`;
    }
    return '100';
  }

  get mainControl(): FormControl | undefined {
    if (this.potDistribution) {
      return this.potDistribution.get('main') as FormControl;
    }
  }

  get sharedEvenControl(): FormControl | undefined {
    if (this.potDistribution) {
      return this.potDistribution.get('sharedEven') as FormControl;
    }
  }

  get sharedRelativeControl(): FormControl | undefined {
    if (this.potDistribution) {
      return this.potDistribution.get('sharedRelative') as FormControl;
    }
  }

  get periodControl(): FormControl | undefined {
    if (this.form) {
      return this.form.get('eligibility.period') as FormControl;
    }
  }

  get betsThresholdControl(): FormControl | undefined {
    if (this.form) {
      return this.form.get('eligibility.betsThreshold') as FormControl;
    }
  }

  get potDistribution(): FormGroup | undefined {
    if (this.form) {
      return this.form.get('potDistribution') as FormGroup;
    }
  }
}
