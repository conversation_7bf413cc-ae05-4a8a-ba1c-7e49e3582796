<form [formGroup]="form" style="padding-left: 40px;">

  <div formGroupName="potDistribution">
    <div fxLayout.lt-sm="column" fxLayout="row">
      <div fxFlex.lt-sm="100" fxFlex="160px" fxFlexAlign="center">
        {{ 'JACKPOT.FORM.CONFIGURATION.main' | translate }} *
      </div>
      <div class="margin-right24 margin-left32 margin-top4" fxFlexAlign="center" fxFlex="24px">
        <mat-icon
          class="help-icon"
          svgIcon="question_mark"
          matTooltip="{{ 'JACKPOT.TOOLTIP.main' | translate }}">
        </mat-icon>
      </div>
      <div class="form-field" fxFlex.lt-sm="100" fxFlex="200px">
        <div class="form-field__input">
          <mat-form-field class="form-field__mat-field" appearance="outline">
            <input type="number" min="0.01" max="100" placeholder="0" matInput formControlName="main" swFloatFormatter>
            <mat-hint align="end">{{ potDistributionValue }}/100</mat-hint>
            <mat-error>
              <lib-swui-control-messages [control]="mainControl"></lib-swui-control-messages>
            </mat-error>
          </mat-form-field>
        </div>
        <mat-icon [inline]="true" class="form-field__suffix">%</mat-icon>
      </div>
    </div>

    <div fxLayout.lt-sm="column" fxLayout="row">
      <div fxFlex.lt-sm="100" fxFlex="160px" fxFlexAlign="center">
        {{ 'JACKPOT.FORM.CONFIGURATION.sharedEven' | translate }} *
      </div>
      <div class="margin-right24 margin-left32 margin-top4" fxFlexAlign="center" fxFlex="24px">
        <mat-icon
          class="help-icon"
          svgIcon="question_mark"
          matTooltip="{{ 'JACKPOT.TOOLTIP.sharedEven' | translate }}">
        </mat-icon>
      </div>
      <div class="form-field" fxFlex.lt-sm="100" fxFlex="200px">
        <div class="form-field__input">
          <mat-form-field class="form-field__mat-field" appearance="outline">
            <input type="number" min="0" max="100" placeholder="0" matInput formControlName="sharedEven"
                   swFloatFormatter>
            <mat-hint align="end">{{ potDistributionValue }}/100</mat-hint>
            <mat-error>
              <lib-swui-control-messages [control]="sharedEvenControl"></lib-swui-control-messages>
            </mat-error>
          </mat-form-field>
        </div>
        <mat-icon [inline]="true" class="form-field__suffix">%</mat-icon>
      </div>
    </div>

    <div fxLayout.lt-sm="column" fxLayout="row">
      <div fxFlex.lt-sm="100" fxFlex="160px" fxFlexAlign="center">
        {{ 'JACKPOT.FORM.CONFIGURATION.sharedRelative' | translate }} *
      </div>
      <div class="margin-right24 margin-left32 margin-top4" fxFlexAlign="center" fxFlex="24px">
        <mat-icon
          class="help-icon"
          svgIcon="question_mark"
          matTooltip="{{ 'JACKPOT.TOOLTIP.sharedRelative' | translate }}">
        </mat-icon>
      </div>
      <div class="form-field" fxFlex.lt-sm="100" fxFlex="200px">
        <div class="form-field__input">
          <mat-form-field class="form-field__mat-field" appearance="outline">
            <input type="number" min="0" max="100" placeholder="0" matInput formControlName="sharedRelative"
                   swFloatFormatter>
            <mat-hint align="end">{{ potDistributionValue }}/100</mat-hint>
            <mat-error>
              <lib-swui-control-messages [control]="sharedRelativeControl"></lib-swui-control-messages>
            </mat-error>
          </mat-form-field>
        </div>
        <mat-icon [inline]="true" class="form-field__suffix">%</mat-icon>
      </div>
    </div>
  </div>

  <div [formGroup]="form?.get('eligibility')">
    <div fxLayout.lt-sm="column" fxLayout="row">
      <div fxFlex.lt-sm="100" fxFlex="160px" fxFlexAlign="center">
        {{ 'JACKPOT.FORM.CONFIGURATION.period' | translate }} *
      </div>
      <div class="margin-right24 margin-left32 margin-top4" fxFlexAlign="center" fxFlex="24px">
        <mat-icon
          class="help-icon"
          svgIcon="question_mark"
          matTooltip="{{ 'JACKPOT.TOOLTIP.period' | translate }}">
        </mat-icon>
      </div>
      <div class="form-field" fxFlex.lt-sm="100" fxFlex="200px">
        <div class="form-field__input">
          <mat-form-field class="form-field__mat-field" appearance="outline">
            <input type="number" min="0" placeholder="0" matInput formControlName="period">
            <mat-error>
              <lib-swui-control-messages [control]="periodControl"></lib-swui-control-messages>
            </mat-error>
          </mat-form-field>
        </div>
        <div fxFlexAlign="center" class="form-field__suffix">
          {{ 'JACKPOT.FORM.CONFIGURATION.hours' | translate }}
        </div>
      </div>
    </div>

    <div fxLayout.lt-sm="column" fxLayout="row">
      <div fxFlex.lt-sm="100" fxFlex="160px" fxFlexAlign="center">
        {{ 'JACKPOT.FORM.CONFIGURATION.betsThreshold' | translate }} *
      </div>
      <div class="margin-right24 margin-left32 margin-top4" fxFlexAlign="center" fxFlex="24px">
        <mat-icon
          class="help-icon"
          svgIcon="question_mark"
          matTooltip="{{ 'JACKPOT.TOOLTIP.betsThreshold' | translate }}">
        </mat-icon>
      </div>
      <div class="form-field" fxFlex.lt-sm="100" fxFlex="200px">
        <div class="form-field__prefix">{{ baseCurrency | currencySymbol }}</div>
        <div class="form-field__input">
          <mat-form-field class="form-field__mat-field" appearance="outline">
            <input type="number" min="0" placeholder="0" matInput formControlName="betsThreshold" swFloatFormatter>
            <mat-error>
              <lib-swui-control-messages [control]="betsThresholdControl"></lib-swui-control-messages>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>
  </div>

  <div fxLayout.lt-sm="column" fxLayout="row">
    <mat-checkbox formControlName="showProgressBar" [disabled]="isDisabled">
      {{ 'JACKPOT.FORM.CONFIGURATION.showSplitPrizeProgressBarLabel' | translate }}
    </mat-checkbox>
  </div>
</form>
