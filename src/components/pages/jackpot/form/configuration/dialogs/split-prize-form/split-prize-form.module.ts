import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiCurrencySymbolModule } from '@skywind-group/lib-swui';
import { FloatFormatterModule } from '../../../../../../../common/directives/floatFormatter/floatFormatter.module';
import { FormatFloatPipe } from '../../../../../../../common/pipes/format-float.pipe';
import { SplitPrizeFormComponent } from './split-prize-form.component';

@NgModule({
  declarations: [
    SplitPrizeFormComponent,
  ],
  imports: [
    ReactiveFormsModule,
    TranslateModule,
    MatIconModule,
    FlexModule,
    MatFormFieldModule,
    MatInputModule,
    MatDividerModule,
    MatCheckboxModule,
    MatTooltipModule,
    SwuiCurrencySymbolModule,
    FloatFormatterModule,
    CommonModule,
    SwuiControlMessagesModule,
  ],
  exports: [
    SplitPrizeFormComponent,
  ],
  providers: [
    FormatFloatPipe,
  ],
})
export class SplitPrizeFormModule {
}
