import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiCurrencySymbolModule } from '@skywind-group/lib-swui';
import { FloatFormatterModule } from '../../../../../../../common/directives/floatFormatter/floatFormatter.module';
import { JpnService } from '../../../../../../../common/services/jpn.service';
import { BaseSettingsModule } from '../base-settings/base-settings.module';
import { SplitPrizeFormModule } from '../split-prize-form/split-prize-form.module';
import { AmountLimitSettingsComponent } from './amount-limit-settings.component';

@NgModule({
  imports: [
    MatButtonModule,
    MatDialogModule,
    MatIconModule,
    TranslateModule,
    SplitPrizeFormModule,
    BaseSettingsModule,
    SwuiCurrencySymbolModule,
    FlexModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    FloatFormatterModule,
    SwuiControlMessagesModule,
    MatTableModule,
    CommonModule,
  ],
  declarations: [
    AmountLimitSettingsComponent,
  ],
  exports: [
    AmountLimitSettingsComponent,
  ],
  providers: [
    JpnService,
  ],
})
export class AmountLimitSettingsModule {
}
