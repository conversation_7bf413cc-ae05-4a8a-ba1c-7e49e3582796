import { Component, Inject, OnInit } from '@angular/core';
import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { READONLY_JURISDICTION_ID } from '../../../../../interfaces/jackpot';
import { initSplitPrizeForm } from '../split-prize-form/split-prize-form.component';

@Component({
  selector: 'sw-amount-limit-settings',
  templateUrl: './amount-limit-settings.component.html',
  styleUrls: ['./amount-limit-settings.component.scss', '../../jackpot-configuration-form.component.scss'],
})
export class AmountLimitSettingsComponent implements OnInit {
  form: FormGroup | undefined;
  baseCurrency?: string;
  title = 'Amount Jackpot settings';
  currencies: string[] = [];
  isDisabled?: boolean;
  dropAmountDisplay: any;

  initialFormValue: any;

  rates: any[] | undefined;

  private isOwner = false;

  constructor( @Inject(MAT_DIALOG_DATA) public data: any,
               public dialogRef: MatDialogRef<any>,
  ) {
    if (this.data) {
      this.title = this.data.title;
      this.form = this.data.settingsItem;
      this.baseCurrency = this.data.baseCurrency;
      this.currencies = this.data.currencies;
      this.isOwner = this.data.isOwner;

      this.isDisabled = this.isOwner
        ? this.data.isDisabled && !this.needEnableStatus
        : this.data.isDisabled;

      if (this.data.settingsItem) {
        this.initialFormValue = this.data.settingsItem.getRawValue();
      }

      this.rates = this.data.rates;
      this.dropAmountDisplay = this.dropAmountDisplayControl?.value;

      if (this.isDisabled) {
        this.form?.disable({ emitEvent: false });
      }
    }
  }

  get needEnableStatus(): boolean {
    return this.data && ((this.data.status === 'running' && this.data.jurisdiction !== READONLY_JURISDICTION_ID)
      || this.data.status === 'pending');
  }

  ngOnInit() {
    if (this.needEnableStatus && this.isOwner) {
      this.form?.enable();
    }
  }

  getCalculatedCurrencyValue( currency: string ): number {
    return this.rates?.find(item => item.currency === currency).rate * this.dropAmountDisplay;
  }

  isFormValid(): boolean {
    return this.form ? this.form.valid : false;
  }

  get dropAmountDisplayControl(): FormControl | undefined {
    if (this.form) {
      return this.form.get('dropAmountDisplay') as FormControl;
    }
  }

  get dropAmountDisplayPerCurrencyArray(): FormArray | undefined {
    if (this.form) {
      return this.form.get('dropAmountDisplayPerCurrency') as FormArray;
    }
  }

  close() {
    if (this.form) {
      if (this.initialFormValue && this.initialFormValue.splitPrize) {
        this.form.addControl('splitPrize', initSplitPrizeForm());
      } else {
        this.form.removeControl('splitPrize');
      }

      const winNotificationControl = this.form.get('winNotification');

      if (this.initialFormValue && this.initialFormValue.winNotification && winNotificationControl) {
        let initialWinNotification = this.initialFormValue.winNotification;
        winNotificationControl.reset(initialWinNotification);
      }

      this.form.patchValue(this.initialFormValue, { emitEvent: true });
    }
  }

  formatCurrency( value: number ) {
    if (value) {
      return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  }

  isBaseCurrencyInCurrencies() {
    return this.baseCurrency && this.currencies?.length && this.currencies.includes(this.baseCurrency);
  }
}
