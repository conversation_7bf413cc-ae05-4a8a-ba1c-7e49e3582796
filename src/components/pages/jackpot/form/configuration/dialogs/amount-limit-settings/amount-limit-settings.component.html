<button class="mat-dialog-close" mat-button mat-dialog-close (click)="close()" tabindex="-1">
  <mat-icon svgIcon="clear" style="color: grey;"></mat-icon>
</button>
<h2 mat-dialog-title style="padding-left: 20px;">
  <span>{{ title }} settings</span>
</h2>
<div *ngIf="rates?.length || isBaseCurrencyInCurrencies()" style="padding-left: 20px;">
  {{ 'JACKPOT.FORM.CONFIGURATION.amountSettingsDescription' | translate }}
</div>

<mat-dialog-content style="max-height: 70vh !important;">
  <form [formGroup]="form">

    <div *ngIf="rates?.length || isBaseCurrencyInCurrencies()" class="table">
      <div class="row header">
        <div class="column">{{ 'JACKPOT.FORM.CONFIGURATION.currency' | translate }}</div>
        <div class="column" style="padding-left: 0 !important; padding-right: 50px;">
          {{ 'JACKPOT.FORM.CONFIGURATION.actualDropAmount' | translate }}
        </div>
        <div class="column" style="text-align: center">{{ 'JACKPOT.FORM.CONFIGURATION.dropAmountToShow' | translate }}</div>
      </div>
      <div *ngIf="isBaseCurrencyInCurrencies()" class="row first">
        <div class="column bold" style="padding-left: 30px;">{{ baseCurrency }}</div>
        <div class="column bold">
          {{ (baseCurrency | currencySymbol) + ' ' + (formatCurrency(dropAmountDisplayControl?.value) || '') }}
        </div>
        <div class="column" style="padding-right: 17px;">
          {{ (baseCurrency | currencySymbol) + ' ' + (formatCurrency(dropAmountDisplayControl?.value) || '') }}
        </div>
      </div>

      <div *ngIf="rates?.length">
        <div class="row" *ngFor="let item of dropAmountDisplayPerCurrencyArray?.controls">
          <div class="column bold" style="padding-top: 13px; padding-left: 30px;">{{ item?.value?.currency }}</div>
          <div class="column bold" style="padding-top: 13px;">{{ (item?.value?.currency | currencySymbol) + ' ' +
          (formatCurrency(getCalculatedCurrencyValue(item?.value?.currency)) || '') }}
          </div>
          <div class="column">
            <div class="inline-field">
              <div class="inline-field__prefix">
                {{ item?.value?.currency | currencySymbol }}
              </div>

              <div class="inline-field__input">
                <mat-form-field style="width: 150px;" appearance="outline">
                  <input type="number"
                         min="0"
                         placeholder="{{ (formatCurrency(getCalculatedCurrencyValue(item?.value?.currency)) || '0') }}"
                         matInput
                         swFloatFormatter
                         [formControl]="item?.get('value')">
                  <mat-error>
                    <lib-swui-control-messages [control]="item?.get('value')"></lib-swui-control-messages>
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <sw-base-settings [form]="form" [baseCurrency]="baseCurrency" [isDisabled]="isDisabled"></sw-base-settings>
  </form>
</mat-dialog-content>

<mat-dialog-actions class="actions">
  <button mat-dialog-close
          mat-flat-button
          class="mat-button-md link"
          (click)="close()">
    {{ (form?.enabled ? 'DIALOG.cancel' : 'DIALOG.ok') | translate }}
  </button>
  <button *ngIf="form?.enabled"
          mat-flat-button
          color="primary"
          class="mat-button-md"
          style="margin-left: 20px;"
          mat-dialog-close="{{ form?.value }}"
          [disabled]="!isFormValid()">
    {{ 'DIALOG.ok' | translate }}
  </button>
</mat-dialog-actions>
