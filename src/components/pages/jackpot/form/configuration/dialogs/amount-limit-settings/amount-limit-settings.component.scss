$color-border: #d9dbdf;

.actions {
  margin: 30px -24px -24px;
  padding: 20px;
  justify-content: center;
  background-color: #eef1f5;
}

.table {
  display: block;
  padding-left: 12px;
  padding-top: 10px;
}

.row {
  display: flex;
  flex-direction: row;
}

.header {
  border-bottom: 1px solid $color-border;
  font-weight: bold;
  padding: 10px 10px 10px 0;

  .column {
    padding-left: 10px !important;
  }
}

.first {
  .column {
    padding: 20px 0 25px 30px;
  }
}

.column {
  flex: 1;
}

.bold {
  font-weight: bold;
}

.inline-field {
  display: flex;
  align-items: center;
  position: relative;

  &__prefix {
    position: absolute;
    left: 0;
    text-align: right;
    width: 50px;
    padding: 0 10px 6px 0 !important;
  }

  &__input {
    position: relative;
    top: 3px;
    padding-left: 50px;
    width: auto !important;
  }
}

.button-white-color{
  background-color: #d9dbdf;
}
