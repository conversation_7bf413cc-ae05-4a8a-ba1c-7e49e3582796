<button class="mat-dialog-close" mat-button mat-dialog-close (click)="close()" tabindex="-1">
  <mat-icon svgIcon="clear" style="color: grey;"></mat-icon>
</button>
<h2 mat-dialog-title style="padding-left: 20px;">
  {{ 'JACKPOT.FORM.CONFIGURATION.hourlyJackpotSettings' | translate }}
</h2>

<mat-dialog-content style="max-height: 70vh !important;">
  <sw-base-settings [form]="form" [baseCurrency]="baseCurrency" [isDisabled]="isDisabled"></sw-base-settings>
</mat-dialog-content>

<mat-dialog-actions class="actions">
  <button mat-dialog-close
          mat-flat-button
          class="mat-button-md link"
          (click)="close()">
    {{ (form?.enabled ? 'DIALOG.cancel' : 'DIALOG.ok') | translate }}
  </button>
  <button *ngIf="form?.enabled"
          mat-flat-button
          color="primary"
          class="mat-button-md"
          style="margin-left: 20px;"
          mat-dialog-close="{{ form?.value }}"
          [disabled]="!isFormValid()">
    {{ 'DIALOG.ok' | translate }}
  </button>
</mat-dialog-actions>
