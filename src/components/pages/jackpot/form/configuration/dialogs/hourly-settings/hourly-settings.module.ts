import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { BaseSettingsModule } from '../base-settings/base-settings.module';
import { HourlySettingsComponent } from './hourly-settings.component';
import { CommonModule } from '@angular/common';

@NgModule({
    imports: [
        MatButtonModule,
        MatDialogModule,
        MatIconModule,
        TranslateModule,
        BaseSettingsModule,
        CommonModule,
    ],
  declarations: [
    HourlySettingsComponent,
  ],
  exports: [
    HourlySettingsComponent,
  ],
})
export class HourlySettingsModule {
}
