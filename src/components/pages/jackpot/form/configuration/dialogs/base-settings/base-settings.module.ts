import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SplitPrizeFormModule } from '../split-prize-form/split-prize-form.module';
import { BaseSettingsComponent } from './base-settings.component';
import { DropNotificationsModule } from './drop-notifications/drop-notifications.module';

@NgModule({
  imports: [
    MatDialogModule,
    TranslateModule,
    SplitPrizeFormModule,
    ReactiveFormsModule,
    MatCheckboxModule,
    MatIconModule,
    MatTooltipModule,
    CommonModule,
    DropNotificationsModule,
    FormsModule,
  ],
  declarations: [
    BaseSettingsComponent,
  ],
  exports: [
    BaseSettingsComponent,
  ],
})
export class BaseSettingsModule {
}
