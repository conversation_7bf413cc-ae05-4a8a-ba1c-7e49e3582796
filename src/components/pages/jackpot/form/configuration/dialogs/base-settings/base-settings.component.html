<form *ngIf="form" [formGroup]="form" class="base-settings">
  <div class="base-settings__item">
    <mat-checkbox [formControl]="enableSplitPrizeControl" class="base-settings__checkbox">
      {{ 'JACKPOT.FORM.CONFIGURATION.sharedPrize' | translate }}
    </mat-checkbox>

    <sw-split-prize-form
      *ngIf="splitPrizeControl"
      [form]="splitPrizeControl"
      [baseCurrency]="baseCurrency"
      [isDisabled]="isDisabled">
    </sw-split-prize-form>
  </div>

  <div class="base-settings__item">
    <div class="base-settings__title">
      <mat-checkbox [formControl]="enableNotificationsControl" class="base-settings__checkbox">
        Jackpot drop notifications
      </mat-checkbox>
      <mat-icon
        class="help-icon"
        svgIcon="question_mark"
        matTooltip="{{ 'JACKPOT.FORM.CONFIGURATION.dropNotificationsTooltip' | translate }}">
      </mat-icon>
    </div>

    <sw-drop-notifications
      *ngIf="showNotificationToasters"
      [formControl]="winNotificationControl"
      [baseCurrency]="baseCurrency">
    </sw-drop-notifications>
  </div>
</form>
