import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { initSplitPrizeForm } from '../split-prize-form/split-prize-form.component';

@Component({
  selector: 'sw-base-settings',
  templateUrl: 'base-settings.component.html',
  styleUrls: ['base-settings.component.scss'],
})
export class BaseSettingsComponent implements OnInit, OnDestroy {

  @Input() form: FormGroup | undefined;
  @Input() baseCurrency?: string;

  @Input()
  set isDisabled( val: boolean ) {
    this._isDiabled = val;
    if (this._isDiabled) {
      this.enableSplitPrizeControl.disable({ emitEvent: false });
      this.enableNotificationsControl.disable({ emitEvent: false });
    } else {
      this.enableSplitPrizeControl.enable({ emitEvent: false });
      this.enableNotificationsControl.enable({ emitEvent: false });
    }
  }

  get isDisabled(): boolean {
    return this._isDiabled;
  }

  readonly isSuperAdmin;
  readonly enableNotificationsControl = new FormControl(true);
  readonly enableSplitPrizeControl = new FormControl(false);

  showNotificationToasters = true;

  private readonly destroyed$ = new Subject<void>();
  private _isDiabled = false;

  constructor( { isSuperAdmin }: SwHubAuthService ) {
    this.isSuperAdmin = isSuperAdmin;

    this.enableSplitPrizeControl.valueChanges
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe(( checked: boolean ) => {
        checked ?
          this.form?.addControl('splitPrize', initSplitPrizeForm()) :
          this.form?.removeControl('splitPrize');
      });

    this.enableNotificationsControl.valueChanges
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe(( checked: boolean ) => {
        this.showNotificationToasters = checked;
        if (this.winNotificationControl) {
          let winValue = { type: 'animation', amount: 0 };
          if (this.isWinNotificationEnabled() && Object.keys(this.winNotificationControl.value).length) {
            winValue = this.winNotificationControl.value;
          }
          this.winNotificationControl.setValue(checked ? winValue : null);
        }
      });
  }

  ngOnInit() {
    if (this.splitPrizeControl) {
      this.enableSplitPrizeControl.setValue(true);
    }

    if (this.winNotificationControl) {
      this.enableNotificationsControl.setValue(this.isWinNotificationEnabled());
    }
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get winNotificationControl(): FormControl | undefined {
    if (this.form) {
      return this.form.get('winNotification') as FormControl;
    }
  }

  get splitPrizeControl(): FormControl | undefined {
    if (this.form) {
      return this.form.get('splitPrize') as FormControl;
    }
  }

  private isWinNotificationEnabled(): boolean {
    return !!this.winNotificationControl && this.winNotificationControl.value !== null;
  }
}
