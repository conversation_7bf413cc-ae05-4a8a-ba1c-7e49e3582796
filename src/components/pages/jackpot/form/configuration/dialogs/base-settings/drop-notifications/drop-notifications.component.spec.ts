import { CommonModule } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { DropNotificationsComponent } from './drop-notifications.component';
import { DROP_NOTIFICATIONS_MODULES } from './drop-notifications.module';

describe('DropNotificationsComponent', () => {
  let component: DropNotificationsComponent;
  let fixture: ComponentFixture<DropNotificationsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        NoopAnimationsModule,
        ...DROP_NOTIFICATIONS_MODULES,
      ],
      declarations: [ DropNotificationsComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DropNotificationsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
