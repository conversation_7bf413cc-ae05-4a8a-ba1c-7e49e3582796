import { Component, forwardRef, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  ControlValueAccessor, FormBuilder, FormControl, FormGroup, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors, Validators
} from '@angular/forms';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { MESSAGE_ERRORS } from '../../../../../../../../common/constants/errors-message-list';
import { WinNotification } from '../../../../../../interfaces/jackpot';


@Component({
  selector: 'sw-drop-notifications',
  templateUrl: './drop-notifications.component.html',
  styleUrls: ['./drop-notifications.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DropNotificationsComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => DropNotificationsComponent),
      multi: true
    },
  ]
})
export class DropNotificationsComponent implements ControlValueAccessor, OnInit, OnDestroy {
  @Input() baseCurrency?: string;
  onChange: ( _: any ) => void = (() => {
  });
  notificationTypes: SwuiSelectOption[] = [
    { id: 'animation', text: 'Engagement bar animation'},
    { id: 'toaster', text: 'Toaster'},
    { id: 'toaster_info', text: 'Toaster with winning player info'}
  ];
  readonly form: FormGroup;
  readonly messageErrors = MESSAGE_ERRORS;
  private readonly _destroyed$ = new Subject<void>();

  constructor( private readonly fb: FormBuilder ) {
    this.form = this.initForm();
  }

  ngOnInit(): void {
    this.form.valueChanges
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(() => {
        this.onChange(this.form.getRawValue());
      });
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  writeValue( val: WinNotification | null ): void {
    if (!val) {
      return;
    }
    this.form.patchValue(val);
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( isDisabled: boolean ): void {
    isDisabled ? this.form.disable() : this.form.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid || this.form.status === 'DISABLED' ? null : { invalidForm: { valid: false } };
  }

  get typeControl(): FormControl {
    return this.form.get('type') as FormControl;
  }

  get amountControl(): FormControl {
    return this.form.get('amount') as FormControl;
  }

  private initForm(): FormGroup {
    return this.fb.group({
      type: ['animation'],
      amount: [0, Validators.compose([
        Validators.required,
        Validators.min(0),
        Validators.max(99999999),
      ])]
    });
  }
}
