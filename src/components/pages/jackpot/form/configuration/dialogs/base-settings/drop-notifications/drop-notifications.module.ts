import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { SwuiControlMessagesModule, SwuiCurrencySymbolModule } from '@skywind-group/lib-swui';
import { DropNotificationsComponent } from './drop-notifications.component';

export const DROP_NOTIFICATIONS_MODULES = [
  ReactiveFormsModule,
  MatFormFieldModule,
  MatInputModule,
  MatRadioModule,
  SwuiCurrencySymbolModule,
  SwuiControlMessagesModule,
];

@NgModule({
  declarations: [DropNotificationsComponent],
  exports: [DropNotificationsComponent],
  imports: [
    CommonModule,
    ...DROP_NOTIFICATIONS_MODULES
  ]
})
export class DropNotificationsModule { }
