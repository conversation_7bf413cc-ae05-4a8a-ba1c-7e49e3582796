<div class="drop-notifications" [formGroup]="form">
  <div class="drop-notifications__amount">
    <div class="drop-notifications__amount-title">Show drop notifications when drop amount is at least</div>
    <div class="drop-notifications__currency">{{baseCurrency | currencySymbol}}</div>
    <mat-form-field appearance="outline" class="drop-notifications__field">
      <input type="number" matInput [formControl]="amountControl">
      <mat-error>
        <lib-swui-control-messages [control]="amountControl" [messages]="messageErrors"></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </div>
  <div class="drop-notifications__type">
    <div class="drop-notifications__type-title">
      Notifications type:
    </div>
    <mat-radio-group [formControl]="typeControl" class="drop-notifications__radio">
      <mat-radio-button *ngFor="let type of notificationTypes" [value]="type.id">
        {{type.text}}
      </mat-radio-button>
    </mat-radio-group>
  </div>
</div>
