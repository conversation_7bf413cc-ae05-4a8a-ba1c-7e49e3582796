import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { BaseSettingsModule } from '../base-settings/base-settings.module';
import { DailySettingsComponent } from './daily-settings.component';
import { DropTimeModule } from './drop-time/drop-time.module';

@NgModule({
  imports: [
    MatButtonModule,
    MatDialogModule,
    MatIconModule,
    TranslateModule,
    BaseSettingsModule,
    ReactiveFormsModule,
    FlexModule,
    MatFormFieldModule,
    SwuiControlMessagesModule,
    MatSelectModule,
    MatInputModule,
    CommonModule,
    SwuiSelectModule,
    MatCheckboxModule,
    DropTimeModule,
  ],
  declarations: [
    DailySettingsComponent,
  ],
  exports: [
    DailySettingsComponent,
  ],
})
export class DailySettingsModule {
}
