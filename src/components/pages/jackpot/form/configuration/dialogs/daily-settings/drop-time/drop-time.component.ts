import { Component, forwardRef, HostListener, OnInit } from '@angular/core';
import {
  ControlValueAccessor,
  FormControl,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ValidationErrors,
  Validator,
  Validators
} from '@angular/forms';
import { takeUntil } from 'rxjs/operators';
import { BaseComponent } from '../../../../../../../../common/components/base.component';

@Component({
  selector: 'sw-drop-time',
  templateUrl: './drop-time.component.html',
  styleUrls: ['./drop-time.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DropTimeComponent),
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => DropTimeComponent),
      multi: true,
    },
  ],
})
export class DropTimeComponent extends BaseComponent implements ControlValueAccessor, Validator, OnInit {
  readonly midnightInMilliseconds = 86399999;

  dropTimeControl: FormControl = new FormControl(0, Validators.required);
  dropAtMidnightControl: FormControl = new FormControl(false);

  onChange: ( _: any ) => void = (() => {
  });

  constructor() {
    super();
  }

  @HostListener('blur') onblur() {
    this.onTouched();
  }

  ngOnInit() {
    this.dropTimeControl.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: number ) => {
        this.onChange(val);
      });

    this.dropAtMidnightControl.valueChanges.subscribe(
      value => {
        if (value) {
          this.dropTimeControl?.setValue(this.midnightInMilliseconds);
          this.dropTimeControl?.disable({ emitEvent: false });
          this.onChange(this.midnightInMilliseconds);
        } else {
          this.dropTimeControl?.enable();
          this.dropTimeControl?.setValue('0');
        }
      }
    );
  }

  writeValue( value: number ) {
    if (!value) {
      return;
    }

    this.dropTimeControl.patchValue(value);

    if (value === this.midnightInMilliseconds) {
      this.dropAtMidnightControl.setValue(true, { emitEvent: true });
    }
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: any ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( disabled: boolean ) {
    if (disabled) {
      this.dropTimeControl.disable({ emitEvent: false });
      this.dropAtMidnightControl.disable({ emitEvent: false });
    } else {
      this.dropTimeControl.enable();
      this.dropAtMidnightControl.enable();
    }
  }

  validate(): ValidationErrors | null {
    return this.dropTimeControl.disabled || this.dropTimeControl.valid ?
      null :
      { invalidForm: { valid: false } };
  }
}
