import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiStartTimeModule } from '@skywind-group/lib-swui';
import { DropTimeComponent } from './drop-time.component';

@NgModule({
  imports: [
    MatFormFieldModule,
    TranslateModule,
    MatCheckboxModule,
    ReactiveFormsModule,
    SwuiStartTimeModule,
    SwuiControlMessagesModule,
    FlexModule,
  ],
  exports: [
    DropTimeComponent,
  ],
  declarations: [
    DropTimeComponent,
  ],
})
export class DropTimeModule {
}
