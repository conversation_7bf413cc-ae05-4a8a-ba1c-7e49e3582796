import { Component, Inject, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { getTimeZones } from '../../../../../../../app.constants';
import { BaseComponent } from '../../../../../../../common/components/base.component';
import { TimeZone } from '../../../../../../../common/models/time-zone';
import { READONLY_JURISDICTION_ID } from '../../../../../interfaces/jackpot';
import { initSplitPrizeForm } from '../split-prize-form/split-prize-form.component';

@Component({
  selector: 'sw-daily-settings',
  templateUrl: './daily-settings.component.html',
  styleUrls: ['./daily-settings.component.scss', '../../jackpot-configuration-form.component.scss'],
})
export class DailySettingsComponent extends BaseComponent implements OnInit {
  form: FormGroup | undefined;
  baseCurrency?: string;
  isDisabled?: boolean;

  initialFormValue: any;

  timezones: TimeZone[] = getTimeZones();

  private isOwner = false;

  constructor( @Inject(MAT_DIALOG_DATA) public data: any,
               public dialogRef: MatDialogRef<any>,
  ) {
    super();

    if (this.data) {
      this.form = this.data.settingsItem;
      this.baseCurrency = this.data.baseCurrency;
      this.isOwner = this.data.isOwner;

      this.isDisabled = this.isOwner
        ? this.data.isDisabled && !this.needEnableStatus
        : this.data.isDisabled;

      if (this.data.settingsItem) {
        this.initialFormValue = this.data.settingsItem.getRawValue();
      }
    }
  }

  get needEnableStatus(): boolean {
    return this.data && ((this.data.status === 'running' && this.data.jurisdiction !== READONLY_JURISDICTION_ID)
      || this.data.status === 'pending');
  }

  ngOnInit() {
    if (this.needEnableStatus && this.isOwner) {
      this.form?.enable();
    }
  }

  isFormValid(): boolean {
    return this.form ? this.form.valid : false;
  }

  get dropTimeControl(): FormControl | undefined {
    if (this.form) {
      return this.form.get('dropTime') as FormControl;
    }
  }

  get timezoneControl(): FormControl | undefined {
    if (this.form) {
      return this.form.get('timezone') as FormControl;
    }
  }

  close() {
    if (this.form) {
      if (this.initialFormValue && this.initialFormValue.splitPrize) {
        this.form.addControl('splitPrize', initSplitPrizeForm());
      } else {
        this.form.removeControl('splitPrize');
      }

      const winNotificationControl = this.form.get('winNotification');

      if (this.initialFormValue && this.initialFormValue.winNotification && winNotificationControl) {
        let initialWinNotification = this.initialFormValue.winNotification;
        winNotificationControl.reset(initialWinNotification);
      }

      this.form.patchValue(this.initialFormValue, { emitEvent: true });
    }
  }
}
