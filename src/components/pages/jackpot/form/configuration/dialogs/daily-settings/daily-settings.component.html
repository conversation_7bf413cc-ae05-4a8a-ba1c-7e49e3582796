<button class="mat-dialog-close" mat-button mat-dialog-close (click)="close()" tabindex="-1">
  <mat-icon svgIcon="clear" style="color: grey;"></mat-icon>
</button>
<h2 mat-dialog-title style="padding-left: 20px;">
  {{ 'JACKPOT.FORM.CONFIGURATION.dailyJackpotSettings' | translate }}
</h2>

<mat-dialog-content style="max-height: 70vh !important;">
  <form [formGroup]="form">
    <div fxLayout="row" class="daily_settings">
      <mat-form-field appearance="outline" class="daily_settings__start">
        <mat-label>{{ 'JACKPOT.FORM.CONFIGURATION.timeZone' | translate }}</mat-label>
        <lib-swui-select
          [showSearch]="true"
          [disableEmptyOption]="true"
          [data]="timezones"
          formControlName="timezone"
          required></lib-swui-select>
        <mat-error>
          <lib-swui-control-messages [control]="timezoneControl"></lib-swui-control-messages>
        </mat-error>
      </mat-form-field>

      <sw-drop-time [formControl]="dropTimeControl"></sw-drop-time>
    </div>

    <sw-base-settings [form]="form" [baseCurrency]="baseCurrency" [isDisabled]="isDisabled"></sw-base-settings>
  </form>
</mat-dialog-content>

<mat-dialog-actions class="actions">
  <button mat-dialog-close
          mat-flat-button
          class="mat-button-md link"
          (click)="close()">
    {{ (form?.enabled ? 'DIALOG.cancel' : 'DIALOG.ok') | translate }}
  </button>
  <button *ngIf="form?.enabled"
          mat-flat-button
          color="primary"
          class="mat-button-md"
          style="margin-left: 20px;"
          mat-dialog-close="{{ form?.value }}"
          [disabled]="!isFormValid()">
    {{ 'DIALOG.ok' | translate }}
  </button>
</mat-dialog-actions>
