import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { CurrenciesResolver } from '../../../common/services/currency.service';
import { GamesResolver } from '../../../common/services/game.service';
import { JackpotService } from '../../../common/services/jackpot.service';
import { JackpotListModule } from './jackpot-list/jackpot-list.module';
import { JackpotRoutingModule } from './jackpot-routing.module';
import { JackpotUpdateModule } from './jackpot-update/jackpot-update.module';
import { JackpotComponent } from './jackpot.component';

@NgModule({
  imports: [
    CommonModule,
    JackpotListModule,
    JackpotRoutingModule,
    JackpotUpdateModule,
  ],
  declarations: [
    JackpotComponent,
  ],
  providers: [
    CurrenciesResolver,
    JackpotService,
    GamesResolver
  ]
})
export class JackpotModule {
}
