import { Component, forwardRef, HostListener, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatTabGroup } from '@angular/material/tabs';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  ActionConfirmDialogComponent,
  SwBrowserTitleService,
  SwHubAuthService,
  SWUI_CONTROL_MESSAGES,
  SwuiIsControlInvalidService,
  SwuiNotificationsService
} from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, finalize, map, switchMap, take, takeUntil } from 'rxjs/operators';

import { BaseComponent } from '../../../../common/components/base.component';
import { CurrencyModel } from '../../../../common/models/currency.model';
import { isBrandType } from '../../../../common/models/entity';
import { GameInfo } from '../../../../common/models/game';
import { JackpotService } from '../../../../common/services/jackpot.service';
import { FeatureGeneral, OperatorsBrand, SegmentationBrand } from '../../interfaces/feature';
import { Jackpot, READONLY_JURISDICTION_ID } from '../../interfaces/jackpot';
import { JackpotFormService } from './jackpot-form-service/jackpot-form.service';
import { SW_FORM_SERVICE } from '../../../../common/components/games-form/form-service.model';
import { ErrorStateMatcher } from '@angular/material/core';
import { MESSAGE_ERRORS } from '../../../../common/constants/errors-message-list';

@Component({
  selector: 'sw-jackpot-update',
  templateUrl: 'jackpot-update.component.html',
  styleUrls: ['jackpot-update.component.scss'],
  providers: [
    JackpotFormService,
    { provide: SW_FORM_SERVICE, useExisting: forwardRef(() => JackpotFormService) },
    { provide: ErrorStateMatcher, useExisting: forwardRef(() => JackpotFormService) },
    { provide: SwuiIsControlInvalidService, useExisting: forwardRef(() => JackpotFormService) },
    { provide: SWUI_CONTROL_MESSAGES, useValue: MESSAGE_ERRORS }
  ]
})
export class JackpotUpdateComponent extends BaseComponent implements OnDestroy {

  jackpot$: Subject<Jackpot> = new Subject();
  status: string | undefined;
  currencies: CurrencyModel[] = [];
  baseCurrency?: string;
  jackpotId?: string;
  isSidebarHidden = false;
  isDuplicate = false;
  isDisabled = false;
  userType: string | undefined;
  isJackpotInfoDisabled = false;
  sourceGames: GameInfo[] = [];
  isOwner: boolean;
  isBrand: boolean;
  canSave = false;
  jurisdiction?: string;

  @ViewChild('tabs') tabs?: MatTabGroup;

  set jackpot( value: Jackpot ) {
    this._jackpot = value;
  }

  get jackpot(): Jackpot {
    return this._jackpot;
  }

  readonly form: FormGroup;

  private _jackpot: Jackpot = {} as Jackpot;

  constructor( private readonly route: ActivatedRoute,
               private readonly fb: FormBuilder,
               private readonly service: JackpotService,
               private readonly formService: JackpotFormService,
               private readonly notifications: SwuiNotificationsService,
               private readonly translate: TranslateService,
               private readonly dialog: MatDialog,
               private readonly authService: SwHubAuthService,
               private readonly router: Router,
               protected readonly browserTitleService: SwBrowserTitleService,
  ) {
    super();
    this.hideSidebar();
    this.form = this.initForm();
    const { data: { games, currencies, jackpot, duplicate, brief }, params: { id } } = this.route.snapshot;
    this.sourceGames = games;
    this.isDuplicate = duplicate;
    this.currencies = currencies;
    this.userType = brief && brief.type;
    this.jackpotId = id;

    this.isOwner = !(id && !this.authService.isSuperAdmin && brief.id !== jackpot?.operators?.owner?.id);
    this.isBrand = isBrandType(brief);

    this.segmentationControl.valueChanges.pipe(
      map(() => {
        const { segmentation: { brands }, operators } = this.form.getRawValue();
        return [brands, operators];
      }),
      filter(( [brands] ) => {
        return !!brands.length;
      }),
      takeUntil(this.destroyed),
    ).subscribe(( [brands, operators] ) => {
      if (!operators) {
        operators = { brands: [] };
      }
      const operatorsSet = new Set(operators.brands.map(( el: OperatorsBrand ) => el.id));
      (brands as SegmentationBrand[]).forEach(brand => {
        if (!operatorsSet.has(brand.id)) {
          operators.brands.push({ id: brand.id });
        }
      });
      this.operatorsControl.patchValue(operators, { emitEvent: false });
    });

    this.operatorsControl.valueChanges.pipe(
      map(() => {
        const { operators, segmentation } = this.form.getRawValue();
        const segmentationBrands = segmentation && segmentation.brands ? segmentation.brands.map(( el: SegmentationBrand ) => el.id) : [];
        const segmentationSet = new Set<string>(segmentationBrands);
        const operatorsBrands = operators && operators.brands ? operators.brands.map(( el: OperatorsBrand ) => el.id) : [];
        const operatorsSet = new Set<string>(operatorsBrands);
        return [segmentationSet, operatorsSet, operators, segmentation];
      }),
      filter(( [segmentationSet, operatorsSet] ) => {
        return !isEqualSets(segmentationSet, operatorsSet);
      }),
      takeUntil(this.destroyed)
    ).subscribe(( [segmentationSet, operatorsSet, operators, segmentation] ) => {
      if (!segmentation) {
        segmentation = { brands: [] };
      }
      segmentation.brands?.forEach(( brand: SegmentationBrand, index: number ) => {
        if (!operatorsSet.has(brand.id)) {
          segmentation.brands.splice(index, 1);
        }
      });
      operators.brands?.forEach(( brand: OperatorsBrand ) => {
        if (!segmentationSet.has(brand.id)) {
          segmentation.brands.push({ id: brand.id, players: [] });
        }
      });
      this.segmentationControl.patchValue(segmentation, { emitEvent: false });
    });

    this.generalControl.valueChanges.pipe(
      filter<FeatureGeneral>(Boolean),
      takeUntil(this.destroyed)
    ).subscribe(( { jurisdiction } ) => {
      if (jurisdiction !== undefined) {
        this.jurisdiction = jurisdiction;
      }
    });

    if (!this.isOwner && !duplicate) {
      this.isJackpotInfoDisabled = true;
      this.form.disable({ emitEvent: false });
    }

    this.jackpot$.subscribe(jpot => {
      this.setupJackpot(jpot);
    });

    this.jackpot$.next(jackpot);

    this.browserTitleService.setupTitles('Engagement', jackpot?.general?.name || '');
  }

  @HostListener('window:resize') onResize() {
    this.hideSidebar();
  }

  ngOnDestroy() {
    super.ngOnDestroy();
    this.formService.formSubmitted = false;
  }

  onStatusChanged( status: string ) {
    this.status = status;

    if (!this.jackpot || !this.jackpot.id) {
      return;
    }

    this.service.get(this.jackpot.id)
      .subscribe(jackpot => {
        this.jackpot$.next(jackpot);
      });
  }

  onSubmit() {
    this.generalControl?.updateValueAndValidity();
    // on disabled status form is not valid and not invalid
    if (this.form.valid || this.form.disabled) {
      if (!this.isDuplicate && this.status && ['enabled', 'running', 'pending'].indexOf(this.status) !== -1) {
        const operatorsWithAllPlayers = getOperatorsWithAllPlayers(this.form.getRawValue());
        const confirmText = !!operatorsWithAllPlayers.length ?
          this.translate.instant('JACKPOT.NOTIFICATIONS.makeChangesWillAffectAllPlayers',
            { brands: operatorsWithAllPlayers.join(', ') }) :
          this.translate.instant('JACKPOT.NOTIFICATIONS.makeChanges');

        this.dialog.open(ActionConfirmDialogComponent, {
          data: {
            action: {
              confirmText,
            }
          }
        }).afterClosed().subscribe(confirmed => {
            if (confirmed) {
              this.updateJackpot();
            }
          }
        );
      } else {
        this.updateJackpot();
      }
    } else {
      this.formService.formSubmitted = true;
      this.switchToFirstInvalidTab();
    }
  }

  get generalControl(): FormControl {
    return this.form.get('general') as FormControl;
  }

  get segmentationControl(): FormControl {
    return this.form.get('segmentation') as FormControl;
  }

  get operatorsControl(): FormControl {
    return this.form.get('operators') as FormControl;
  }

  get configurationControl(): FormControl {
    return this.form.get('configuration') as FormControl;
  }

  get gamesControl(): FormControl {
    return this.form.get('games') as FormControl;
  }

  get topPanelTitle(): string | undefined {
    return this.jackpotId && this.jackpot && this.jackpot.general ?
      this.translate.instant('JACKPOT.editJackpot', { name: this.jackpot.general.name }) :
      this.translate.instant('JACKPOT.createJackpot');
  }

  private initForm(): FormGroup {
    return this.fb.group({
      general: ['', Validators.required],
      configuration: ['', Validators.required],
      games: ['', Validators.required],
      operators: ['', Validators.required],
      segmentation: ['', Validators.required],
      ui: ['']
    });
  }

  private setupJackpot( jackpot: Jackpot ) {
    this.jackpot = jackpot || { type: 'jackpot', status: 'disabled' };

    if (jackpot) {
      this.status = jackpot.status;
    }

    if (this.isDuplicate) {
      this.jackpotId = undefined;
      this.jackpot.active = false;
      this.jackpot.status = 'disabled';
      this.jackpot.modifiedBy = undefined;
      this.jackpot.activatedAt = undefined;

      delete this.jackpot.id;
      delete this.jackpot.drops;
      delete this.jackpot.created;
      delete this.jackpot.updated;
      delete this.jackpot.activated;
      delete this.jackpot.general.name;

      if (!this.authService.isSuperAdmin) {
        this.jackpot.operators.owner = null;
      }
    }
    this.jurisdiction = jackpot?.general?.jurisdiction;

    if (jackpot && (jackpot.status === 'running' || jackpot.status === 'pending') && !this.isDuplicate) {
      this.configurationControl.disable({ emitEvent: false });
      if (jackpot.general.jurisdiction === READONLY_JURISDICTION_ID) {
        this.form.get('ui')?.disable({ emitEvent: false });
      }
    }

    if (jackpot && jackpot.status === 'expired' && !this.isDuplicate) {
      this.form.disable({ emitEvent: false });
    }

    if (jackpot?.status === 'running' && jackpot?.general?.jurisdiction === READONLY_JURISDICTION_ID && !this.isDuplicate) {
      this.form.disable({emitEvent: false});
      this.canSave = false;
    } else {
      this.canSave = this.isOwner || this.isBrand || this.isDuplicate;
    }

    this.form.patchValue(this.jackpot);
  }

  private hideSidebar() {
    this.isSidebarHidden = window.innerWidth <= 1300;
  }

  private updateJackpot() {
    this.isDisabled = true;
    let jackpot = this.form.getRawValue();

    if (this.jackpotId) {
      let updatedJackpot = { ...this.jackpot, ...jackpot };

      if ([this.jackpot.status, this.status].indexOf('running') !== -1 || [this.jackpot.status, this.status].indexOf('pending') !== -1) {
        updatedJackpot.configuration = { ...this.jackpot.configuration, ...this.configurationControl.value };
      }

      updatedJackpot.status = this.status;

      this.service.update(this.jackpotId, updatedJackpot)
        .pipe(
          take(1),
          switchMap(( item: Jackpot ) => this.service.get(item.id as string)),
          finalize(() => this.isDisabled = false)
        )
        .subscribe(( data: Jackpot ) => {
          if (data) {
            this.isDuplicate = false;
            this.jackpot$.next(data);

            this.notifications.success(
              this.translate.instant('JACKPOT.updatedJackpotMessage', { name: updatedJackpot.general.name }),
            );
          }
        });
    } else {
      jackpot.type = 'jackpot';
      jackpot.status = 'disabled';
      jackpot.assets = [];

      this.service.create(jackpot)
        .pipe(
          take(1),
          finalize(() => this.isDisabled = false)
        )
        .subscribe(( item: Jackpot ) => {
          if (item) {
            this.isDuplicate = false;
            this.router.navigate(['./pages/jackpot/edit', item.id]);
            this.notifications.success(
              this.translate.instant('JACKPOT.createdJackpotMessage')
            );
            if (this.tabs) {
              this.tabs.selectedIndex = 0;
            }
          }
        });
    }
  }

  private switchToFirstInvalidTab() {
    const controls = Object.keys(this.form.controls);
    const firstInvalid = controls.find(key => this.form.controls[key].invalid);
    if (this.tabs && firstInvalid) {
      this.tabs.selectedIndex = controls.indexOf(firstInvalid);
    }
  }
}

export function getOperatorsWithAllPlayers( feature: Jackpot ): string[] {
  let operatorsBrands: OperatorsBrand[] = feature.operators?.brands || [];
  let segmentationBrands: SegmentationBrand[] = feature.segmentation?.brands || [];
  let operatorsWithAllPlayers: string[] = [];

  segmentationBrands?.forEach(( segmentationBrand: SegmentationBrand ) => {
    if (!segmentationBrand.players.length) {
      const operator = operatorsBrands.find(( brand: OperatorsBrand ) => brand.id === segmentationBrand.id);
      if (operator) {
        operatorsWithAllPlayers.push(operator.title || operator.id);
      }
    }
  });
  return operatorsWithAllPlayers;
}

function isEqualSets( as: Set<string>, bs: Set<string> ): boolean {
  if (as.size !== bs.size) {
    return false;
  }
  for (let a of as) {
    if (!bs.has(a)) {
      return false;
    }
  }
  return true;
}
