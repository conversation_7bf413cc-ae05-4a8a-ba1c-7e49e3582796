import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { GamesFormModule } from '../../../../common/components/games-form/games-form.module';
import { JackpotConfigurationFormModule } from '../form/configuration/jackpot-configuration-form.module';
import { JackpotGeneralFormModule } from '../form/general/jackpot-general-form.module';
import { JackpotOperatorsFormModule } from '../form/operators/jackpot-operators-form.module';
import { JackpotSegmentationFormModule } from '../form/segmentation/jackpot-segmentation-form.module';
import { JackpotUiModule } from '../form/ui/jackpot-ui.module';
import { JackpotInfoModule } from './jackpot-info/jackpot-info.module';
import { JackpotUpdateComponent } from './jackpot-update.component';

@NgModule({
    imports: [
      CommonModule,
      RouterModule,
      TranslateModule.forChild(),
      ReactiveFormsModule,
      MatTabsModule,
      MatIconModule,
      MatButtonModule,
      MatCardModule,
      FlexLayoutModule,
      JackpotGeneralFormModule,
      JackpotConfigurationFormModule,
      GamesFormModule,
      JackpotSegmentationFormModule,
      JackpotInfoModule,
      SwuiPagePanelModule,
      JackpotOperatorsFormModule,
      JackpotUiModule,
    ],
  declarations: [
    JackpotUpdateComponent,
  ],
})
export class JackpotUpdateModule {
}
