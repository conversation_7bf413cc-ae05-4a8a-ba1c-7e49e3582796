<lib-swui-page-panel [title]="topPanelTitle" [back]="true" backUrl="/pages/jackpot"></lib-swui-page-panel>
<div class="page-jackpot">
  <div class="jackpot-edit">
    <mat-tab-group #tabs class="jackpot-edit__main" [formGroup]="form" animationDuration="0ms">

      <mat-tab>
        <ng-template mat-tab-label>{{ 'JACKPOT.generalTab' | translate }}</ng-template>
        <sw-jackpot-general-form
          formControlName="general"
          [status]="jackpot?.status">
        </sw-jackpot-general-form>
      </mat-tab>

      <mat-tab>
        <ng-template mat-tab-label>{{ 'JACKPOT.configurationTab' | translate }}</ng-template>
        <sw-jackpot-configuration-form [userType]="userType"
                                       [status]="status"
                                       [isDuplicate]="isDuplicate"
                                       [isOwner]="isOwner"
                                       [jurisdiction]="jackpot?.general?.jurisdiction"
                                       formControlName="configuration">
        </sw-jackpot-configuration-form>
      </mat-tab>

      <mat-tab>
        <ng-template mat-tab-label>{{ 'JACKPOT.gamesTab' | translate }}</ng-template>
        <sw-games-form formControlName="games" [sourceGames]="sourceGames"></sw-games-form>
      </mat-tab>

      <mat-tab>
        <ng-template mat-tab-label>{{ 'JACKPOT.operatorsTab' | translate }}</ng-template>
        <sw-jackpot-operators-form formControlName="operators"></sw-jackpot-operators-form>
      </mat-tab>

      <mat-tab>
        <ng-template mat-tab-label>{{ 'JACKPOT.segmentationTab' | translate }}</ng-template>
        <sw-jackpot-segmentation-form [currenciesList]="currencies"
                                      [canPlayersEdit]="canSave"
                                      [status]="jackpot?.status"
                                      formControlName="segmentation">
        </sw-jackpot-segmentation-form>
      </mat-tab>

      <mat-tab>
        <ng-template mat-tab-label>UI</ng-template>
        <sw-jackpot-ui
          formControlName="ui"
          [games]="gamesControl?.value"
          [jurisdiction]="jurisdiction"
          [sourceGames]="sourceGames">
        </sw-jackpot-ui>
      </mat-tab>

      <mat-tab label="Info" *ngIf="isSidebarHidden">
        <ng-template mat-tab-label>
          <span>Info</span>
        </ng-template>
        <ng-template matTabContent>
          <div class="mat-card">
            <sw-jackpot-info [jackpot]="jackpot"
                             [id]="jackpotId"
                             [disabled]="isJackpotInfoDisabled"
                             (status)="onStatusChanged($event)">
            </sw-jackpot-info>
          </div>
        </ng-template>
      </mat-tab>

    </mat-tab-group>

    <div class="jackpot-edit__sidebar" *ngIf="!isSidebarHidden">
      <sw-jackpot-info [jackpot]="jackpot"
                       [id]="jackpotId"
                       [disabled]="isJackpotInfoDisabled"
                       (status)="onStatusChanged($event)">
      </sw-jackpot-info>
      <div class="padding-top32" [ngClass]="{'sidebar-hidden': isSidebarHidden}">
        <div class="controls">
          <button mat-flat-button class="controls__button" routerLink="/pages/jackpot">
            {{ 'COMMON.ACTIONS.cancel' | translate }}
          </button>
          <button mat-flat-button
                  class="controls__button link"
                  [disabled]="(jackpot?.status === 'expired' && !isDuplicate) || isDisabled || !canSave"
                  [color]="'primary'"
                  (click)="onSubmit()">
            {{ 'COMMON.ACTIONS.save' | translate }}
          </button>
        </div>
      </div>
    </div>

  </div>

  <div *ngIf="isSidebarHidden" class="footer" [ngClass]="{'sidebar-hidden': isSidebarHidden}">
    <div class="controls">
      <button mat-flat-button class="controls__button link" routerLink="/pages/jackpot">
        {{ 'COMMON.ACTIONS.cancel' | translate }}
      </button>
      <button mat-flat-button
              class="controls__button"
              [color]="'primary'"
              [disabled]="jackpot?.status === 'expired' && !isDuplicate || isDisabled || !canSave"
              (click)="onSubmit()">
        {{ 'COMMON.ACTIONS.save' | translate }}
      </button>
    </div>
  </div>

</div>
