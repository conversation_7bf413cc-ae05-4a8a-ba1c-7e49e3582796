import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { FormSubmitted } from '../../../../../common/lib/form.submitted';
import { PayoutItem } from '../../../interfaces/jackpot';

@Injectable()
export class JackpotFormService extends FormSubmitted {
  readonly payout$ = new BehaviorSubject<{ payout?: PayoutItem; baseCurrency?: string; }>({});
  readonly poolType$ = new BehaviorSubject<string>('');
  readonly currencies$ = new BehaviorSubject<string[]>([]);
}
