import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSlideToggle, MatSlideToggleChange } from '@angular/material/slide-toggle';
import { TranslateService } from '@ngx-translate/core';
import {
  ActionConfirmDialogComponent, SettingsService, SwHubConfigService, SwuiNotificationsService
} from '@skywind-group/lib-swui';
import { AppSettings } from '@skywind-group/lib-swui/services/settings/app-settings';
import { takeUntil, tap } from 'rxjs/operators';
import { REPORT_ID } from '../../../../../app.constants';
import { BaseInfoComponent } from '../../../../../common/components/base-info.component';
import { SchemaFieldMapItem } from '../../../../../common/models/schema-field-map-item';
import { JackpotService } from '../../../../../common/services/jackpot.service';
import { STATUS_MAP } from '../../../interfaces/feature';

import { Jackpot } from '../../../interfaces/jackpot';
import { getOperatorsWithAllPlayers } from '../jackpot-update.component';

@Component({
  selector: 'sw-jackpot-info',
  templateUrl: './jackpot-info.component.html',
  styleUrls: ['./jackpot-info.component.scss'],
})
export class JackpotInfoComponent extends BaseInfoComponent implements OnInit {

  @ViewChild('toggle') toggle: MatSlideToggle | undefined;

  @Input()
  set jackpot( val: Jackpot | undefined ) {
    if (!val) {
      return;
    }

    this._jackpot = val;
    this.setJackpotStatus(this._jackpot);
    this.initDates(this.jackpot);
  }

  get jackpot(): Jackpot | undefined {
    return this._jackpot;
  }

  @Input() id?: string;
  @Input() disabled = false;

  @Output() status: EventEmitter<string> = new EventEmitter();

  jackpotStatus: SchemaFieldMapItem | undefined;
  jackpotReportUrl = '';
  analyticsUrl: string | undefined;
  dropNames = {
    grand: 'no limit'
  };

  private _jackpot: Jackpot | undefined;
  private _userTimeZone: string | undefined;

  constructor( { hubs }: SwHubConfigService,
               private readonly service: JackpotService,
               private readonly notifications: SwuiNotificationsService,
               private readonly translate: TranslateService,
               private readonly dialog: MatDialog,
               protected readonly settingsService: SettingsService,
  ) {
    super(settingsService);
    if (hubs && hubs.analytics) {
      const { url } = hubs.analytics;
      this.analyticsUrl = url;
    }
  }

  ngOnInit(): void {
    this.initDates(this.jackpot);
    this.jackpotReportUrl = `${this.analyticsUrl}/${REPORT_ID.jackpot}/${this.jackpot?.id}`;

    this.settingsService.settings.pipe(
      tap(( data: AppSettings ) => this._userTimeZone = data.timezoneName),
      takeUntil(this.destroyed)
    ).subscribe();
  }

  get disableToggle() {
    if (this.jackpot) {
      if (!!this.jackpot.id && this.jackpot.status !== 'expired' && !this.disabled) {
        return false;
      }
    }
    return true;
  }

  get userTimeZone(): string | undefined {
    return this._userTimeZone;
  }

  triggerActive( event: MatSlideToggleChange ) {
    if (this.id) {
      const id = this.id;

      if (this.jackpot) {
        const operatorsWithAllPlayers = getOperatorsWithAllPlayers(this.jackpot);
        const confirmText = !!operatorsWithAllPlayers.length ?
          this.translate.instant('JACKPOT.NOTIFICATIONS.makeChangesWillAffectAllPlayers',
            { brands: operatorsWithAllPlayers.join(', ') }) :
          this.translate.instant('JACKPOT.NOTIFICATIONS.makeChanges');

        this.dialog.open(ActionConfirmDialogComponent, {
          data: {
            action: {
              confirmText: confirmText,
            },
            closeButtonText: 'DIALOG.no',
            confirmButtonText: 'DIALOG.yes',
          }
        }).afterClosed().subscribe(confirmed => {
            if (confirmed) {
              this.updateJackpotStatus(id, event.checked);
            } else {
              if (this.toggle) {
                this.toggle.checked = !this.toggle.checked;
              }
            }
          }
        );
      }
    }
  }

  updateJackpotStatus( id: string, isActive: boolean ) {
    this.service.patch(id, { active: isActive }).subscribe(
      ( jackpot: Jackpot ) => {
        if (this.jackpot) {
          this.setJackpotStatus(jackpot, true);
        }

        this.notifications.success(
          this.translate.instant('JACKPOT.NOTIFICATIONS.statusChangedMessage'),
        );
      },
      () => {
        if (this.toggle) {
          this.toggle.checked = !this.toggle.checked;
        }
      });
  }

  private setJackpotStatus( val: Jackpot, emit = false ) {
    this.jackpotStatus = STATUS_MAP.find(( item: SchemaFieldMapItem ) => item.id === val.status);
    if (this.jackpotStatus && emit) {
      this.status.emit(this.jackpotStatus.id);
    }
  }
}
