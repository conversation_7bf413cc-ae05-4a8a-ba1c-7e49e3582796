import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { PipesModule } from '../../../../../common/pipes/pipes.module';

import { JackpotInfoComponent } from './jackpot-info.component';

export const MODULES = [
  FlexLayoutModule,
  MatIconModule,
  MatSlideToggleModule,
  MatListModule,
];

@NgModule({
  declarations: [JackpotInfoComponent],
  imports: [
    CommonModule,
    TranslateModule,
    ...MODULES,
    PipesModule,
  ],
  exports: [JackpotInfoComponent],
})
export class JackpotInfoModule {
}
