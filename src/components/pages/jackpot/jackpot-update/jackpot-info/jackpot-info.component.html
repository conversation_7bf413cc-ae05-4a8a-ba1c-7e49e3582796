<div class="sd">
  <div class="sd__item">
    <div class="sd__title">{{ 'JACKPOT.FORM.INFO.jackpotStatus' | translate }}</div>
    <ul class="sd__list sd-list">
      <li class="sd-list__item">
        <div class="sd-list__title">{{ 'JACKPOT.FORM.INFO.active' | translate }}</div>
        <div class="sd-list__content">
          <mat-slide-toggle
            #toggle
            [disabled]="disableToggle"
            [checked]="jackpot?.active"
            (change)="triggerActive($event)">
          </mat-slide-toggle>
        </div>
      </li>

      <li class="sd-list__item">
        <div class="sd-list__title">{{ 'JACKPOT.FORM.INFO.status' | translate }}</div>
        <div class="sd-list__content">
          <div class="sw-chip" [ngClass]="jackpotStatus?.class">
            {{ jackpotStatus?.title }}
          </div>
        </div>
      </li>
    </ul>
  </div>

  <div class="sd__item">
    <div class="sd__title">{{ 'JACKPOT.FORM.INFO.activity' | translate }}</div>
    <ul class="sd__list sd-list">
      <li class="sd-list__item">
        <div class="sd-list__title">{{ 'JACKPOT.FORM.INFO.activated' | translate }}</div>
        <div class="sd-list__content">
          {{ jackpot?.activated | formatDate : 'MMM DD, YYYY HH:mm' : userTimeZone : false }}
        </div>
      </li>

      <li class="sd-list__item">
        <div class="sd-list__title">{{ 'JACKPOT.FORM.INFO.drops' | translate }}</div>
        <div class="sd-list__content">
          <div class="jp-info__drop" *ngFor="let drop of jackpot?.drops">
            {{ dropNames[drop.pool] || drop.pool }}: {{ drop.value || 0 }}
          </div>
        </div>
      </li>
    </ul>
  </div>

  <div class="sd__item">
    <div class="sd__title">{{ 'JACKPOT.FORM.INFO.reports' | translate }}</div>
    <ul class="sd__list sd-list">
      <ng-container *ngIf="jackpot?.id; else tplEmptyReports">
        <li class="sd-list__item">
          <a [href]=jackpotReportUrl class="sd-icon-link">
            <mat-icon svgIcon="icon_report" class="sd-icon-link__icon"></mat-icon>
            <div class="sd-icon-link__label">
              {{ 'JACKPOT.FORM.INFO.jackpotReports' | translate }}
            </div>
          </a>
        </li>
      </ng-container>

      <ng-template #tplEmptyReports>
        <div class="sd-empty">
          <mat-icon svgIcon="icon_report" class="sd-empty__icon"></mat-icon>
          <div class="sd-empty__label">{{ 'JACKPOT.FORM.INFO.noReportsToShow' | translate }}</div>
        </div>
      </ng-template>
    </ul>
  </div>

  <div class="sd__item">
    <div class="sd__title">{{ 'JACKPOT.FORM.INFO.info' | translate }}</div>
    <ul class="sd__list sd-list">
      <li class="sd-list__item">
        <div class="sd-list__title">{{ 'JACKPOT.FORM.INFO.featureId' | translate }}</div>
        <div class="sd-list__content">{{ jackpot?.id || '-' }}</div>
      </li>

      <li *ngIf="jackpot?.activated" class="sd-list__item">
        <div class="sd-list__title">{{ 'JACKPOT.FORM.INFO.id' | translate }}</div>
        <div class="sd-list__content">{{ jackpot?.jpnId || '-' }}</div>
      </li>

      <li class="sd-list__item">
        <div class="sd-list__title">{{ 'JACKPOT.FORM.INFO.created' | translate }}</div>
        <div class="sd-list__content">
          {{ jackpot?.created | formatDate : 'MMM DD, YYYY HH:mm' : userTimeZone : false }}
        </div>
      </li>

      <li class="sd-list__item">
        <div class="sd-list__title">Modified by</div>
        <div class="sd-list__content">
          {{ jackpot?.modifiedBy || '-' }}
        </div>
      </li>

      <li class="sd-list__item">
        <div class="sd-list__title">{{ 'JACKPOT.FORM.INFO.modified' | translate }}</div>
        <div class="sd-list__content">
          {{ jackpot?.updated | formatDate : 'MMM DD, YYYY HH:mm' : userTimeZone : false }}
        </div>
      </li>
    </ul>
  </div>
</div>
