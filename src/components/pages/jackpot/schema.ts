import { SchemaFilterMatchEnum, SwuiConstantsService, SwuiGridField } from '@skywind-group/lib-swui';
import { formatCurrency, REPORT_ID } from '../../../app.constants';
import { formatDate } from '../../../common/lib/format-date';
import { STATUS_MAP } from '../interfaces/feature';
import { Jackpot } from '../interfaces/jackpot';


interface SchemaFieldMapItem {
  id: string;
  title?: string;
  class?: string;
}

const SCHEMA: SwuiGridField[] = [
  {
    field: 'search',
    type: 'search',
    fields: 'id,name,operator,instanceId',
    title: 'Search by id, name, operator',
    isViewable: false,
    isFilterable: true,
    isSortable: false,
    filterMatch: {
      fields: SchemaFilterMatchEnum.Fields,
      text: SchemaFilterMatchEnum.Text
    },
    isFilterableAlways: true
  },
  {
    field: 'name',
    title: 'JACKPOT.GRID.name',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: false,
    isEditable: true,
    td: {
      type: 'link',
      titleFn: ( row: Jackpot ) => row.general.name,
      linkFn: ( row: Jackpot ) => {
        return ['./pages/jackpot/edit', row.id];
      },
      classFn: () => {
      },
      useTranslate: false
    }
  },
  {
    field: 'id',
    title: 'JACKPOT.GRID.featureId',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: false,
    isEditable: true,
    isListVisible: false
  },
  {
    field: 'jackpotType',
    title: 'JACKPOT.GRID.pools',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    isEditable: true,
    td: {
      type: 'calc',
      titleFn: ( row: Jackpot ) => row.configuration.poolType,
      classFn: () => {
      },
      useTranslate: false
    }
  },
  {
    field: 'status',
    title: 'JACKPOT.GRID.status',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: false,
    isEditable: true,
    isFilterable: true,
    isFilterableAlways: true,
    td: {
      type: 'calc',
      titleFn: ( row: any, schema: SwuiGridField ) => {
        const item: SchemaFieldMapItem | undefined =
          STATUS_MAP.find(( el: SchemaFieldMapItem ) => el.id === row[schema.field]);
        return item ? item.title : row[schema.field];
      },
      classFn: ( row: any, schema: SwuiGridField ) => {
        const item: SchemaFieldMapItem | undefined =
          STATUS_MAP.find(( el: SchemaFieldMapItem ) => el.id === row[schema.field]);
        return item ? item.class : '';
      },
      useTranslate: false
    },
    data: STATUS_MAP.filter(( { id } ) => ['disabled', 'running', 'expired'].includes(id)),
    emptyOption: {
      show: true,
      placeholder: '- All -'
    },
    alignment: {
      td: 'center',
      th: 'center'
    }
  },
  {
    field: 'lastExecution',
    title: 'JACKPOT.GRID.lastDrop',
    isList: false,
    isViewable: true,
    isSortable: false,
    isEditable: true,
    td: {
      type: 'timestamp',
      showTimeZone: true
    },
    isFilterable: false,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',
    type: 'datetimerange',
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    }
  },
  {
    field: 'lastAmount',
    title: 'JACKPOT.GRID.lastAmount',
    type: 'string',
    isList: false,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    isEditable: true,
    td: {
      type: 'calc',
      titleFn: ( row: Jackpot ) => {
        let currency = SwuiConstantsService.currencySymbol('EUR');
        if (row && row.lastDropAmount) {
          return (currency ? currency : '')
            + formatCurrency(row.lastDropAmount.toString());
        }
      },
      classFn: () => {
      },
      useTranslate: false
    },
    alignment: {
      td: 'right',
      th: 'right'
    }
  },
  {
    field: 'active',
    title: 'JACKPOT.GRID.active',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: false,
    isEditable: true,
    isFilterableAlways: true,
    td: {
      type: 'inactivity',
    },
    alignment: {
      th: 'center',
      td: 'center'
    },
    isFilterable: true,
    data: [{ id: 'true', title: 'Yes' }, { id: 'false', title: 'No' }],
  },
  {
    field: 'created',
    title: 'JACKPOT.GRID.created',
    type: 'datetimerange',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    isEditable: true,
    td: {
      type: 'calc',
      titleFn: ( row: Jackpot ) => {
        return formatDate(row.created, 'MMM DD, YYYY', '', false, false);
      },
      classFn: () => 'table-date',
      useTranslate: false
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    }
  },
  {
    field: 'operators',
    title: 'JACKPOT.GRID.operators',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    isEditable: true,
    search: {
      placeholder: 'Search',
      show: true
    },
    emptyOption: {
      show: true,
      placeholder: 'MASTER - All'
    },
    td: {
      type: 'list',
      arrayKey: 'title',
      valueFn: ( row: Jackpot ) => {
        return row && row.operators.brands ? row.operators.brands : [];
      },
      useTranslate: false
    },
    data: [],
    filter: {
      title: 'FILTER.resellerOperator',
    },
    alignment: {
      td: 'left',
      th: 'center'
    }
  },
  {
    field: 'isSegmented',
    title: 'JACKPOT.GRID.playersSegmentation',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: false,
    isEditable: true,
    td: {
      type: 'calc',
      titleFn: ( row: Jackpot ) => {
        return row.isSegmented ? 'COMMON.yes' : 'COMMON.no';
      },
      useTranslate: true
    },
    alignment: {
      th: 'center',
      td: 'center'
    },
    isFilterable: true,
    data: [{ id: 'true', title: 'COMMON.yes' }, { id: 'false', title: 'COMMON.no' }],
  },
  {
    field: 'numberOfGames',
    title: 'JACKPOT.GRID.games',
    type: 'string',
    isList: true,
    isListVisible: false,
    isFilterable: false,
    isEditable: true,
    td: {
      type: 'calc',
      titleFn: ( row: Jackpot ) => {
        return row.games.length.toString();
      },
      classFn: () => {
      },
      useTranslate: false
    },
    data: [],
    alignment: {
      td: 'right',
      th: 'right'
    }
  },
  {
    field: 'reports',
    title: 'JACKPOT.GRID.reports',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'report',
      svgIcon: 'icon_report',
      titleFn: () => 'Jackpot results',
      urlParamsFn: ( row: Jackpot ) => row && row.id ? `${REPORT_ID.jackpot}/${row.id}` : '',
      classFn: () => 'sw-color-blue',
      canActivateFn: () => true,
    },
    alignment: {
      th: 'center',
      td: 'center'
    },
  },
];

export const SCHEMA_LIST = SCHEMA.filter(( { isList } ) => isList);
export const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);
