<lib-swui-page-panel [title]="'JACKPOT.LIST.title'" [actions]="panelActions"></lib-swui-page-panel>

<div class="padding32 sw-grid-layout">
  <div class="sw-grid-layout__table">
    <lib-swui-schema-top-filter [schema]="filterSchema"></lib-swui-schema-top-filter>
    <lib-swui-grid #grid [schema]="schema" [rowActions]="rowActions" [rowActionsColumnTitle]="''"
                   [savedFilteredPageName]="'jackpots'"
                   [columnsManagement]="true" sortActive="created" sortDirection="desc">
    </lib-swui-grid>
  </div>
</div>
