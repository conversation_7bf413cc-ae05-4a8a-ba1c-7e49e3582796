import { Component, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  ActionConfirmDialogComponent,
  PanelAction,
  RowAction,
  SchemaTopFilterField,
  SelectInputOptionData, SwBrowserTitleService,
  SwHubAuthService,
  SwHubConfigService,
  SwuiGridComponent,
  SwuiGridDataService,
  SwuiGridField,
  SwuiNotificationsService,
  SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import { filter, finalize, map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { BaseComponent } from '../../../../common/components/base.component';
import { entitiesToSelectOptions, Entity, isBrandType, isResellerType } from '../../../../common/models/entity';
import { EntityService } from '../../../../common/services/entity.service';
import { GameService } from '../../../../common/services/game.service';
import { JackpotService } from '../../../../common/services/jackpot.service';
import { Jackpot } from '../../interfaces/jackpot';
import { SCHEMA_FILTER, SCHEMA_LIST } from '../schema';

@Component({
  selector: 'sw-jackpot-list',
  templateUrl: 'jackpot-list.component.html',
  styleUrls: ['jackpot-list.component.scss'],
  providers: [
    SwuiTopFilterDataService,
    { provide: SwuiGridDataService, useExisting: JackpotService }
  ]
})
export class JackpotListComponent extends BaseComponent {
  readonly schema = SCHEMA_LIST;
  readonly filterSchema: SchemaTopFilterField[];
  readonly panelActions: PanelAction[];
  readonly rowActions: RowAction[];

  allowDeleteRunningFeature = true;

  @ViewChild('grid') grid?: SwuiGridComponent<Jackpot>;
  private isSuperAdmin: boolean;
  private isBrand: boolean;
  private brief: Entity;

  constructor( private readonly service: JackpotService,
               private readonly router: Router,
               private readonly dialog: MatDialog,
               private readonly translate: TranslateService,
               private readonly notifications: SwuiNotificationsService,
               private readonly entityService: EntityService,
               private readonly gameService: GameService,
               { snapshot: { data: { brief } } }: ActivatedRoute,
               { isSuperAdmin }: SwHubAuthService,
               { envName }: SwHubConfigService,
               protected readonly browserTitleService: SwBrowserTitleService,
  ) {
    super(browserTitleService);
    this.isBrand = isBrandType(brief);
    this.panelActions = isBrandType(brief) ? [] : this.initPanelActions();
    this.rowActions = this.initRowActions();
    this.filterSchema = this.initFilterSchema(isResellerType(brief));
    this.isSuperAdmin = isSuperAdmin;
    this.allowDeleteRunningFeature = envName !== 'prod';
    this.brief = brief;
  }

  private isOwner( jackpot: Jackpot ): boolean {
    return this.isSuperAdmin || this.brief.id === jackpot.operators.owner?.id;
  }

  private initPanelActions(): PanelAction[] {
    return [
      {
        title: 'JACKPOT.LIST.newJackpot',
        icon: 'add',
        color: 'primary',
        actionUrl: '/pages/jackpot/create'
      }
    ];
  }

  private initRowActions(): RowAction[] {
    return [
      {
        title: 'COMMON.ACTIONS.viewEdit',
        fn: ( item: Jackpot ) => {
          this.router.navigate(['./pages/jackpot/edit', item.id]);
        },
        canActivateFn: () => true,
      },
      {
        title: 'COMMON.ACTIONS.duplicate',
        fn: ( item: Jackpot ) => {
          this.router.navigate(['./pages/jackpot/clone', item.id]);
        },
        canActivateFn: () => !this.isBrand,
      },
      {
        title: 'COMMON.ACTIONS.delete',
        fn: ( jackpot: Jackpot ) => {
          let deleteConfirmText = 'JACKPOT.NOTIFICATIONS.removeJackpot';
          let forceDelete = false;

          if (this.isSuperAdmin && jackpot.active === false) {
            deleteConfirmText = 'JACKPOT.NOTIFICATIONS.forceRemove';
            forceDelete = true;
          }

          this.dialog.open(ActionConfirmDialogComponent, {
            data: {
              action: {
                confirmText: this.translate.instant(deleteConfirmText, { name: jackpot.general.name }),
              }
            }
          }).afterClosed()
            .pipe(
              finalize(() => this.grid && this.grid.dataSource.loadData()),
              filter(( { confirmed } ) => confirmed),
              switchMap(() => forceDelete ? this.service.forceDelete(jackpot.id) : this.service.delete(jackpot.id)),
              tap(() => this.notifications.success(
                this.translate.instant('JACKPOT.NOTIFICATIONS.deleted', { name: jackpot.general.name }))
              ),
              takeUntil(this.destroyed)
            )
            .subscribe();
        },
        canActivateFn: ( item: Jackpot ) =>
          this.isOwner(item) && (
          (!item.active && !item.lastExecution && item.status === 'disabled') ||
          (!item.active && this.isSuperAdmin && this.allowDeleteRunningFeature)),
      }
    ];
  }

  private initFilterSchema( isEntity: boolean ): SwuiGridField[] {
    return this.initOperatorsFilterSchema(isEntity).map(item => {
      if (item.field === 'games') {
        (item as SelectInputOptionData).data = this.gameService.query();
      }
      return item;
    });
  }

  private initOperatorsFilterSchema( isEntity: boolean ): SwuiGridField[] {
    if (isEntity) {
      return SCHEMA_FILTER.map(item => {
        if (item.field === 'operators') {
          (item as SelectInputOptionData).data = this.entityService.structure$.pipe(
            map(entity => entity ? entitiesToSelectOptions(entity) : [])
          );
        }
        return item;
      });
    }
    return SCHEMA_FILTER.filter(( { field } ) => field !== 'operators');
  }
}
