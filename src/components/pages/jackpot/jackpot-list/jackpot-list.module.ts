import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { JackpotListComponent } from './jackpot-list.component';
import { SwuiGridModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';

@NgModule({
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule.forChild(),
    SwuiPagePanelModule,
    SwuiSchemaTopFilterModule,
    SwuiGridModule,
  ],
  declarations: [
    JackpotListComponent,
  ],
})
export class JackpotListModule {
}
