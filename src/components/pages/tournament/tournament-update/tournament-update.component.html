<lib-swui-page-panel [title]="topPanelTitle" [back]="true" backUrl="/pages/tournament"></lib-swui-page-panel>

<div class="tournament-edit">
  <mat-tab-group #tabs class="tournament-edit__main" [formGroup]="form" animationDuration="0ms">

    <mat-tab>
      <ng-template mat-tab-label>General</ng-template>
      <sw-tournament-general-form
        formControlName="general"
        [status]="tournament?.status">
      </sw-tournament-general-form>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>Schedule</ng-template>
      <sw-tournament-schedule-form
        formControlName="schedule"
        [isDuplicate]="isDuplicate"
        [status]="tournament.status">
      </sw-tournament-schedule-form>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>Ranking</ng-template>
      <sw-ranking [currencies]="currencies" formControlName="ranking"></sw-ranking>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>Payout</ng-template>
      <sw-tournament-payout-form formControlName="configuration"></sw-tournament-payout-form>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>Games</ng-template>
      <sw-games-form formControlName="games" [sourceGames]="sourceGames"></sw-games-form>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>Operators</ng-template>
      <sw-tournament-operators-form formControlName="operators"></sw-tournament-operators-form>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>Segmentation</ng-template>
      <sw-tournament-segmentation-from [currencies]="currencies"
                                       [canPlayersEdit]="canSave"
                                       [status]="tournament?.status"
                                       [initialCurrencies]="tournament?.segmentation?.currencies"
                                       [isDuplicate]="isDuplicate"
                                       formControlName="segmentation">
      </sw-tournament-segmentation-from>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>UI</ng-template>
      <sw-tournament-ui [formControl]="assetsControl"></sw-tournament-ui>
    </mat-tab>

    <mat-tab *ngIf="isSidebarHidden">
      <ng-template mat-tab-label>Info</ng-template>
      <div class="mat-card">
        <sw-tournament-info
          [tournament]="tournament"
          [schedule]="schedule$ | async"
          [id]="tournamentId"
          [disabled]="isTournamentInfoDisabled"
          (status)="onChangeStatus($event)">
        </sw-tournament-info>
      </div>
    </mat-tab>

  </mat-tab-group>

  <div class="tournament-edit__sidebar" *ngIf="!isSidebarHidden">
    <sw-tournament-info [tournament]="tournament"
                        [schedule]="schedule$ | async"
                        [id]="tournamentId"
                        [disabled]="isTournamentInfoDisabled"
                        (status)="onChangeStatus($event)">
    </sw-tournament-info>
    <div class="padding-top32" [ngClass]="{'sidebar-hidden': isSidebarHidden}">
      <div class="controls">
        <button mat-flat-button class="controls__button link" routerLink="/pages/tournament">
          {{ 'COMMON.ACTIONS.cancel' | translate }}
        </button>
        <button mat-flat-button
                class="controls__button"
                [disabled]="(tournament?.status === 'expired' && !isDuplicate) || isDisabled || !canSave"
                [color]="'primary'"
                (click)="onSubmit()">
          {{ 'COMMON.ACTIONS.save' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>

<div *ngIf="isSidebarHidden" class="footer" [ngClass]="{'sidebar-hidden': isSidebarHidden}">
  <div class="controls">
    <button mat-flat-button class="controls__button link" routerLink="/pages/tournament">
      {{ 'COMMON.ACTIONS.cancel' | translate }}
    </button>
    <button mat-flat-button
            class="controls__button"
            [disabled]="(tournament?.status === 'expired' && !isDuplicate) || isDisabled || !canSave"
            [color]="'primary'"
            (click)="onSubmit()">
      {{ 'COMMON.ACTIONS.save' | translate }}
    </button>
  </div>
</div>
