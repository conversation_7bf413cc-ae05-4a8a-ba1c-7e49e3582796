import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { GamesFormModule } from '../../../../common/components/games-form/games-form.module';
import { TournamentGeneralFormModule } from '../form/general/tournament-general-form.module';
import { TournamentOperatorsFormModule } from '../form/operators/tournament-operators-form.module';

import { TournamentPayoutFormModule } from '../form/payout/tournament-payout-form.module';
import { RankingModule } from '../form/ranking/ranking.module';
import { TournamentScheduleFormModule } from '../form/schedule/tournament-schedule-form.module';
import { TournamentSegmentationFormModule } from '../form/segmentation/tournament-segmentation-form.module';
import { TournamentUiModule } from '../form/ui/tournament-ui.module';
import { TournamentInfoModule } from './tournament-info/tournament-info.module';
import { TournamentUpdateComponent } from './tournament-update.component';

export const MODULES = [
  ReactiveFormsModule,
  MatTabsModule,
  MatIconModule,
  MatButtonModule,
  MatCardModule,
  FlexLayoutModule,
  TournamentGeneralFormModule,
  TournamentScheduleFormModule,
  TournamentPayoutFormModule,
  TournamentSegmentationFormModule,
  TournamentGeneralFormModule,
  TournamentInfoModule,
  TournamentOperatorsFormModule,
  TournamentUiModule,
  SwuiPagePanelModule,
  GamesFormModule,
  RankingModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    RouterModule,
    ...MODULES
  ],
  declarations: [
    TournamentUpdateComponent
  ],
})
export class TournamentUpdateModule {
}
