import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { SegmentationBrand } from '../../../interfaces/feature';

import { TournamentRange, TournamentRankingFormulaType } from '../../../interfaces/tournament';
import { FormSubmitted } from '../../../../../common/lib/form.submitted';

export interface FormTournamentRange {
  [key: string]: TournamentRange[];
}

export const prizeMultiplierSupportedTypes = new Set(['highestSingleBetWin', 'highestSingleWin']);

@Injectable()
export class TournamentFormService extends FormSubmitted {
  private _baseCurrency = new BehaviorSubject<string>('');
  private _ranges$ = new BehaviorSubject<FormTournamentRange | null>(null);
  private _baseCurrencyControlValue$ = new BehaviorSubject<TournamentRange[]>([]);
  private _rankingFormulaType$ = new BehaviorSubject<TournamentRankingFormulaType | undefined>(undefined);
  private _addRange$ = new Subject<boolean>();
  private _removeRange$ = new BehaviorSubject<number | null>(null);
  private _segmentationBrands$ = new BehaviorSubject<SegmentationBrand[]>([]);

  set segmentationBrands( val: SegmentationBrand[] ) {
    this._segmentationBrands$.next(val);
  }

  get segmentationBrands$(): Observable<SegmentationBrand[]> {
    return this._segmentationBrands$ as Observable<SegmentationBrand[]>;
  }

  set removeRange( val: number ) {
    this._removeRange$.next(val);
  }

  get removeRange$(): Observable<number> {
    return this._removeRange$ as Observable<number>;
  }

  set addRange( val: boolean ) {
    this._addRange$.next(val);
  }

  get addRange$(): Observable<boolean> {
    return this._addRange$ as Observable<boolean>;
  }

  set rankingFormulaType( val: TournamentRankingFormulaType | undefined ) {
    this._rankingFormulaType$.next(val);
  }

  get rankingFormulaType$(): Observable<TournamentRankingFormulaType> {
    return this._rankingFormulaType$ as Observable<TournamentRankingFormulaType>;
  }

  get isPrizeMultiplierSupported(): boolean {
    return this._rankingFormulaType$.value ? !!prizeMultiplierSupportedTypes.has(this._rankingFormulaType$.value) : false;
  }

  set baseCurrencyControlValue( val: TournamentRange[] ) {
    this._baseCurrencyControlValue$.next(val);
  }

  get baseCurrencyControlValue$(): Observable<TournamentRange[]> {
    return this._baseCurrencyControlValue$ as Observable<TournamentRange[]>;
  }

  set ranges( val: FormTournamentRange | null ) {
    this._ranges$.next(val);
  }

  get ranges$(): Observable<FormTournamentRange | null> {
    return this._ranges$ as Observable<FormTournamentRange>;
  }

  set baseCurrency( val: string ) {
    this._baseCurrency.next(val || '');
  }

  get baseCurrency$(): Observable<string> {
    return this._baseCurrency as Observable<string>;
  }
}
