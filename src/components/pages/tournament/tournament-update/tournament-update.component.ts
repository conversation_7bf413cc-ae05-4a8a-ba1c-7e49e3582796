import {
  ChangeDetectionStrategy, Component, forwardRef, HostListener, On<PERSON>estroy, OnInit, ViewChild
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { MatTabGroup } from '@angular/material/tabs';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  ActionConfirmDialogComponent, SwHubAuthService, SWUI_CONTROL_MESSAGES, SwuiIsControlInvalidService,
  SwuiNotificationsService
} from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { merge, Observable, ReplaySubject, Subscription, timer } from 'rxjs';
import { filter, finalize, map, switchMap, take, takeUntil } from 'rxjs/operators';
import { SwBrowserTitleService } from '@skywind-group/lib-swui';

import { BaseComponent } from '../../../../common/components/base.component';
import { CurrenciesManagerService } from '../../../../common/components/currencies-manager/currencies-manager.service';
import { SW_FORM_SERVICE } from '../../../../common/components/games-form/form-service.model';
import { MESSAGE_ERRORS } from '../../../../common/constants/errors-message-list';
import { formatDate } from '../../../../common/lib/format-date';
import { CurrencyModel } from '../../../../common/models/currency.model';
import { Entity, isBrandType } from '../../../../common/models/entity';
import { GameInfo } from '../../../../common/models/game';
import { TournamentService } from '../../../../common/services/tournament.service';
import { FeatureSchedule, OperatorsBrand, SegmentationBrand } from '../../interfaces/feature';
import { Tournament } from '../../interfaces/tournament';
import { TournamentFormService } from './tournament-form-service/tournament-form.service';

const MILLISECONDS_PER_DAY = 86400000;

@Component({
  selector: 'sw-tournament-update',
  templateUrl: './tournament-update.component.html',
  styleUrls: ['./tournament-update.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    CurrenciesManagerService,
    TournamentFormService,
    { provide: SW_FORM_SERVICE, useExisting: forwardRef(() => TournamentFormService) },
    { provide: ErrorStateMatcher, useExisting: forwardRef(() => TournamentFormService) },
    { provide: SwuiIsControlInvalidService, useExisting: forwardRef(() => TournamentFormService) },
    { provide: SWUI_CONTROL_MESSAGES, useValue: MESSAGE_ERRORS }
  ]
})
export class TournamentUpdateComponent extends BaseComponent implements OnInit, OnDestroy {
  readonly currencies: CurrencyModel[] = [];
  readonly brief: Entity;
  tournamentId?: string;
  tournament: Tournament;
  form: FormGroup = new FormGroup({});
  schedule$: Observable<FeatureSchedule | undefined>;
  baseCurrency?: string;
  isSidebarHidden = false;
  isDuplicate = false;
  isDisabled = false;
  isTournamentInfoDisabled = false;
  sourceGames: GameInfo[] = [];
  isOwner: boolean;
  isBrand: boolean;
  canSave: boolean;

  @ViewChild('tabs') tabs?: MatTabGroup;

  private status: 'enabled' | 'disabled' | null = null;
  private tournamentName = '';
  private tournament$ = new ReplaySubject<Tournament>(1);
  private statusUpdateSubscription = new Subscription();

  constructor( private readonly service: TournamentService,
               private readonly route: ActivatedRoute,
               private readonly fb: FormBuilder,
               private readonly formService: TournamentFormService,
               private readonly notifications: SwuiNotificationsService,
               private readonly translate: TranslateService,
               private readonly authService: SwHubAuthService,
               private readonly router: Router,
               private readonly dialog: MatDialog,
               private readonly currenciesManagerService: CurrenciesManagerService,
               protected readonly browserTitleService: SwBrowserTitleService,
  ) {
    super();
    this.hideSidebar();

    const { data: { games, currencies, tournament, duplicate, brief }, params: { id } } = route.snapshot;

    this.sourceGames = games;
    this.isDuplicate = duplicate;
    this.currencies = currencies;
    this.tournamentId = id;
    this.brief = brief;
    this.tournament = tournament || { type: 'tournament', status: 'disabled' };
    this.isOwner = !id || this.authService.isSuperAdmin || this.brief.id === tournament.operators.owner?.id;
    this.isBrand = isBrandType(brief);
    this.canSave = this.isOwner || this.isBrand || this.isDuplicate;
    this.form = this.initForm();

    this.tournament$
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe((t) => {
        this.initValues(t);
        this.browserTitleService.setupTitles('Engagement', t?.general?.name || '');
      });

    this.tournament$.next(tournament);

    this.schedule$ = merge(
      this.scheduleControl.valueChanges,
      this.tournament$.pipe(
        map(t => t ? t.schedule : null)
      )
    );

    if (duplicate) {
      this.tournamentId = undefined;
      this.tournament.activatedAt = undefined;
      this.tournament.modifiedBy = undefined;
      this.tournament.status = 'disabled';
      this.tournament.active = false;
      delete this.tournament.updated;
      delete this.tournament.created;
      delete this.tournament.lastExecution;
      delete this.tournament.nextExecution;
    }
  }

  @HostListener('window:resize') onResize() {
    this.hideSidebar();
  }

  ngOnInit(): void {
    this.generalControl.valueChanges.pipe(
      takeUntil(this.destroyed),
    ).subscribe(() => {
      const { general: { name } } = this.form.getRawValue();
      this.tournamentName = name || '';
    });

    this.segmentationControl.valueChanges.pipe(
      map(() => {
        const { segmentation: { brands }, operators } = this.form.getRawValue();
        return [brands, operators];
      }),
      filter(( [brands] ) => {
        return !!brands.length;
      }),
      takeUntil(this.destroyed),
    ).subscribe(( [brands, operators] ) => {
      const operatorsSet = new Set(operators.brands.map(( el: OperatorsBrand ) => el.id));
      (brands as SegmentationBrand[]).forEach(brand => {
        if (!operatorsSet.has(brand.id)) {
          operators.brands.push({ id: brand.id });
        }
      });
      this.operatorsControl.patchValue(operators, { emitEvent: false });
    });

    this.operatorsControl.valueChanges.pipe(
      map(() => {
        const { operators, segmentation } = this.form.getRawValue();
        const segmentationBrands = segmentation && segmentation.brands ? segmentation.brands.map(( el: SegmentationBrand ) => el.id) : [];
        const segmentationSet = new Set<string>(segmentationBrands);
        const operatorsBrands = operators && operators.brands ? operators.brands.map(( el: OperatorsBrand ) => el.id) : [];
        const operatorsSet = new Set<string>(operatorsBrands);
        return [segmentationSet, operatorsSet, operators, segmentation];
      }),
      filter(( [segmentationSet, operatorsSet] ) => {
        return !isEqualSets(segmentationSet, operatorsSet);
      }),
      takeUntil(this.destroyed)
    ).subscribe(( [segmentationSet, operatorsSet, operators, segmentation] ) => {
      segmentation.brands.forEach(( brand: SegmentationBrand, index: number ) => {
        if (!operatorsSet.has(brand.id)) {
          segmentation.brands.splice(index, 1);
        }
      });
      operators.brands.forEach(( brand: OperatorsBrand ) => {
        if (!segmentationSet.has(brand.id)) {
          segmentation.brands.push({ id: brand.id, players: [] });
        }
      });
      this.segmentationControl.patchValue(segmentation, { emitEvent: false });
    });

    this.currenciesManagerService.selectedPayoutCurrency$
      .pipe(
        filter(val => !!val),
        takeUntil(this.destroyed)
      )
      .subscribe(() => {
        if (this.tabs) {
          this.tabs.selectedIndex = 3;
        }
      });
  }

  ngOnDestroy() {
    super.ngOnDestroy();
    this.formService.ranges = null;
    this.formService.baseCurrency = '';
    this.formService.formSubmitted = false;
    this.currenciesManagerService.selectedCurrencies = [];
    this.formService.baseCurrencyControlValue = [];
    this.currenciesManagerService.selectedPayoutCurrency = undefined;
    this.formService.rankingFormulaType = undefined;
  }

  onSubmit() {
    // on disabled status form is not valid and not invalid
    if (this.form.valid || this.form.disabled) {
      if (!this.isDuplicate && this.tournament && this.tournament.status === 'running') {
        const operatorsWithAllPlayers = getOperatorsWithAllPlayers(this.form.getRawValue());
        const confirmText = !!operatorsWithAllPlayers.length ?
          this.translate.instant('TOURNAMENT.NOTIFICATIONS.makeChangesWillAffectAllPlayers',
            { brands: operatorsWithAllPlayers.join(', ') }) :
          this.translate.instant('TOURNAMENT.NOTIFICATIONS.makeChanges');

        this.dialog.open(ActionConfirmDialogComponent, {
          data: {
            action: {
              confirmText,
            }
          }
        }).afterClosed().subscribe(confirmed => {
            if (confirmed) {
              this.updateTournament();
            }
          }
        );
      } else {
        this.updateTournament();
      }
    } else {
      this.formService.formSubmitted = true;
      this.switchToFirstInvalidTab();
    }
  }

  updateTournament() {
    const {
      general,
      ranking,
      configuration,
      schedule,
      games,
      operators,
      segmentation,
      assets
    } = this.form.getRawValue();
    const data: Tournament = {
      status: 'disabled',
      ...this.tournament,
      general,
      ranking,
      configuration,
      schedule,
      games,
      operators,
      segmentation,
      assets,
      ...(this.status ? { status: this.status } : {})
    };

    if (this.tournament && this.tournament.status === 'running') {
      data.general = { ...this.tournament.general, ...general };
      data.configuration = JSON.parse(JSON.stringify(this.tournament.configuration));
    }

    if (this.tournament && this.tournament.status === 'scheduled') {
      data.configuration = JSON.parse(JSON.stringify(this.tournament.configuration));
    }

    if (this.tournament && this.isDuplicate && !this.authService.isSuperAdmin) {
      data.operators.owner = null;
    }

    const action = ( tournament: Tournament ): Observable<Tournament> => {
      if (this.tournamentId) {
        return this.service.update(this.tournamentId, tournament);
      }
      return this.service.create(tournament);
    };
    this.isDisabled = true;
    action(data).pipe(
      take(1),
      finalize(() => this.isDisabled = false)
    ).subscribe(( item: Tournament ) => {
      if (item) {
        let id = item.id;

        this.tournament$.next(item);

        if (!this.tournamentId) {
          this.router.navigate(['./pages/tournament/edit', id]);
        }

        const message = this.tournamentId ?
          this.translate.instant('TOURNAMENT.NOTIFICATIONS.updatedTournamentMessage', { name: data.general.name }) :
          this.translate.instant('TOURNAMENT.NOTIFICATIONS.createdTournamentMessage');
        this.notifications.success(message);

        this.isDisabled = true;

        if (this.tabs && !this.tournamentId) {
          this.tabs.selectedIndex = 0;
        }
      }
    });
  }

  onChangeStatus( status: 'enabled' | 'disabled' ) {
    this.status = status;

    this.tournamentId = this.route.snapshot.params.id;

    if (this.tournamentId) {
      this.service.get(this.tournamentId as string)
        .subscribe(tournament => {
          this.tournament$.next(tournament);
        });
    }
  }

  private hideSidebar() {
    this.isSidebarHidden = window.innerWidth <= 1300;
  }

  get topPanelTitle(): string {
    return this.tournamentId ? ('Edit ' + this.tournamentName) : 'Create Tournament ' + this.tournamentName;
  }

  get generalControl(): FormControl {
    return this.form.get('general') as FormControl;
  }

  private initForm(): FormGroup {
    return this.fb.group({
      general: ['', Validators.required],
      schedule: ['', Validators.required],
      ranking: ['', Validators.required],
      configuration: ['', Validators.required],
      games: ['', Validators.required],
      operators: ['', Validators.required],
      segmentation: ['', Validators.required],
      assets: []
    });
  }

  private initValues( tournament: Tournament ): void {
    this.tournament = tournament || { type: 'tournament', status: 'disabled' };

    this.tournamentName = tournament && tournament.general && tournament.general.name ? tournament.general.name : '';

    if (this.isDuplicate) {
      this.tournamentId = undefined;
      this.tournament.active = false;
      this.tournament.status = 'disabled';

      if (!this.authService.isSuperAdmin) {
        this.tournament.operators.owner = null;
      }

      delete this.tournament.id;
      delete this.tournament.created;
      delete this.tournament.updated;
      delete this.tournament.activated;
    }

    this.form.enable();
    this.form.patchValue(tournament || {});

    if (this.tournamentId &&
      !this.isDuplicate &&
      !this.authService.isSuperAdmin &&
      this.tournament.operators.owner !== null &&
      this.tournament.operators.owner.id !== this.brief.id) {
      this.isTournamentInfoDisabled = true;
      this.form.disable();
    }

    if (tournament && (tournament.status === 'scheduled' || tournament.status === 'running') && !this.isDuplicate) {
      this.scheduleControl.disable();
      this.configurationControl.disable();
      this.rankingControl.disable();
    }

    if (tournament && tournament.status === 'expired' && !this.isDuplicate) {
      this.form.disable();
    }

    if (tournament && tournament.status === 'running' && !this.isDuplicate && !tournament.nextExecution) {
      this.segmentationControl.disable();
    }

    this.listenStatus();
  }

  get scheduleControl(): FormControl {
    return this.form.get('schedule') as FormControl;
  }

  get configurationControl(): FormControl {
    return this.form.get('configuration') as FormControl;
  }

  get rankingControl(): FormControl {
    return this.form.get('ranking') as FormControl;
  }

  get segmentationControl(): FormControl {
    return this.form.get('segmentation') as FormControl;
  }

  get operatorsControl(): FormControl {
    return this.form.get('operators') as FormControl;
  }

  get assetsControl(): FormControl {
    return this.form.get('assets') as FormControl;
  }

  private listenStatus(): void {
    this.statusUpdateSubscription.unsubscribe();

    if (!this.tournament.active || !this.tournament.id) {
      return;
    }

    const nextUpdate = this.tournament.nextExecution
      ? formatDate(this.tournament.nextExecution, '', '', false, false)
      : (this.tournament.schedule as FeatureSchedule).nextCheck;

    if (!nextUpdate) {
      return;
    }

    const time = moment(nextUpdate).valueOf() - moment.utc().valueOf() + 10000;

    if (time < 0 || time > MILLISECONDS_PER_DAY) {
      return;
    }

    this.statusUpdateSubscription = timer(time)
      .pipe(
        switchMap(() => this.service.get(this.tournament.id as string))
      )
      .subscribe(tournament => {
        this.tournament$.next(tournament);
      });
  }

  private switchToFirstInvalidTab() {
    if (this.tabs) {
      const controls = Object.keys(this.form.controls);
      const firstInvalid = controls.find(key => this.form.controls[key].invalid);
      if (firstInvalid) {
        this.tabs.selectedIndex = controls.indexOf(firstInvalid);
      }
    }
  }
}

export function getOperatorsWithAllPlayers( feature: Tournament ): string[] {
  let operatorsBrands: OperatorsBrand[] = feature.operators?.brands || [];
  let segmentationBrands: SegmentationBrand[] = feature.segmentation?.brands || [];
  let operatorsWithAllPlayers: string[] = [];

  segmentationBrands?.forEach(( segmentationBrand: SegmentationBrand ) => {
    if (!segmentationBrand.players.length) {
      const operator = operatorsBrands.find(( brand: OperatorsBrand ) => brand.id === segmentationBrand.id);
      if (operator) {
        operatorsWithAllPlayers.push(operator.title || operator.id);
      }
    }
  });
  return operatorsWithAllPlayers;
}

function isEqualSets( as: Set<string>, bs: Set<string> ): boolean {
  if (as.size !== bs.size) {
    return false;
  }
  for (let a of as) {
    if (!bs.has(a)) {
      return false;
    }
  }
  return true;
}
