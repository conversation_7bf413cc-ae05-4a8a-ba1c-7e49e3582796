import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { SettingsService, SwHubConfigService, SwuiNotificationsModule } from '@skywind-group/lib-swui';

import { MockTournamentService, TournamentService } from '../../../../../common/services/tournament.service';
import { TournamentInfoComponent } from './tournament-info.component';
import { MODULES } from './tournament-info.module';
import { MatIconRegistry } from '@angular/material/icon';
import { FakeMatIconRegistry } from '@angular/material/icon/testing';
import { createComponentFactory, Spectator } from '@ngneat/spectator';

describe('TournamentInfoComponent', () => {
  let spectator: Spectator<TournamentInfoComponent>;
  const createComponent = createComponentFactory({
    component: TournamentInfoComponent,
    imports: [
      CommonModule,
      NoopAnimationsModule,
      HttpClientModule,
      RouterTestingModule,
      TranslateModule.forRoot(),
      SwuiNotificationsModule.forRoot(),
      ...MODULES,
    ],
    declarations: [TournamentInfoComponent],
    schemas: [NO_ERRORS_SCHEMA],
    providers: [
      { provide: TournamentService, useClass: MockTournamentService },
      SettingsService,
      { provide: SwHubConfigService, useValue: {} },
      { provide: MatIconRegistry, useClass: FakeMatIconRegistry }
    ]
  });

  beforeEach(() => {
    spectator = createComponent();
  });

  beforeEach(() => {
    spectator.component.schedule = {
      duration: 1,
      timeZone: 'Europe/Bucharest',
      repetitionMode: 2,
      triggerAt: [64980000]
    };
    spectator.component.count = 2;
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();
  });
});
