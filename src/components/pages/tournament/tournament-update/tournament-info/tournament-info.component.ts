import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSlideToggle, MatSlideToggleChange } from '@angular/material/slide-toggle';
import { TranslateService } from '@ngx-translate/core';
import {
  ActionConfirmDialogComponent,
  SettingsService,
  SwHubConfigService,
  SwuiNotificationsService
} from '@skywind-group/lib-swui';
import { AppSettings } from '@skywind-group/lib-swui/services/settings/app-settings';
import 'moment-timezone';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { map, take, takeUntil, tap } from 'rxjs/operators';
import { getTimeZones, REPORT_ID } from '../../../../../app.constants';
import { BaseInfoComponent } from '../../../../../common/components/base-info.component';
import { SchemaFieldMapItem } from '../../../../../common/models/schema-field-map-item';
import { TournamentService } from '../../../../../common/services/tournament.service';
import { FeatureSchedule, STATUS_MAP } from '../../../interfaces/feature';
import { getScheduleRuns, Tournament } from '../../../interfaces/tournament';
import { getOperatorsWithAllPlayers } from '../tournament-update.component';

@Component({
  selector: 'sw-tournament-info',
  templateUrl: './tournament-info.component.html',
})
export class TournamentInfoComponent extends BaseInfoComponent implements OnInit {

  @ViewChild('toggle') toggle: MatSlideToggle | undefined;

  @Output() status = new EventEmitter<'enabled' | 'disabled'>();

  @Input()
  set tournament( val: Tournament | undefined ) {
    if (!val) {
      return;
    }
    this._tournament = val;
    this.setTournamentStatus(this._tournament);
  }

  get tournament(): Tournament | undefined {
    return this._tournament;
  }

  @Input()
  set schedule( val: FeatureSchedule | undefined ) {
    if (!val) {
      return;
    }
    this.schedule$.next(val);
  }

  @Input() id?: string;

  @Input()
  set count( value: number ) {
    if (typeof value === 'undefined') {
      return;
    }
    this.count$.next(value);
  }

  @Input() disabled = false;

  readonly scheduleRuns$: Observable<string[]>;
  tournamentsReportUrl = '';
  analyticsUrl: string | undefined;

  tournamentStatus: SchemaFieldMapItem | undefined;

  private _tournament: Tournament | undefined;
  private _userTimeZone: string | undefined;

  private readonly schedule$ = new BehaviorSubject<FeatureSchedule | null>(null);
  private readonly count$ = new BehaviorSubject<number>(6);

  constructor( { hubs }: SwHubConfigService,
               private readonly service: TournamentService,
               private readonly dialog: MatDialog,
               private readonly notifications: SwuiNotificationsService,
               private readonly translate: TranslateService,
               protected readonly settingsService: SettingsService,
  ) {
    super(settingsService);
    this.scheduleRuns$ = combineLatest([this.schedule$, this.count$]).pipe(
      map(( [schedule, count] ) => schedule ? getScheduleRuns(schedule, count) : [])
    );
    if (hubs && hubs.analytics) {
      const { url } = hubs.analytics;
      this.analyticsUrl = url;
    }
  }

  ngOnInit(): void {
    this.initDates(this.tournament);
    this.tournamentsReportUrl = `${this.analyticsUrl}/${REPORT_ID.tournament}/${this.tournament?.id}`;

    this.settingsService.settings.pipe(
      tap(( data: AppSettings ) => this._userTimeZone = data.timezoneName),
      takeUntil(this.destroyed)
    ).subscribe();
  }

  get disableToggle() {
    if (this.tournament) {
      if (!!this.tournament.id && this.tournament.status !== 'expired' && !this.disabled) {
        return false;
      }
    }
    return true;
  }

  get timeZone(): string | undefined {
    const value = this.schedule$.value;
    return value ? value.timeZone : undefined;
  }

  get userTimeZone(): string | undefined {
    return this._userTimeZone;
  }

  triggerActive( event: MatSlideToggleChange ) {
    if (this.id) {
      const id = this.id;

      if (this.tournament) {
        const operatorsWithAllPlayers = getOperatorsWithAllPlayers(this.tournament);
        const confirmText = !!operatorsWithAllPlayers.length ?
          this.translate.instant('TOURNAMENT.NOTIFICATIONS.makeChangesWillAffectAllPlayers',
            { brands: operatorsWithAllPlayers.join(', ') }) :
          this.translate.instant('TOURNAMENT.NOTIFICATIONS.makeChanges');

        this.dialog.open(ActionConfirmDialogComponent, {
          data: {
            action: {
              confirmText,
            },
            closeButtonText: 'DIALOG.no',
            confirmButtonText: 'DIALOG.yes',
          }
        }).afterClosed()
          .pipe(take(1))
          .subscribe(confirmed => {
              if (confirmed) {
                this.updateTournamentStatus(id, event.checked);
              } else {
                if (this.toggle) {
                  this.toggle.checked = !this.toggle.checked;
                }
              }
            }
          );
      }
    }
  }

  updateTournamentStatus( id: string, isActive: boolean ) {
    this.service.patch(id, { active: isActive })
      .pipe(take(1))
      .subscribe(( tournament: Tournament ) => {
          if (this.tournament) {
            this.setTournamentStatus(tournament);
          }

          this.notifications.success(
            this.translate.instant('TOURNAMENT.NOTIFICATIONS.statusChangedMessage'),
          );

          const status = this.toggle && this.toggle.checked
            ? 'enabled'
            : 'disabled';
          this.status.emit(status);
        },
        () => {
          if (this.toggle) {
            this.toggle.checked = !isActive;
          }
        });
  }

  getTimeZoneTitle( id: string ): string | undefined {
    return getTimeZones()?.find(item => item.id === id)?.text;
  }

  private setTournamentStatus( val: Tournament ) {
    this.tournamentStatus = STATUS_MAP.find(( item: SchemaFieldMapItem ) => item.id === val.status);
  }
}
