import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { PipesModule } from '../../../../../common/pipes/pipes.module';

import { TournamentInfoComponent } from './tournament-info.component';


export const MODULES = [
  FlexLayoutModule,
  MatIconModule,
  MatSlideToggleModule,
  MatListModule,
  MatDialogModule,
  PipesModule,
];

@NgModule({
  declarations: [TournamentInfoComponent],
  imports: [
    CommonModule,
    TranslateModule,
    ...MODULES,
  ],
  exports: [TournamentInfoComponent],
})
export class TournamentInfoModule {
}
