import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import {
  SettingsService, SwBrowserTitleService, SwDexieService,
  SwHubAuthService,
  SwHubConfigService,
  SwuiGridModule,
  SwuiNotificationsModule
} from '@skywind-group/lib-swui';

import { CurrencyService, MockCurrencyService } from '../../../../common/services/currency.service';
import { EntityService, MockEntityService } from '../../../../common/services/entity.service';
import { GameService, MockGameService } from '../../../../common/services/game.service';
import { MockTournamentService, TournamentService } from '../../../../common/services/tournament.service';
import { TournamentListComponent } from './tournament-list.component';
import { MODULES } from './tournament-list.module';
import { createComponentFactory, Spectator } from '@ngneat/spectator';


describe('TournamentListComponent', () => {
  let spectator: Spectator<TournamentListComponent>;
  const createComponent = createComponentFactory({
    component: TournamentListComponent,
    imports: [
      NoopAnimationsModule,
      HttpClientTestingModule,
      RouterTestingModule,
      TranslateModule.forRoot(),
      SwuiGridModule.forRoot(),
      SwuiNotificationsModule.forRoot(),
      ...MODULES
    ],
    providers: [
      SettingsService,
      SwHubAuthService,
      { provide: SwBrowserTitleService, useClass: SwBrowserTitleService },
        { provide: CurrencyService, useClass: MockCurrencyService },
      { provide: TournamentService, useClass: MockTournamentService },
      { provide: EntityService, useClass: MockEntityService },
      { provide: GameService, useClass: MockGameService },
      { provide: SwHubConfigService, useValue: {} },
      {
        provide: SwDexieService, useValue: {
          getFilterState() {
            return Promise.resolve({});
          }
        }
      }
    ],
    schemas: [NO_ERRORS_SCHEMA],
  });

  beforeEach(() => {
    spectator = createComponent();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();
  });
});
