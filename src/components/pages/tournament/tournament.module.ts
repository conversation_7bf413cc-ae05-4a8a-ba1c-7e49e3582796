import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TournamentService } from '../../../common/services/tournament.service';
import { TournamentListModule } from './tournament-list/tournament-list.module';
import { TournamentRoutingModule } from './tournament-routing.module';
import { TournamentUpdateModule } from './tournament-update/tournament-update.module';
import { TournamentComponent } from './tournament.component';

@NgModule({
  imports: [
    CommonModule,
    TournamentRoutingModule,
    TournamentListModule,
    TournamentUpdateModule,
  ],
  providers: [
    TournamentService,
  ],
  declarations: [
    TournamentComponent,
  ],
})
export class TournamentModule {
}
