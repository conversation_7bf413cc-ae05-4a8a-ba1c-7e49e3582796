import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PERMISSIONS_NAMES } from '@skywind-group/lib-swui';
import { CurrenciesResolver } from '../../../common/services/currency.service';
import { GamesResolver } from '../../../common/services/game.service';
import { BriefResolver } from '../../../common/services/resolvers/brief.resolver';
import { TournamentResolver } from '../../../common/services/resolvers/tournament.resolver';
import { TournamentListComponent } from './tournament-list/tournament-list.component';
import { TournamentUpdateComponent } from './tournament-update/tournament-update.component';

import { TournamentComponent } from './tournament.component';

export const routes: Routes = [
  {
    path: '',
    component: TournamentComponent,
    children: [
      {
        path: '',
        component: TournamentListComponent,
        resolve: {
          brief: BriefResolver,
        },
        data: {
          permissions: [PERMISSIONS_NAMES.GRANTED_ALL],
          title: 'Tournaments'
        },
      },
      {
        path: 'create',
        component: TournamentUpdateComponent,
        resolve: {
          currencies: CurrenciesResolver,
          games: GamesResolver,
        },
        data: {
          permissions: [PERMISSIONS_NAMES.GRANTED_ALL],
          title: 'Tournaments - Create'
        },
      },
      {
        path: 'clone/:id',
        component: TournamentUpdateComponent,
        resolve: {
          currencies: CurrenciesResolver,
          tournament: TournamentResolver,
          brief: BriefResolver,
          games: GamesResolver,
        },
        data: {
          duplicate: true,
          permissions: [PERMISSIONS_NAMES.GRANTED_ALL],
          title: 'Tournaments - Clone'
        },
      },
      {
        path: 'edit/:id',
        component: TournamentUpdateComponent,
        resolve: {
          currencies: CurrenciesResolver,
          tournament: TournamentResolver,
          brief: BriefResolver,
          games: GamesResolver,
        },
        data: {
          permissions: [PERMISSIONS_NAMES.GRANTED_ALL],
          title: 'Tournaments - Edit'
        },
      },
    ]
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: [
    BriefResolver,
    TournamentResolver,
    CurrenciesResolver,
    GamesResolver
  ],
})
export class TournamentRoutingModule {
}
