import { Component, forwardRef, OnInit } from '@angular/core';
import {
  ControlValueAccessor,
  FormArray,
  FormBuilder,
  FormGroup,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ValidationErrors,
  Validator,
} from '@angular/forms';
import { combineLatest } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { BaseComponent } from '../../../../../common/components/base.component';
import { TournamentConfiguration } from '../../../interfaces/tournament';
import {
  FormTournamentRange,
  TournamentFormService
} from '../../tournament-update/tournament-form-service/tournament-form.service';


@Component({
  selector: 'sw-tournament-payout-form',
  templateUrl: './tournament-payout-form.component.html',
  styleUrls: ['./tournament-payout-form.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TournamentPayoutFormComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => TournamentPayoutFormComponent),
      multi: true
    }
  ]
})
export class TournamentPayoutFormComponent extends BaseComponent implements ControlValueAccessor, Validator, OnInit {
  form: FormGroup;
  baseCurrency?: string;
  ranges: FormTournamentRange | null = null;
  onChange: ( _: any ) => void = (() => {
    // This is intentional
  });

  constructor( private readonly fb: FormBuilder,
               private readonly formService: TournamentFormService
  ) {
    super();
    this.form = this.initForm();
  }

  ngOnInit() {
    combineLatest([this.formService.baseCurrency$, this.formService.ranges$])
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( [baseCurrency, ranges] ) => {
        this.baseCurrency = baseCurrency;
        if (ranges && this.baseCurrency) {
          this.ranges = ranges;
        } else {
          this.ranges = null;
        }
      });

    this.form.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: TournamentConfiguration ) => {
        this.onChange(val);
      });
  }

  onTouched: () => void = () => {
    // This is intentional
  };

  writeValue( val: TournamentConfiguration ): void {
    this.form.patchValue(val);
  }

  registerOnChange( fn: any ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState?( isDisabled: boolean ): void {
    isDisabled ? this.form.disable() : this.form.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid ? null : { invalidForm: { valid: false } };
  }

  get payouts(): FormArray {
    return this.form.get('payouts') as FormArray;
  }

  get luckyDraw(): FormArray {
    return this.form.get('luckyDraw') as FormArray;
  }

  private initForm(): FormGroup {
    return this.fb.group({
      payouts: [],
      luckyDraw: [{ enabled: false, players: null, prizeType: null, payoutsPerRange: [] }],
    });
  }
}
