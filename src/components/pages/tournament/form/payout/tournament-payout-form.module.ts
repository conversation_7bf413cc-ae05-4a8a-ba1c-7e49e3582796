import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';
import {
  SwuiControlMessagesModule,
  SwuiCurrencySymbolModule,
  SwuiIsControlInvalidModule
} from '@skywind-group/lib-swui';
import { TotalPayoutsModule } from '../../../../../common/components/total-payouts/total-payouts.module';
import { FloatFormatterModule } from '../../../../../common/directives/floatFormatter/floatFormatter.module';
import { IntFormatterModule } from '../../../../../common/directives/integerFormatter/intFormatter.module';
import { TournamentPayoutFormLuckyComponent } from './tournament-payout-form-lucky/tournament-payout-form-lucky.component';

import { TournamentPayoutFormRanksComponent } from './tournament-payout-form-ranks/tournament-payout-form-ranks.component';
import { TournamentPayoutFormComponent } from './tournament-payout-form.component';
import { TournamentPayoutTotalsModule } from './tournament-payout-totals/tournament-payout-totals.module';
import { SettingCurrencyFormatterModule } from '../../../../../common/directives/appSettingCurrencyFormatter/settingCurrencyFormatter.module';


export const PAYOUT_FORM_MODULES = [
  MatFormFieldModule,
  MatInputModule,
  MatIconModule,
  MatButtonModule,
  MatCheckboxModule,
  MatTabsModule,
  FlexLayoutModule,
  ReactiveFormsModule,
  SwuiCurrencySymbolModule,
  MatSelectModule,
  SwuiIsControlInvalidModule,
  SwuiControlMessagesModule,
  FloatFormatterModule,
  IntFormatterModule,
  TotalPayoutsModule,
  TournamentPayoutTotalsModule
];

@NgModule({
  declarations: [
    TournamentPayoutFormComponent,
    TournamentPayoutFormRanksComponent,
    TournamentPayoutFormLuckyComponent,
  ],
    imports: [
        CommonModule,
        TranslateModule.forChild(),
        ...PAYOUT_FORM_MODULES,
        SettingCurrencyFormatterModule,
    ],
  exports: [TournamentPayoutFormComponent],
})
export class TournamentPayoutFormModule {
}
