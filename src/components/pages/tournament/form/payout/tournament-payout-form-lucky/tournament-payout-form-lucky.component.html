<form [formGroup]="form">
  <mat-checkbox formControlName="enabled">Lucky Draw</mat-checkbox>

  <ng-container *ngIf="!!enabled.value">
    <div class="error-message margin-top16" *swIsControlInvalid="form">
      <strong class="error-message__heading">Following fields has errors:</strong>

      <ul class="error-message__list">

        <li *swIsControlInvalid="players">
          "Players" :
          <lib-swui-control-messages [control]="players"></lib-swui-control-messages>
        </li>

        <li *swIsControlInvalid="prizeTypeControl">
          "Prize type" :
          <lib-swui-control-messages [control]="prizeTypeControl"></lib-swui-control-messages>
        </li>

        <li *swIsControlInvalid="payoutsPerRange">
          "Payout per range" :
          <ul>
            <ng-container *ngFor="let item of payoutsPerRange.controls; let index = index">
              <li *swIsControlInvalid="item">
                <strong>Payout #{{index + 1}}:</strong>
                <lib-swui-control-messages [control]="item"></lib-swui-control-messages>
              </li>
            </ng-container>
          </ul>
        </li>

      </ul>

    </div>
  </ng-container>

  <div class="scroll">
    <table class="ranks-table sw-mat-table" *ngIf="!!enabled.value">
      <tbody>
      <tr>
        <td class="cell-players">
          <mat-form-field appearance="outline" class="no-field-padding">
            <input type="number"
                   min="0"
                   placeholder="0"
                   matInput
                   formControlName="players"
                   swIntFormatter>
          </mat-form-field>
        </td>
        <td class="cell-type">
          <mat-form-field appearance="outline" class="no-field-padding">
            <mat-select formControlName="prizeType">
              <mat-option
                *ngFor="let item of prizeTypes"
                [value]="item.id"
                [disabled]="item.disabled">
                {{item.text}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </td>
        <ng-container formArrayName="payoutsPerRange">
          <td class="cell-payout" *ngFor="let range of payoutsPerRange.controls; let colIndex = index">
            <div class="cell-wrap" [ngSwitch]="form.get('prizeType')?.value">
              <mat-form-field appearance="outline" class="no-field-padding" *ngSwitchDefault>
                <span matPrefix class="cell-prefix">{{currency | currencySymbol}}</span>
                <input type="number"
                       min="0"
                       placeholder="0"
                       matInput
                       [formControlName]="colIndex"
                       swCurrencyFormatter [currencyCode]="currency">
              </mat-form-field>

              <mat-form-field appearance="outline" class="no-field-padding" *ngSwitchCase="'text'">
                <input type="text"
                       matInput
                       [formControlName]="colIndex">
              </mat-form-field>
            </div>
          </td>
        </ng-container>
      </tr>
      </tbody>
    </table>
  </div>

</form>
