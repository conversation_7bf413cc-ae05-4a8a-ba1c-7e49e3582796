import { TournamentFormService } from '../../../tournament-update/tournament-form-service/tournament-form.service';
import { PAYOUT_FORM_MODULES } from '../tournament-payout-form.module';
import { TournamentPayoutFormLuckyComponent } from './tournament-payout-form-lucky.component';
import { createHostFactory, createServiceFactory, SpectatorHost, SpectatorService } from '@ngneat/spectator';
import { FormControl } from '@angular/forms';
import { TournamentLuckyDraw } from '../../../../interfaces/tournament';
import { Component } from '@angular/core';


@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}

describe('TournamentPayoutFormLuckyComponent', () => {
  let spectator: SpectatorHost<TournamentPayoutFormLuckyComponent, CustomHostComponent>;
  const createHost = createHostFactory({
    component: TournamentPayoutFormLuckyComponent,
    host: CustomHostComponent,
    imports: [
      ...PAYOUT_FORM_MODULES,
    ],
    providers: [
      TournamentFormService
    ]
  });

  let spectatorService: SpectatorService<TournamentFormService>;
  const createService = createServiceFactory({
    service: TournamentFormService
  });

  const value: TournamentLuckyDraw = {
    enabled: false,
    players: 1,
    prizeType: 'fixed',
    payoutsPerRange: [1, 2]
  };
  const ranges = {
    'CNY': [
      {
        min: 0,
        max: 10,
        minQualifyAmount: 10
      },
      {
        min: 1,
        max: 11,
        minQualifyAmount: 11
      }
    ]
  };

  beforeEach(() => {
    spectator = createHost(`<sw-hub-tournament-payout-form-lucky [formControl]="form"></sw-hub-tournament-payout-form-lucky>`);
    spectatorService = createService();
  });

  afterEach(() => {
    spectator.component.ngOnDestroy();
    spectator.fixture.destroy();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();

    expect(spectatorService.service).toBeTruthy();
    expect(spectatorService.service).toBeDefined();
  });

  it('luckyDraw controls enabled when enabled true', () => {
    const testValue = Object.assign({}, value);
    testValue.enabled = true;

    spectator.hostComponent.form.patchValue(testValue);

    expect(spectator.component.prizeTypeControl.enabled).toBeTrue();
    expect(spectator.component.players.enabled).toBeTrue();
    expect(spectator.component.payoutsPerRange.enabled).toBeTrue();
  });

  it('luckyDraw controls disabled when enabled false', () => {
    spectator.hostComponent.form.patchValue(value);

    expect(spectator.component.prizeTypeControl.disabled).toBeTrue();
    expect(spectator.component.players.disabled).toBeTrue();
    expect(spectator.component.payoutsPerRange.disabled).toBeTrue();
  });

  it('form set disabled state', () => {
    spectator.component.setDisabledState(true);
    expect(spectator.component.form.disabled).toBeTrue();
  });

  it('form set payoutsPerRange with currency', () => {
    spectator.component.currency = 'CNY';
    spectator.component.ranges = ranges;
    spectator.hostComponent.form.patchValue(value);
    spectator.component.ngOnInit();

    expect(spectator.component.payoutsPerRange.value).toEqual([1, 2]);
  });

  it('form set payoutsPerRange with unset currency', () => {
    spectator.hostComponent.form.patchValue(value);
    spectator.component.ranges = ranges;

    expect(spectator.component.payoutsPerRange.value).toEqual([]);
  });

  it('form set payoutsPerRange with unset ranges', () => {
    spectator.hostComponent.form.patchValue(value);
    spectator.component.ranges = null;
    spectator.component.ngOnInit();

    expect(spectator.component.payoutsPerRange.value).toEqual([1]);
  });
});
