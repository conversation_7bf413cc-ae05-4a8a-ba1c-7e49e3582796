import { Component, forwardRef, Input, OnInit } from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ValidationErrors,
  Validator,
  Validators
} from '@angular/forms';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { delay, filter, takeUntil, tap } from 'rxjs/operators';

import { BaseComponent } from '../../../../../../common/components/base.component';
import {
  digitsOnlyValidator,
  fractionsNumbersLengthValidator,
  numberMaxLength,
  numbersOnlyValidator
} from '../../../../../../common/lib/validators';
import { TournamentLuckyDraw } from '../../../../interfaces/tournament';
import { FormTournamentRange } from '../../../tournament-update/tournament-form-service/tournament-form.service';

@Component({
  selector: 'sw-hub-tournament-payout-form-lucky',
  templateUrl: './tournament-payout-form-lucky.component.html',
  styleUrls: ['./tournament-payout-form-lucky.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TournamentPayoutFormLuckyComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => TournamentPayoutFormLuckyComponent),
      multi: true
    }
  ],
})
export class TournamentPayoutFormLuckyComponent extends BaseComponent
  implements ControlValueAccessor, Validator, OnInit {

  @Input() currency?: string;

  @Input()
  get ranges(): FormTournamentRange | null {
    return this._ranges$.value;
  }

  set ranges( value: FormTournamentRange | null ) {
    this._ranges$.next(value || null);
  }

  form: FormGroup;
  prizeTypes: SwuiSelectOption[] = [
    { id: 'fixed', text: 'Fixed' },
    { id: 'text', text: 'Text' }
  ];
  onChange: ( _: any ) => void = (() => {
  });

  private _ranges$ = new BehaviorSubject<FormTournamentRange | null>(null);
  private _luckyDraw$ = new BehaviorSubject<TournamentLuckyDraw | null>(null);
  private luckyDraw: TournamentLuckyDraw | null = null;
  private readonly maxNumberInputLength = 12;

  constructor( private fb: FormBuilder ) {
    super();
    this.form = this.initForm();
  }

  ngOnInit(): void {
    this._luckyDraw$
      .pipe(
        filter(val => !!val),
        takeUntil(this.destroyed)
      )
      .subscribe(( val: TournamentLuckyDraw | null ) => {
        this.luckyDraw = val;
        if (val) {
          this.form.patchValue(val);
        }
      });

    combineLatest([this._ranges$, this._luckyDraw$])
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( [ranges] ) => {
        this.payoutsPerRange.clear();
        if (ranges) {
          if (this.currency) {
            const currencyRanges = ranges[this.currency];
            if (currencyRanges?.length) {
              currencyRanges.forEach(( _, index ) => {
                const control = this.fb.control({
                  value: this.luckyDraw?.payoutsPerRange[index] || null,
                  disabled: this.form.disabled
                });
                this.payoutsPerRange.push(control);
              });
            }
          }
        } else {
          const control = this.fb.control({
            value: this.luckyDraw?.payoutsPerRange[0] || null,
            disabled: this.form.disabled
          });
          this.payoutsPerRange.push(control);
        }
      });

    this.enabled.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: boolean ) => {
        this.form.markAsUntouched();
        if (val) {
          this.prizeTypeControl.enable();
          this.players.enable();
          this.payoutsPerRange.enable();
        } else {
          this.prizeTypeControl.disable();
          this.players.disable();
          this.payoutsPerRange.disable();
        }
      });

    this.form.valueChanges
      .pipe(
        tap(() => {
          this.triggerPayoutPerRangeValidators();
        }),
        delay(0),
        takeUntil(this.destroyed)
      ).subscribe(() => {
      const val = this.form.getRawValue();
      if (!val.enabled) {
        val.players = null;
        val.prizeType = null;
        val.payoutsPerRange = [];
      } else {
        if (val.prizeType === 'fixed') {
          val.payoutsPerRange = val.payoutsPerRange.map(( el: any ) => el !== null ? parseFloat(el) : el);
        } else if (val.prizeType === 'text') {
          val.payoutsPerRange = val.payoutsPerRange.map(( el: any ) => el !== null ? el.toString() : el);
        }
      }

      this.luckyDraw = val;
      this.onChange(val);
    });
  }

  onTouched: any = () => {
  };

  writeValue( value: TournamentLuckyDraw ) {
    this._luckyDraw$.next(value);
  }

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( disabled: boolean ) {
    disabled ? this.form.disable() : this.form.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid || !this.enabled?.value ? null : { invalidForm: { valid: false } };
  }

  get payoutsPerRange(): FormArray {
    return this.form.get('payoutsPerRange') as FormArray;
  }

  get prizeTypeControl(): FormControl {
    return this.form.get('prizeType') as FormControl;
  }

  get enabled(): FormControl {
    return this.form.get('enabled') as FormControl;
  }

  get players(): FormControl {
    return this.form.get('players') as FormControl;
  }

  private initForm(): FormGroup {
    return this.fb.group({
      enabled: [false],
      players: [null, Validators.compose([
        Validators.required,
        digitsOnlyValidator,
        numberMaxLength(this.maxNumberInputLength),
      ])],
      prizeType: [null, Validators.required],
      payoutsPerRange: this.fb.array([
        this.fb.control(null, Validators.compose([
          Validators.required,
          numbersOnlyValidator,
          fractionsNumbersLengthValidator,
          numberMaxLength(this.maxNumberInputLength),
        ]))
      ])
    });
  }

  private triggerPayoutPerRangeValidators() {
    this.payoutsPerRange.controls.forEach(( payout: AbstractControl ) => {
      if (this.prizeTypeControl.value === 'text') {
        payout.setValidators(Validators.compose([
          Validators.required,
          Validators.maxLength(50)
        ]));
      } else {
        payout.setValidators(Validators.compose([
          Validators.required,
          numbersOnlyValidator,
          fractionsNumbersLengthValidator,
          numberMaxLength(this.maxNumberInputLength),
        ]));
      }
    });
  }
}
