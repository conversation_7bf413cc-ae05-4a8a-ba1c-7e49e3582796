.ranks-table {
  th {
    text-align: left !important;
  }
  .cell-action {
    width: 40px;
  }
  .cell-type {
    width: 166px;
    text-align: left !important;
    .mat-form-field {
      width: 150px;
      text-align: left;
    }
  }
  .cell-players {
    width: 85px;
    .mat-form-field {
      width: 85px;
    }
  }
  .cell-payout {
    position: relative;
    padding-left: 36px;
    .mat-form-field {
      width: 122px;
      text-align: left;
    }
    .cell-prefix {
      padding-right: 6px;
      &--x {
        position: relative;
        top: -2px;
        font-size: 10px;
      }
    }
  }
  .cell-currency {
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 36px;
    height: 65px;
    padding-right: 3px;
  }
  .cell-wrap {
    display: flex;
    align-items: center;
  }
}

.button-add {
  margin: 8px 0;
}
