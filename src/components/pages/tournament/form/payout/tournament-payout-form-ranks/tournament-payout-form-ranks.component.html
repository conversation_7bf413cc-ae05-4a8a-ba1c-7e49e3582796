<div class="error-message margin-top16" *swIsControlInvalid="payoutsFormArray">
  <strong class="error-message__heading">Following fields has errors:</strong>
  <ul *ngFor="let payout of payoutsFormArray.controls; let index = index;" class="error-message__list">
    <li *swIsControlInvalid="payout">
      <strong class="error-message__heading">Row #{{index + 1}}:</strong>

      <ul class="error-message__list error-message__list--nested">

        <li *swIsControlInvalid="getPlayers(payout)">
          "Players" :
          <lib-swui-control-messages [control]="getPlayers(payout)"></lib-swui-control-messages>
        </li>

        <li *swIsControlInvalid="getPrizeType(payout)">
          "Prize type" :
          <lib-swui-control-messages [control]="getPrizeType(payout)"></lib-swui-control-messages>
        </li>

        <li *swIsControlInvalid="getPayoutsPerRange(payout)">
          "Payout per range" :
          <ul *ngFor="let item of getPayoutsPerRange(payout).controls; let j = index">
            <li *swIsControlInvalid="item">
              <strong>Payout #{{j + 1}}:</strong>
              <lib-swui-control-messages [control]="item"></lib-swui-control-messages>
            </li>
          </ul>
        </li>

      </ul>

    </li>
  </ul>
</div>

<form [formGroup]="form" class="scroll">
  <table class="ranks-table sw-mat-table">
    <thead>
    <tr>
      <th class="cell-players"># of players</th>
      <th class="cell-type">Prize types</th>
      <th class="cell-payout" *ngFor="let range of columnsRanges">
        {{range}}
      </th>
      <th class="cell-action" *ngIf="payoutsFormArray.controls.length > 1"></th>
    </tr>
    </thead>
    <tbody>
    <ng-container formArrayName="payouts">
      <tr [formGroupName]="rowIndex" *ngFor="let payout of payoutsFormArray.controls; let rowIndex = index">
        <td class="cell-players">
          <mat-form-field appearance="outline" class="no-field-padding">
            <input type="number"
                   min="0"
                   placeholder="0"
                   matInput
                   formControlName="players">
          </mat-form-field>
        </td>
        <td class="cell-type">
          <mat-form-field appearance="outline" class="no-field-padding">
            <mat-select formControlName="prizeType">
              <mat-option
                *ngFor="let item of prizeTypes"
                [value]="item.id"
                [disabled]="item.disabled">
                {{item.text}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </td>
        <ng-container formArrayName="payoutsPerRange">
          <td class="cell-payout" *ngFor="let range of getPayoutsPerRange(payout).controls; let colIndex = index">
            <div class="cell-wrap" [ngSwitch]="payout.get('prizeType')?.value">
              <mat-form-field appearance="outline" class="no-field-padding" *ngSwitchDefault>
                <span matPrefix class="cell-prefix">{{currency | currencySymbol}}</span>
                <input type="number"
                       min="0"
                       placeholder="0"
                       matInput
                       [formControlName]="colIndex"
                       swCurrencyFormatter [currencyCode]="currency">
              </mat-form-field>

              <mat-form-field appearance="outline" class="no-field-padding" *ngSwitchCase="'multiplier'">
                <span matPrefix class="cell-prefix cell-prefix--x">✕</span>
                <input type="number"
                       min="0"
                       placeholder="0"
                       matInput
                       [formControlName]="colIndex">
              </mat-form-field>

              <mat-form-field appearance="outline" class="no-field-padding" *ngSwitchCase="'text'">
                <input type="text"
                       matInput
                       [formControlName]="colIndex">
              </mat-form-field>
            </div>
          </td>
        </ng-container>
        <td class="cell-action" *ngIf="payoutsFormArray.controls.length > 1">
          <button mat-icon-button (click)="removePayoutItem(rowIndex)" [disabled]="isDisabled">
            <mat-icon>clear</mat-icon>
          </button>
        </td>
      </tr>
    </ng-container>
    </tbody>
  </table>
</form>
<button
  class="mat-button-md button-add"
  mat-button [color]="'primary'"
  (click)="addPayoutItem()"
  [disabled]="isDisabled">
  <mat-icon>add</mat-icon>
  Add payout
</button>
