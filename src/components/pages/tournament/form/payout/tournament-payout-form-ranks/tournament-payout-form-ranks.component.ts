import { getCurrencySymbol } from '@angular/common';
import { Component, forwardRef, Input, OnInit } from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ValidationErrors,
  Validator,
  Validators
} from '@angular/forms';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { BaseComponent } from '../../../../../../common/components/base.component';
import {
  digitsOnlyValidator,
  fractionsNumbersLengthValidator,
  numberMaxLength,
  numbersOnlyValidator
} from '../../../../../../common/lib/validators';
import { FeaturePayout } from '../../../../interfaces/feature';
import { TournamentPayoutPrizeType, TournamentRange } from '../../../../interfaces/tournament';
import {
  FormTournamentRange,
  TournamentFormService
} from '../../../tournament-update/tournament-form-service/tournament-form.service';
import { SettingsService } from '@skywind-group/lib-swui';
import { currencyFormatter } from '../../../../../../common/directives/appSettingCurrencyFormatter/settingCurrencyFormatter.directive';


@Component({
  selector: 'sw-hub-tournament-payout-form-ranks',
  templateUrl: './tournament-payout-form-ranks.component.html',
  styleUrls: ['./tournament-payout-form-ranks.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TournamentPayoutFormRanksComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => TournamentPayoutFormRanksComponent),
      multi: true
    }
  ],
})
export class TournamentPayoutFormRanksComponent extends BaseComponent
  implements ControlValueAccessor, Validator, OnInit {

  @Input() currency?: string;

  @Input()
  get ranges(): FormTournamentRange | null {
    return this._ranges$.value;
  }

  set ranges( value: FormTournamentRange | null ) {
    this._ranges$.next(value || null);
  }

  readonly form: FormGroup;
  columnsRanges: string[] = ['Prize'];
  maxNumberInputLength = 12;
  prizeMultiplierSupported = false;
  onChange: ( _: any ) => void = (() => {
  });
  isDisabled = false;
  prizeTypes: { id: TournamentPayoutPrizeType; text: string; disabled?: boolean }[] = [];

  private payouts: FeaturePayout[] = [];

  private readonly _ranges$ = new BehaviorSubject<FormTournamentRange | null>(null);
  private readonly _payouts$ = new Subject<void>();

  constructor( private readonly fb: FormBuilder,
               private readonly formService: TournamentFormService,
               protected readonly settingsService: SettingsService
  ) {
    super();
    this.form = this.initForm();
  }

  ngOnInit() {
    combineLatest([this._ranges$, this._payouts$])
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( [ranges] ) => {
        this.setHeader(ranges);
        this.setPayouts(this.payouts);
      });

    this.payoutsFormArray.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(() => {
        this.payoutsFormArray.controls.forEach(( control: AbstractControl ) => {
          const ctrl = control as FormGroup;
          const prizeTypeControl = ctrl.get('prizeType') as FormControl;
          const payoutsPerRangeArray = ctrl.get('payoutsPerRange') as FormArray;
          payoutsPerRangeArray.controls.forEach(( payout: AbstractControl ) => {
            if (prizeTypeControl.value === 'text') {
              payout.setValidators(Validators.compose([
                Validators.required,
                Validators.maxLength(50)
              ]));
            } else {
              payout.setValidators(Validators.compose([
                Validators.required,
                numbersOnlyValidator,
                fractionsNumbersLengthValidator,
                numberMaxLength(this.maxNumberInputLength),
              ]));
            }
          });
        });
      });

    this.form.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(() => {
        const val = this.form.getRawValue();
        const processed = val.payouts.map(( payout: FeaturePayout ) => {
          const payoutsPerRange: any[] = payout.payoutsPerRange;
          if (payout.prizeType === 'fixed' || payout.prizeType === 'multiplier') {
            payout.payoutsPerRange = payoutsPerRange.map(( el: any ) => el !== null && typeof el !== 'number' ? parseFloat(el) : el);
          } else if (payout.prizeType === 'text') {
            payout.payoutsPerRange = payoutsPerRange.map(( el: any ) => el !== null && typeof el !== 'string' ? el.toString() : el);
          }
          return payout;
        });
        this.payouts = processed;
        this.onChange(processed);
      });

    this.formService.rankingFormulaType$
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(() => {
        this.prizeMultiplierSupported = this.formService.isPrizeMultiplierSupported;
        this.prizeTypes = [
          { id: 'fixed', text: 'Fixed' },
          { id: 'multiplier', text: 'Multiplier', disabled: !this.prizeMultiplierSupported },
          { id: 'text', text: 'Text' }
        ];
      });
  }

  onTouched: any = () => {
  };

  writeValue( value: FeaturePayout[] ) {
    this.payouts = Array.isArray(value) ? value : [];
    this._payouts$.next();
  }

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( disabled: boolean ) {
    this.isDisabled = disabled;
    disabled ? this.form.disable() : this.form.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid ? null : { invalidForm: { valid: false } };
  }

  get payoutsFormArray(): FormArray {
    return this.form.get('payouts') as FormArray;
  }

  getPlayers( payoutRow: AbstractControl ): FormControl {
    return payoutRow.get('players') as FormControl;
  }

  getPrizeType( payoutRow: AbstractControl ): FormControl {
    return payoutRow.get('prizeType') as FormControl;
  }

  addPayoutItem() {
    this.newPayoutItem(this.payouts.slice().pop()?.prizeType);
  }

  removePayoutItem( index: number ) {
    this.payoutsFormArray.removeAt(index);
  }

  getPayoutsPerRange( payoutRow: AbstractControl ): FormArray {
    return payoutRow.get('payoutsPerRange') as FormArray;
  }

  private setHeader( ranges: FormTournamentRange | null ) {
    if (ranges) {
      if (this.currency) {
        const currencyRanges = ranges[this.currency];
        const currencyFormat = this.settingsService.appSettings.currencyFormat;
        if (currencyRanges) {
          this.columnsRanges = currencyRanges.map(( el: TournamentRange ) => {
            const suffixMin = el && el.min ?
              currencyFormatter(currencyFormat, el.min, this.currency) : '0';
            const suffixMax = el && el.max ?
              currencyFormatter(currencyFormat, el.max, this.currency) : 'MAX';
            return 'Prize ' + getCurrencySymbol(this.currency || '', 'narrow') + suffixMin + '-' + suffixMax;
          });
        }
      }
    } else {
      this.columnsRanges = ['Prize'];
    }
  }

  private setPayouts( payouts: FeaturePayout[] ) {
    this.payoutsFormArray.clear();
    if (payouts.length) {
      payouts.forEach(payout => {
        this.newPayoutItem(payout.prizeType);
      });
      this.payoutsFormArray.patchValue(payouts);
    } else {
      this.newPayoutItem();
    }

    this.isDisabled ? this.form.disable() : this.form.enable();
  }

  private newPayoutItem( prizeType: TournamentPayoutPrizeType = 'fixed' ) {
    const payoutRow = this.initPayoutItem(prizeType);
    const payoutsPerRange = this.getPayoutsPerRange(payoutRow);
    payoutsPerRange.clear();
    if (this.ranges) {
      if (this.currency) {
        const currencyRanges = this.ranges[this.currency];
        if (currencyRanges?.length) {
          currencyRanges.forEach(() => {
            payoutsPerRange.push(this.initPerRangeItem());
          });
        }
      }
    } else {
      payoutsPerRange.push(this.initPerRangeItem());
    }
    this.payoutsFormArray.push(payoutRow);
  }

  private initForm(): FormGroup {
    return this.fb.group({
      payouts: this.fb.array([this.initPayoutItem()])
    });
  }

  private initPayoutItem( prizeType: TournamentPayoutPrizeType = 'fixed' ): FormGroup {
    return this.fb.group({
      players: [
        { value: null, disabled: this.form && this.form.disabled }, Validators.compose([
          Validators.required,
          digitsOnlyValidator,
          numberMaxLength(this.maxNumberInputLength),
        ])
      ],
      prizeType: [prizeType, Validators.required],
      payoutsPerRange: this.fb.array([this.initPerRangeItem()])
    });
  }

  private initPerRangeItem(): FormControl {
    return this.fb.control({
      value: null,
      disabled: this.form && this.form.disabled
    }, Validators.compose([
      Validators.required,
      numbersOnlyValidator,
      fractionsNumbersLengthValidator,
      numberMaxLength(this.maxNumberInputLength),
    ]));
  }
}
