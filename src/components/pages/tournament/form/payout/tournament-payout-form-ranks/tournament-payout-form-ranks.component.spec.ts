import { TournamentFormService } from '../../../tournament-update/tournament-form-service/tournament-form.service';
import { PAYOUT_FORM_MODULES } from '../tournament-payout-form.module';
import { TournamentPayoutFormRanksComponent } from './tournament-payout-form-ranks.component';
import { SettingsService, SwuiConstantsService } from '@skywind-group/lib-swui';
import { createHostFactory, SpectatorHost } from '@ngneat/spectator';
import { FormControl } from '@angular/forms';
import { FeaturePayout } from '../../../../interfaces/feature';
import { Component } from '@angular/core';

@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}

describe('TournamentPayoutFormRanksComponent', () => {
  let spectator: SpectatorHost<TournamentPayoutFormRanksComponent, CustomHostComponent>;
  const createHost = createHostFactory({
    component: TournamentPayoutFormRanksComponent,
    host: CustomHostComponent,
    imports: [
      ...PAYOUT_FORM_MODULES,
    ],
    providers: [
      TournamentFormService,
      SettingsService
    ]
  });
  const currency = 'CNY';
  const payout: FeaturePayout[] = [
    {
      players: 3,
      payoutsPerRange: [1, 2],
      prizeType: 'fixed'
    }
  ];
  const ranges = {
    'CNY': [
      {
        min: 0,
        max: 10.00,
        minQualifyAmount: 10.00
      },
      {
        min: 1,
        max: 10.00,
        minQualifyAmount: 10.00
      }
    ]
  };

  beforeEach(() => {
    spectator = createHost(`<sw-hub-tournament-payout-form-ranks [formControl]="form"></sw-hub-tournament-payout-form-ranks>`);
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();
  });

  it('should set columnsRanges without currency', () => {
    spectator.hostComponent.form.patchValue(payout);
    spectator.component.ranges = ranges;

    expect(spectator.component.columnsRanges.length).toEqual(1);
    expect(spectator.component.columnsRanges[0]).toEqual('Prize');
  });

  it('should set columnsRanges with currency', () => {
    spectator.component.currency = currency;
    spectator.hostComponent.form.patchValue(payout);
    spectator.component.ranges = ranges;

    expect(spectator.component.columnsRanges.length).toEqual(2);
    expect(spectator.component.columnsRanges[0]).toEqual(`Prize ${SwuiConstantsService.currencySymbol(currency)}${ranges[currency][0].min}-${ranges[currency][0].max}.00`);
  });

  it('should create newPayoutItem', () => {
    spectator.component.currency = currency;
    spectator.hostComponent.form.patchValue([]);
    spectator.component.ranges = ranges;

    expect(spectator.component.payoutsFormArray.controls.length).toEqual(1);
    expect(spectator.component.payoutsFormArray.controls[0].value['prizeType']).toEqual('fixed');
  });

  it('form set disabled state', () => {
    spectator.component.setDisabledState(true);
    expect(spectator.component.form.disabled).toBeTrue();
  });

  it('form remove payout item', () => {
    spectator.hostComponent.form.patchValue(payout);
    spectator.component.ranges = ranges;
    expect(spectator.component.payoutsFormArray.value.length).toEqual(1);

    spectator.component.removePayoutItem(0);
    expect(spectator.component.payoutsFormArray.value.length).toEqual(0);
  });

  it('form add payout item', () => {
    spectator.hostComponent.form.patchValue(payout);
    spectator.component.ranges = ranges;
    expect(spectator.component.payoutsFormArray.value.length).toEqual(1);

    spectator.component.addPayoutItem();
    expect(spectator.component.payoutsFormArray.value.length).toEqual(2);
  });
});
