import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { combineLatest } from 'rxjs';
import { filter, map, takeUntil } from 'rxjs/operators';
import { BaseComponent } from '../../../../../../common/components/base.component';
import { CurrenciesManagerService } from '../../../../../../common/components/currencies-manager/currencies-manager.service';
import { FeatureCurrency, FeaturePayout } from '../../../../interfaces/feature';
import { TournamentLuckyDraw, TournamentRanges } from '../../../../interfaces/tournament';
import { TournamentFormService } from '../../../tournament-update/tournament-form-service/tournament-form.service';

@Component({
  selector: 'sw-tournament-payout-totals',
  templateUrl: './tournament-payout-totals.component.html',
  styleUrls: ['./tournament-payout-totals.component.scss']
})
export class TournamentPayoutTotalsComponent extends BaseComponent implements OnInit {

  @Input()
  get payouts(): FeaturePayout[] {
    return this._payouts;
  }

  set payouts( value: FeaturePayout[] ) {
    if (!value) {
      return;
    }
    this._payouts = value;
  }

  @Input()
  get ranges(): TournamentRanges | null {
    return this._ranges;
  }

  set ranges( value: TournamentRanges | null ) {
    this._ranges = value || null;
  }

  @Input() luckyDraw?: TournamentLuckyDraw;

  currencies: FeatureCurrency[] = [];
  currencyToActivate?: string;
  @ViewChild('payoutTotal') payoutTotalRef?: ElementRef;
  private _payouts: FeaturePayout[] = [];
  private _ranges: TournamentRanges | null = null;

  constructor(private readonly formService: TournamentFormService,
              private readonly currenciesManagerService: CurrenciesManagerService ) {
    super();
  }

  ngOnInit(): void {
    combineLatest([this.formService.baseCurrency$, this.currenciesManagerService.selectedCurrencies$])
      .pipe(
        map(( [baseCurrency, currencies] ) => {
          if (baseCurrency && !currencies.find(( el: FeatureCurrency ) => el.code === baseCurrency)) {
            return [{ code: baseCurrency, rate: 1 }, ...currencies];
          } else if (baseCurrency && currencies.find(( el: FeatureCurrency ) => el.code === baseCurrency)){
            const index = currencies.findIndex( ( el: FeatureCurrency ) => el.code === baseCurrency);
            currencies.splice(index, 1);
            return [{ code: baseCurrency, rate: 1 }, ...currencies];
          } else {
            return currencies;
          }
        }),
        takeUntil(this.destroyed)
      )
      .subscribe( (currencies: FeatureCurrency[]) => {
        this.currencies = currencies;
      });

    this.currenciesManagerService.selectedPayoutCurrency$
      .pipe(
        filter(val => !!val),
        takeUntil(this.destroyed)
      )
      .subscribe((val: FeatureCurrency | undefined) => {
        this.currencyToActivate = val ? val.code : undefined;
        setTimeout( () => {
          if (this.payoutTotalRef) {
            const refElement = this.payoutTotalRef.nativeElement as HTMLElement;
            const scrollableContainer = document.getElementById('main-content-wrap');
            if (scrollableContainer) {
              scrollableContainer.scroll({top: refElement?.offsetTop + 500, behavior:'smooth'});
            }
          }
        }, 500);
      });
  }
}
