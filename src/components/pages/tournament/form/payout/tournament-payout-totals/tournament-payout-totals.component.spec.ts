import { CommonModule } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CurrenciesManagerService } from '../../../../../../common/components/currencies-manager/currencies-manager.service';
import { JpnService, MockJpnService } from '../../../../../../common/services/jpn.service';
import { TournamentFormService } from '../../../tournament-update/tournament-form-service/tournament-form.service';

import { TournamentPayoutTotalsComponent } from './tournament-payout-totals.component';
import { PAYOUT_FORM_MODULES } from '../tournament-payout-form.module';
import { SettingsService } from '@skywind-group/lib-swui';
import { createComponentFactory, createServiceFactory, Spectator, SpectatorService } from '@ngneat/spectator';


describe('TournamentPayoutTotalsComponent', () => {
  let spectator: Spectator<TournamentPayoutTotalsComponent>;
  const createComponent = createComponentFactory({
    component: TournamentPayoutTotalsComponent,
    imports: [
      CommonModule,
      HttpClientTestingModule,
      ...PAYOUT_FORM_MODULES,
    ],
    providers: [
      SettingsService,
      TournamentFormService,
      { provide: JpnService, useClass: MockJpnService },
      CurrenciesManagerService,
    ]
  });

  let tournamentFormService: SpectatorService<TournamentFormService>;
  let currenciesManagerService: SpectatorService<CurrenciesManagerService>;
  const createTournamentFormService = createServiceFactory({
    service: TournamentFormService,
  });
  const createCurrenciesManagerService = createServiceFactory({
    service: CurrenciesManagerService,
  });

  const baseCurrency = 'CNY';
  const selectedCurrencies = [{ code: 'USD', rate: 2 }];
  const selectedBaseCurrency = [{ code: baseCurrency, rate: 2 }];
  const selectedPayoutCurrency = { code: baseCurrency, rate: 2 };

  beforeEach(() => {
    spectator = createComponent();
    tournamentFormService = createTournamentFormService();
    currenciesManagerService = createCurrenciesManagerService();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();

    expect(tournamentFormService).toBeTruthy();
    expect(tournamentFormService).toBeDefined();

    expect(currenciesManagerService).toBeTruthy();
    expect(currenciesManagerService).toBeDefined();
  });

  it('selectedCurrencies is not contain baseCurrency', () => {
    tournamentFormService.service.baseCurrency = baseCurrency;
    currenciesManagerService.service.selectedCurrencies = selectedCurrencies;

    expect(spectator.component.currencies).toEqual([
      { code: baseCurrency, rate: 1 }, ...selectedCurrencies
    ]);
  });

  it('selectedCurrencies is contain baseCurrency', () => {
    tournamentFormService.service.baseCurrency = baseCurrency;
    currenciesManagerService.service.selectedCurrencies = selectedBaseCurrency;

    expect(spectator.component.currencies).toEqual([
      { code: baseCurrency, rate: 1 }, ...selectedBaseCurrency
    ]);
  });

  it('currencyToActivate is selected', () => {
    currenciesManagerService.service.selectedPayoutCurrency = selectedPayoutCurrency;

    expect(spectator.component.currencyToActivate).toEqual(baseCurrency);
  });
});
