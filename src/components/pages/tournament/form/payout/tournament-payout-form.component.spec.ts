import { Component } from '@angular/core';

import { TournamentPayoutFormComponent } from './tournament-payout-form.component';
import { PAYOUT_FORM_MODULES } from './tournament-payout-form.module';
import { FormControl } from '@angular/forms';
import { createHostFactory, createServiceFactory, SpectatorHost, SpectatorService } from '@ngneat/spectator';
import {
  FormTournamentRange,
  TournamentFormService
} from '../../tournament-update/tournament-form-service/tournament-form.service';
import { CurrenciesManagerService } from '../../../../../common/components/currencies-manager/currencies-manager.service';
import { JpnService, MockJpnService } from '../../../../../common/services/jpn.service';
import { SettingsService } from '@skywind-group/lib-swui';
import { TournamentPayoutFormRanksComponent } from './tournament-payout-form-ranks/tournament-payout-form-ranks.component';
import { TournamentPayoutFormLuckyComponent } from './tournament-payout-form-lucky/tournament-payout-form-lucky.component';


@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}

describe('TournamentPayoutFormComponent', () => {
  let spectator: SpectatorHost<TournamentPayoutFormComponent, CustomHostComponent>;
  const createHost = createHostFactory({
    component: TournamentPayoutFormComponent,
    host: CustomHostComponent,
    imports: [
      ...PAYOUT_FORM_MODULES,
    ],
    declarations: [TournamentPayoutFormRanksComponent, TournamentPayoutFormLuckyComponent],
    providers: [
      SettingsService,
      TournamentFormService,
      CurrenciesManagerService,
      { provide: JpnService, useClass: MockJpnService },
    ]
  });

  let spectatorService: SpectatorService<TournamentFormService>;
  const createService = createServiceFactory({
    service: TournamentFormService
  });

  const baseCurrency = 'CNY';
  const ranges: FormTournamentRange = {
    'test': [
      {
        min: 0,
        max: 10,
        minQualifyAmount: 10
      }
    ]
  };

  beforeEach(() => {
    spectator = createHost(`<sw-tournament-payout-form [formControl]="form"></sw-tournament-payout-form>`);
    spectatorService = createService();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();

    expect(spectatorService.service).toBeTruthy();
    expect(spectatorService.service).toBeDefined();
  });

  it('range should be null when base currency unset', () => {
    spectatorService.service.ranges = ranges;
    expect(spectator.component.ranges).toBeNull();
  });

  it('range should be null when ranges unset', () => {
    spectatorService.service.baseCurrency = baseCurrency;
    expect(spectator.component.ranges).toBeNull();
    expect(spectator.component.baseCurrency).toEqual(baseCurrency);
  });

  it('range should be valid', () => {
    spectatorService.service.baseCurrency = baseCurrency;
    spectatorService.service.ranges = ranges;
    expect(spectator.component.ranges).toEqual(ranges);
  });
});
