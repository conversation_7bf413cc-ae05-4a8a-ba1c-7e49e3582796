<form [formGroup]="form" class="ranking-formula">
  <mat-form-field appearance="outline" class="ranking-formula__select">
    <mat-label>Criteria</mat-label>
    <lib-swui-select
      #type
      [formControl]="typeControl"
      [disableEmptyOption]="true"
      [data]="rankingFormulaTypes"></lib-swui-select>
    <mat-error>
      <lib-swui-control-messages [control]="typeControl"></lib-swui-control-messages>
    </mat-error>
  </mat-form-field>
  <ng-container [ngSwitch]="typeControl.value">

    <ng-template ngSwitchCase="single">
      <div class="ranking-formula__rank">
        <span class="ranking-formula__title">Rank:</span> Players are ranked by highest sum of bets and wins during the
        tournament
      </div>
      <div class="ranking-formula__score">
        <div class="ranking-formula__left">
          <span class="ranking-formula__title">Score:</span>
        </div>
        <div class="ranking-formula__right">
          <ng-container *ngTemplateOutlet="tplBetPoints"></ng-container>
          <ng-container *ngTemplateOutlet="tplWinPoints"></ng-container>
        </div>
      </div>
    </ng-template>

    <ng-template ngSwitchCase="highestTotalBet">
      <div class="ranking-formula__rank">
        <span class="ranking-formula__title">Rank:</span> Players are ranked by highest sum of all their bets during the
        tournament
      </div>
      <div class="ranking-formula__score">
        <div class="ranking-formula__left">
          <span class="ranking-formula__title">Score:</span>
        </div>
        <div class="ranking-formula__right">
          <ng-container *ngTemplateOutlet="tplBetPoints"></ng-container>
        </div>
      </div>
    </ng-template>

    <ng-template ngSwitchCase="highestTotalWin">
      <div class="ranking-formula__rank">
        <span class="ranking-formula__title">Rank:</span> Players are ranked by highest sum of all their wins during the
        tournament
      </div>
      <div class="ranking-formula__score">
        <div class="ranking-formula__left">
          <span class="ranking-formula__title">Score:</span>
        </div>
        <div class="ranking-formula__right">
          <ng-container *ngTemplateOutlet="tplWinPoints"></ng-container>
        </div>
      </div>
    </ng-template>

    <ng-template ngSwitchCase="highestSingleBetWin">
      <div class="ranking-formula__rank">
        <span class="ranking-formula__title">Rank:</span> Players are ranked by highest win multiplier (win/bet) among
        all rounds in the tournament
      </div>
      <div class="ranking-formula__score">
        <div class="ranking-formula__left">
          <span class="ranking-formula__title">Score:</span>
        </div>
        <div class="ranking-formula__right">
          <span class="ranking-formula__solo">Highest single win/bet x 100</span>
        </div>
      </div>
    </ng-template>

    <ng-template ngSwitchCase="highestSingleWin">
      <div class="ranking-formula__rank">
        <span class="ranking-formula__title">Rank:</span> Players are ranked by highest single round win
        amount among all wins during the tournament.
      </div>
      <div class="ranking-formula__score">
        <div class="ranking-formula__left">
          <span class="ranking-formula__title">Score:</span>
        </div>
        <div class="ranking-formula__right">
          <span class="ranking-formula__solo">Highest single win amount x 100</span>
        </div>
      </div>
    </ng-template>

    <ng-template ngSwitchCase="highestNumberOfSpins">
      <div class="ranking-formula__rank">
        <span class="ranking-formula__title">Rank:</span> Players are ranked by highest number of rounds
        played during the tournament
      </div>
      <div class="ranking-formula__score">
        <div class="ranking-formula__left">
          <span class="ranking-formula__title">Score:</span>
        </div>
        <div class="ranking-formula__right">
          <span class="ranking-formula__solo">10 points for each round</span>
        </div>
      </div>
    </ng-template>

    <ng-template ngSwitchCase="multiple">
      <div class="ranking-formula__rank">
        <span class="ranking-formula__title">Rank:</span> Players are ranked by highest sum of all win multipliers
        (win/bet) in the tournament.
      </div>
      <div class="ranking-formula__score">
        <div class="ranking-formula__left">
          <span class="ranking-formula__title">Score:</span>
        </div>
        <div class="ranking-formula__right">
          <span class="ranking-formula__solo">Sum of win/bet x 10</span>
        </div>
      </div>
    </ng-template>
  </ng-container>
</form>

<ng-template #tplBetPoints>
  <div class="ranking-formula__row">
    <div class="ranking-formula__prefix">1 point per every</div>
    <div class="ranking-formula__control">
      <mat-form-field appearance="outline" class="ranking-formula__field no-field-padding">
        <span matPrefix>{{baseCurrency | currencySymbol}}</span>
        <input matInput type="number" min="0" maxlength="5" [formControl]="betPointsControl"
               swCurrencyFormatter [currencyCode]="baseCurrency">
        <mat-error class="ranking-formula__error">
          <lib-swui-control-messages [control]="betPointsControl"></lib-swui-control-messages>
        </mat-error>
      </mat-form-field>
    </div>
    <div class="ranking-formula__suffix">bet</div>
  </div>
</ng-template>

<ng-template #tplWinPoints>
  <div class="ranking-formula__row">
    <div class="ranking-formula__prefix">1 point per every</div>
    <div class="ranking-formula__control">
      <mat-form-field appearance="outline" class="ranking-formula__field no-field-padding">
        <span matPrefix>{{baseCurrency | currencySymbol}}</span>
        <input matInput type="number" min="0" [formControl]="winPointsControl" swCurrencyFormatter [currencyCode]="baseCurrency">
        <mat-error class="ranking-formula__error">
          <lib-swui-control-messages [control]="winPointsControl"></lib-swui-control-messages>
        </mat-error>
      </mat-form-field>
    </div>
    <div class="ranking-formula__suffix">win</div>
  </div>
</ng-template>
