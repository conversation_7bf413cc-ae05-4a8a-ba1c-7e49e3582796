import { CommonModule } from '@angular/common';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { TournamentFormService } from '../../../tournament-update/tournament-form-service/tournament-form.service';

import { TournamentRankingFormulaComponent } from './tournament-ranking-formula.component';
import { RANKING_FORMULA_MODULES } from './tournament-ranking-formula.module';
import { Component } from '@angular/core';
import { FormControl } from '@angular/forms';
import { createHostFactory, createServiceFactory, SpectatorHost, SpectatorService } from '@ngneat/spectator';


@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}

describe('TournamentRankingFormulaComponent', () => {
  let spectator: SpectatorHost<TournamentRankingFormulaComponent, CustomHostComponent>;
  const createHost = createHostFactory({
    component: TournamentRankingFormulaComponent,
    host: CustomHostComponent,
    imports: [
      CommonModule,
      NoopAnimationsModule,
      TranslateModule.forRoot(),
      ...RANKING_FORMULA_MODULES,
    ],
    declarations: [TournamentRankingFormulaComponent],
    providers: [TournamentFormService]
  });

  let spectatorService: SpectatorService<TournamentFormService>;
  const createService = createServiceFactory({
    service: TournamentFormService
  });

  beforeEach(() => {
    spectator = createHost(`<sw-tournament-ranking-formula [formControl]="form"></sw-tournament-ranking-formula>`);
    spectatorService = createService();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();

    expect(spectatorService.service).toBeTruthy();
    expect(spectatorService.service).toBeDefined();
  });

  it('set base currency', () => {
    spectatorService.service.baseCurrency = 'USD';
    expect(spectator.component.baseCurrency).toEqual('USD');
  });


  it('set type single', () => {
    spectator.hostComponent.form.patchValue({
      type: 'single',
      betPoints: 0,
      winPoints: 0
    });
    expect(spectator.component.betPointsControl.enabled).toEqual(true);
    expect(spectator.component.winPointsControl.enabled).toEqual(true);
  });

  it('set type highestTotalBet', () => {
    spectator.hostComponent.form.patchValue({
      type: 'highestTotalBet',
      betPoints: 0,
      winPoints: 0
    });
    expect(spectator.component.betPointsControl.enabled).toEqual(true);
    expect(spectator.component.winPointsControl.disabled).toEqual(true);
  });

  it('set type highestTotalWin', () => {
    spectator.hostComponent.form.patchValue({
      type: 'highestTotalWin',
      betPoints: 0,
      winPoints: 0
    });
    expect(spectator.component.betPointsControl.disabled).toEqual(true);
    expect(spectator.component.winPointsControl.enabled).toEqual(true);
  });

  it('set type multiple', () => {
    spectator.hostComponent.form.patchValue({
      type: 'multiple',
      betPoints: 0,
      winPoints: 0
    });
    expect(spectator.component.betPointsControl.disabled).toEqual(true);
    expect(spectator.component.winPointsControl.disabled).toEqual(true);
  });

  it('output data when type highestTotalBet', () => {
    spectator.hostComponent.form.patchValue({
      type: 'highestTotalBet',
      betPoints: 0,
      winPoints: 0
    });
    expect(spectator.hostComponent.form.value).toEqual({
      type: 'highestTotalBet',
      betPoints: 0
    });
  });

  it('output data when type highestTotalWin', () => {
    spectator.hostComponent.form.patchValue({
      type: 'highestTotalWin',
      betPoints: 0,
      winPoints: 0
    });
    expect(spectator.hostComponent.form.value).toEqual({
      type: 'highestTotalWin',
      winPoints: 0
    });
  });

  it('output data when type single', () => {
    spectator.hostComponent.form.patchValue({
      type: 'single',
      betPoints: 0,
      winPoints: 0
    });
    expect(spectator.hostComponent.form.value).toEqual({
      type: 'single',
      betPoints: 0,
      winPoints: 0
    });
  });

  it('output data when type multiple', () => {
    spectator.hostComponent.form.patchValue({
      type: 'multiple',
      betPoints: 0,
      winPoints: 0
    });
    expect(spectator.hostComponent.form.value).toEqual({
      type: 'multiple'
    });
  });
});
