.ranking-formula {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 3px;
  padding: 24px;
  &__select {
    width: 500px;
  }
  &__title {
    margin-right: 8px;
    font-weight: 500;
  }
  &__field {
    width: 132px;
  }
  &__score {
    display: flex;
    margin-top: 12px;
    .ranking-formula {
      &__title {
        margin-top: 11px;
        display: block;
      }
    }
  }
  &__solo {
    display: block;
    margin-top: 11px;
  }
  &__right {
    display: flex;
    flex-direction: column;
  }
  &__row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  &__control {
    position: relative;
  }
  &__error {
    position: absolute;
    bottom: -16px;
    left: 0;
    font-size: 12px;
    white-space: nowrap;
  }
  &__prefix {
    margin-right: 6px;
  }
  &__suffix {
    margin-left: 6px;
  }
  input {
    text-align: right;
  }
}
