import { Component, forwardRef, OnInit, ViewChild } from '@angular/core';
import {
  ControlValueAccessor, FormBuilder, FormControl, FormGroup, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors,
  ValidatorFn, Validators
} from '@angular/forms';
import { SwuiSelectComponent, SwuiSelectOption } from '@skywind-group/lib-swui';
import { filter, takeUntil } from 'rxjs/operators';
import { BaseComponent } from '../../../../../../common/components/base.component';
import { fractionsNumbersLengthValidator, numberMaxLength } from '../../../../../../common/lib/validators';
import { TournamentRankingFormula, TournamentRankingFormulaType } from '../../../../interfaces/tournament';
import { TournamentFormService } from '../../../tournament-update/tournament-form-service/tournament-form.service';

const VALIDATORS: ValidatorFn[] = [
  fractionsNumbersLengthValidator,
  numberMaxLength(12),
  Validators.required,
];

@Component({
  selector: 'sw-tournament-ranking-formula',
  templateUrl: './tournament-ranking-formula.component.html',
  styleUrls: ['./tournament-ranking-formula.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TournamentRankingFormulaComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => TournamentRankingFormulaComponent),
      multi: true
    },
  ]
})
export class TournamentRankingFormulaComponent extends BaseComponent implements OnInit, ControlValueAccessor {
  readonly rankingFormulaTypes: SwuiSelectOption[] = [
    { id: 'single', text: 'Total bet and win amounts' },
    { id: 'highestTotalBet', text: 'Total bet amount' },
    { id: 'highestTotalWin', text: 'Total win amount' },
    { id: 'multiple', text: 'Total win multipliers' },
    { id: 'highestSingleBetWin', text: 'Highest single round win multiplier' },
    { id: 'highestSingleWin', text: 'Highest single round win amount' },
    { id: 'highestNumberOfSpins', text: 'Number of rounds' },
  ];
  readonly form: FormGroup;

  baseCurrency?: string;
  onChange: ( _: any ) => void = (() => {
  });

  @ViewChild('type') typeInput?: SwuiSelectComponent;

  constructor( private readonly fb: FormBuilder,
               private readonly formService: TournamentFormService
  ) {
    super();
    this.form = this.initForm();
  }

  ngOnInit(): void {
    this.formService.formSubmitted$.pipe(
      filter(submitted => submitted),
      takeUntil(this.destroyed)
    ).subscribe(() => {
      this.typeControl.updateValueAndValidity({ onlySelf: true, emitEvent: false, });
    });

    this.formService.baseCurrency$
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: string ) => {
        this.baseCurrency = val;
      });

    this.typeControl.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: TournamentRankingFormulaType ) => {
        switch (val) {
          case 'single': {
            this.betPointsControl.enable();
            this.winPointsControl.enable();
            break;
          }
          case 'highestTotalBet': {
            this.betPointsControl.enable();
            this.winPointsControl.disable();
            break;
          }
          case 'highestTotalWin': {
            this.betPointsControl.disable();
            this.winPointsControl.enable();
            break;
          }
          default: {
            this.betPointsControl.disable();
            this.winPointsControl.disable();
            break;
          }
        }
      });

    this.form.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(() => {
        this.onChange(TournamentRankingFormulaComponent.getProcessedValue(this.form.getRawValue()));
      });
  }

  writeValue( val: TournamentRankingFormula ): void {
    if (!val) {
      return;
    }
    this.form.patchValue(val);
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState?( isDisabled: boolean ): void {
    isDisabled ? this.form.disable() : this.form.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid || this.form.status === 'DISABLED' ? null : { invalidForm: { valid: false } };
  }

  get typeControl(): FormControl {
    return this.form.get('type') as FormControl;
  }

  get betPointsControl(): FormControl {
    return this.form.get('betPoints') as FormControl;
  }

  get winPointsControl(): FormControl {
    return this.form.get('winPoints') as FormControl;
  }

  private static getProcessedValue( val: TournamentRankingFormula): TournamentRankingFormula {
    switch (val.type) {
      case 'highestTotalBet': {
        delete val.winPoints;
        break;
      }
      case 'highestTotalWin': {
        delete val.betPoints;
        break;
      }
      default: {
        if (val.type !== 'single') {
          delete val.betPoints;
          delete val.winPoints;
        }
        break;
      }
    }

    return val;
  }

  private initForm(): FormGroup {
    return this.fb.group({
      type: ['', Validators.required],
      betPoints: [null, VALIDATORS],
      winPoints: [null, VALIDATORS]
    });
  }
}
