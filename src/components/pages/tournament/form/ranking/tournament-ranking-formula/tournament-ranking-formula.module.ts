import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { SwuiControlMessagesModule, SwuiCurrencySymbolModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { FloatFormatterModule } from '../../../../../../common/directives/floatFormatter/floatFormatter.module';
import { TournamentRankingFormulaComponent } from './tournament-ranking-formula.component';
import { SettingCurrencyFormatterModule } from '../../../../../../common/directives/appSettingCurrencyFormatter/settingCurrencyFormatter.module';

export const RANKING_FORMULA_MODULES = [
  ReactiveFormsModule,
  MatFormFieldModule,
  SwuiSelectModule,
  MatInputModule,
  SwuiControlMessagesModule,
  SwuiCurrencySymbolModule,
  FloatFormatterModule,
];

@NgModule({
  declarations: [TournamentRankingFormulaComponent],
  exports: [TournamentRankingFormulaComponent],
    imports: [
        CommonModule,
        ...RANKING_FORMULA_MODULES,
        SettingCurrencyFormatterModule,
    ]
})
export class TournamentRankingFormulaModule {
}
