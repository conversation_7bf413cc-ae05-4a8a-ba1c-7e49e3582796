<div class="error-message margin-bottom16" *swIsControlInvalid="ranges">
  <strong class="error-message__heading">Following fields has errors:</strong>
  <ul class="error-message__list">
    <ng-container *ngFor="let range of ranges.controls; let i=index">
      <li *swIsControlInvalid="range">
        <div class="error-message__title">Range {{i + 1}}</div>
        <div *swIsControlInvalid="minBetControl(range)">
          "Min bet" :
          <lib-swui-control-messages [control]="minBetControl(range)"></lib-swui-control-messages>
        </div>

        <div *swIsControlInvalid="maxBetControl(range)">
          "To bet" :
          <lib-swui-control-messages [control]="maxBetControl(range)"></lib-swui-control-messages>
        </div>

        <div *swIsControlInvalid="minQualifyAmountControl(range)">
          "Total bet amount" :
          <lib-swui-control-messages [control]="minQualifyAmountControl(range)"></lib-swui-control-messages>
        </div>
      </li>
    </ng-container>
  </ul>
</div>
