import { Component, Input } from '@angular/core';
import { AbstractControl, FormArray, FormControl } from '@angular/forms';

@Component({
  selector: 'sw-tournament-range-errors',
  templateUrl: './tournament-range-errors.component.html',
  styleUrls: ['./tournament-range-errors.component.scss']
})
export class TournamentRangeErrorsComponent {
  @Input() ranges?: FormArray;

  minQualifyAmountControl( range: AbstractControl ): FormControl {
    return range.get('minQualifyAmount') as FormControl;
  }

  maxBetControl( range: AbstractControl ): FormControl {
    return range.get('max') as FormControl;
  }

  minBetControl( range: AbstractControl ): FormControl {
    return range.get('min') as FormControl;
  }
}

