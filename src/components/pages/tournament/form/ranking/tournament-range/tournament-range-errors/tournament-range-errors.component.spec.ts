import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TournamentRangeErrorsComponent } from './tournament-range-errors.component';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { RANGE_MODULES } from '../tournament-range.module';


describe('TournamentRangesErrorsComponent', () => {
  let component: TournamentRangeErrorsComponent;
  let fixture: ComponentFixture<TournamentRangeErrorsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        NoopAnimationsModule,
        ...RANGE_MODULES
      ],
      declarations: [TournamentRangeErrorsComponent],
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TournamentRangeErrorsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
