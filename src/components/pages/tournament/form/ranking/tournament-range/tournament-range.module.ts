import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { TournamentRangeGroupModule } from './tournament-range-group/tournament-range-group.module';
import { TournamentRangeComponent } from './tournament-range.component';
import { PipesModule } from '../../../../../../common/pipes/pipes.module';

export const RANGE_MODULES = [
  ReactiveFormsModule,
  TournamentRangeGroupModule,
  PipesModule,
];

@NgModule({
  declarations: [
    TournamentRangeComponent,
  ],
  exports: [TournamentRangeComponent],
  imports: [
    CommonModule,
    ...RANGE_MODULES,
  ]
})
export class TournamentRangeModule {
}
