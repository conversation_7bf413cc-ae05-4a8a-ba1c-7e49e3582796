import { CommonModule } from '@angular/common';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { CurrenciesManagerService } from '../../../../../../common/components/currencies-manager/currencies-manager.service';
import { TournamentFormService } from '../../../tournament-update/tournament-form-service/tournament-form.service';

import { TournamentRangeComponent } from './tournament-range.component';
import { RANGE_MODULES } from './tournament-range.module';
import { JpnService, MockJpnService } from '../../../../../../common/services/jpn.service';
import { createHostFactory, createServiceFactory, SpectatorHost, SpectatorService } from '@ngneat/spectator';
import { Component } from '@angular/core';
import { FormControl } from '@angular/forms';
import { FeatureCurrency } from '../../../../interfaces/feature';


@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}

describe('TournamentRangeComponent', () => {
  let spectator: SpectatorHost<TournamentRangeComponent, CustomHostComponent>;
  const createHost = createHostFactory({
    component: TournamentRangeComponent,
    host: CustomHostComponent,
    imports: [
      CommonModule,
      NoopAnimationsModule,
      ...RANGE_MODULES
    ],
    declarations: [
      TournamentRangeComponent
    ],
    providers: [
      TournamentFormService,
      CurrenciesManagerService,
      { provide: JpnService, useClass: MockJpnService },]
  });

  const currency: FeatureCurrency[] = [{
    code: 'CNY',
    rate: 2,
    currentRate: 3
  }];

  let spectatorService: SpectatorService<TournamentFormService>;
  const createService = createServiceFactory({
    service: TournamentFormService
  });

  let currenciesManagerService: SpectatorService<CurrenciesManagerService>;
  const createCurrenciesManagerService = createServiceFactory({
    service: CurrenciesManagerService,
  });

  beforeEach(() => {
    spectator = createHost(`<sw-tournament-range [formControl]="form"></sw-tournament-range>`);
    spectatorService = createService();
    currenciesManagerService = createCurrenciesManagerService();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();

    expect(spectatorService.service).toBeTruthy();
    expect(spectatorService.service).toBeDefined();

    expect(currenciesManagerService).toBeTruthy();
    expect(currenciesManagerService).toBeDefined();
  });

  it('form Submitted', () => {
    spectatorService.service.formSubmitted = true;
    expect(spectator.component.submitted).toEqual(true);
  });

  it('form add control', () => {
    spectatorService.service.baseCurrency = 'CNY';
    currenciesManagerService.service.selectedCurrencies = currency;

    expect(spectator.component.baseCurrency).toEqual('CNY');
    expect(spectator.component.selectedCurrencies).toEqual(currency);
    expect(spectator.component.form.get(currency[0].code)?.value).toBeNull();
    expect(spectator.component.selectedTabIndex).toEqual(0);
  });

  it('new form control disabled for non base currency', () => {
    spectatorService.service.baseCurrency = 'USD';
    currenciesManagerService.service.selectedCurrencies = currency;
    expect(spectator.component.form.get(currency[0].code)?.disabled).toEqual(true);
  });

  it('new form control disabled is isDisabled', () => {
    spectator.component.isDisabled = true;
    spectatorService.service.baseCurrency = 'USD';
    currenciesManagerService.service.selectedCurrencies = currency;

    expect(spectator.component.form.get(currency[0].code)?.disabled).toEqual(true);
  });

  it('is base currency', () => {
    spectator.component.baseCurrency = 'USD';
    expect(spectator.component.isBaseCurrency('CNY')).toEqual(false);
    expect(spectator.component.isBaseCurrency('USD')).toEqual(true);
  });

  it('get currency', () => {
    spectator.component.selectedCurrencies = currency;
    spectator.component.baseCurrency = 'USD';

    expect(spectator.component.getCurrency('CNY')).toEqual(currency[0]);
    expect(spectator.component.getCurrency('USD')).toEqual(
      {
        code: 'USD',
        rate: 1
      });
  });

  it('select tab', () => {
    expect(spectator.component.selectedTabIndex).toEqual(0);
    spectator.component.selectTab(2);
    expect(spectator.component.selectedTabIndex).toEqual(2);
  });
});
