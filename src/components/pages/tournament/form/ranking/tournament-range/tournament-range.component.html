<form [formGroup]="form">
  <div class="sw-tab">
    <div class="sw-tab__header">
      <div
        *ngFor="let control of currenciesControls; let i=index"
        class="sw-tab__th"
        (click)="selectTab(i)"
        [ngClass]="{
        'sw-tab__th--active': i === selectedTabIndex,
        'sw-tab__th--error': control.invalid && submitted && isBaseCurrency(getName(control))
        }">
        {{getName(control)}}
      </div>
    </div>
    <div class="sw-tab__body">
        <div
          *ngFor="let control of currenciesControls; let i=index" class="sw-tab__content"
          [ngClass]="{'sw-tab__content--hidden': i !== selectedTabIndex}">
          <sw-tournament-range-group
            [formControl]="control"
            [isBaseCurrency]="isBaseCurrency(getName(control))"
            [isActionsDisabled]="isDisabled"
            [currency]="getCurrency(getName(control))">
          </sw-tournament-range-group>
        </div>
    </div>
  </div>
</form>
