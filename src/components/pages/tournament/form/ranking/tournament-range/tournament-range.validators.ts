import { AbstractControl, FormControl, FormGroup, ValidationErrors } from '@angular/forms';

export function validateMaxRangeItem( control: AbstractControl ): ValidationErrors | null {
  const form = control as FormGroup;
  let { max, min } = form.getRawValue();
  const maxControl = form.get('max') as FormControl;
  const maxErrors = maxControl.errors || {};
  if (typeof min === 'string') {
    min = parseFloat(min);
  }
  if (max !== null && min > max) {
    maxControl.setErrors(Object.assign(maxErrors, { 'lessThanMax': true }));
  } else {
    if ('lessThanMax' in maxErrors) {
      delete maxErrors['lessThanMax'];
      if (Object.keys(maxErrors).length === 0) {
        maxControl.setErrors(null);
      }
    }
  }
  return null;
}
