import { Component, forwardRef, OnInit } from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormBuilder,
  FormControl,
  FormGroup,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ValidationErrors,
} from '@angular/forms';
import { combineLatest } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';

import { BaseComponent } from '../../../../../../common/components/base.component';
import { CurrenciesManagerService } from '../../../../../../common/components/currencies-manager/currencies-manager.service';
import { FeatureCurrency } from '../../../../interfaces/feature';
import {
  FormTournamentRange,
  TournamentFormService
} from '../../../tournament-update/tournament-form-service/tournament-form.service';


@Component({
  selector: 'sw-tournament-range',
  templateUrl: './tournament-range.component.html',
  styleUrls: ['./tournament-range.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TournamentRangeComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => TournamentRangeComponent),
      multi: true
    },
  ]
})
export class TournamentRangeComponent extends BaseComponent implements OnInit, ControlValueAccessor {
  form: FormGroup;
  baseCurrency?: string;
  selectedCurrencies: FeatureCurrency[] = [];
  selectedTabIndex = 0;
  isDisabled = false;
  submitted = false;
  onChange: ( _: any ) => void = (() => {
  });

  constructor( private readonly fb: FormBuilder,
               private readonly formService: TournamentFormService,
               private readonly currenciesManagerService: CurrenciesManagerService,
  ) {
    super();
    this.form = this.initForm();
  }

  ngOnInit(): void {
    this.formService.formSubmitted$
      .pipe(
        filter( val => val),
        takeUntil(this.destroyed)
      ).subscribe( submitted => {
        this.submitted = submitted;
        this.form.markAllAsTouched();
      });

    this.form.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(() => {
        const formValue = this.form.getRawValue();
        this.onChange(formValue);
      });

    combineLatest([
      this.formService.baseCurrency$,
      this.currenciesManagerService.selectedCurrencies$
    ])
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( [baseCurrency, currencies] ) => {
        const sourceFormBaseControlValue = this.baseCurrency ? this.form.getRawValue()[this.baseCurrency] : null;
        const sourceBaseCurrency = this.baseCurrency;
        this.baseCurrency = baseCurrency;
        this.selectedCurrencies = currencies;
        this.clearForm();
        const currenciesCodes = currencies.map(el => el.code);
        const allCurrencies: string[] = baseCurrency ? [baseCurrency, ...currenciesCodes] : currenciesCodes;
        allCurrencies.forEach(( currency: string ) => {
          const control = new FormControl();
          if (sourceBaseCurrency === baseCurrency && currency === sourceBaseCurrency) {
            control.patchValue(sourceFormBaseControlValue);
          }
          if (this.isDisabled || (currency !== baseCurrency)) {
            control.disable();
          } else {
            control.enable();
          }

          this.form.addControl(currency, control);
        });

        this.selectedTabIndex = 0;
      });
  }

  writeValue( val: FormTournamentRange ): void {
    this.buildForm(this.selectedCurrencies, this.baseCurrency);
    if (val) {
      this.form.patchValue(val);
    }
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState?( isDisabled: boolean ): void {
    this.isDisabled = isDisabled;
    isDisabled ? this.form.disable() : this.form.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid ? null : { invalidForm: { valid: false } };
  }

  isBaseCurrency( currency: string ): boolean {
    return currency === this.baseCurrency;
  }

  getCurrency( code: string ): FeatureCurrency | undefined {
    if (code === this.baseCurrency) {
      return {
        code,
        rate: 1
      };
    }
    return this.selectedCurrencies.find(( el: FeatureCurrency ) => el.code === code);
  }

  selectTab( i: number ) {
    this.selectedTabIndex = i;
  }

  get currenciesControls(): FormControl[] {
    return Object.keys(this.form.controls).map( key => this.form.get(key) as FormControl);
  }

  getName(control: AbstractControl): string | null {
    let group = control.parent as FormGroup;
    if (!group) {
      return null;
    }
    let name = null;
    Object.keys(group.controls).forEach(key => {
      let childControl = group.get(key);
      if (childControl !== control) {
        return;
      }
      name = key;
    });

    return name;
  }

  private buildForm( currencies: FeatureCurrency[], baseCurrency?: string ): void {
    this.clearForm();

    const currenciesCodes = currencies.map(el => el.code);
    const allCurrencies: string[] = baseCurrency ? [baseCurrency, ...currenciesCodes] : currenciesCodes;
    allCurrencies.forEach(( currency: string ) => {
      const control = new FormControl();
      this.isDisabled ? control.disable() : control.enable();
      this.form.addControl(currency, control);
    });
  }

  private clearForm(): void {
    Object.keys(this.form.controls).forEach(key => {
      this.form.removeControl(key);
    });
  }

  private initForm(): FormGroup {
    return this.fb.group({});
  }
}
