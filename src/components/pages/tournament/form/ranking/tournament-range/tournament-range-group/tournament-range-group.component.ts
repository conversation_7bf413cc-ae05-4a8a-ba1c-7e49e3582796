import { Component, forwardRef, Input, OnInit } from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ValidationErrors,
  Validators
} from '@angular/forms';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { delay, filter, takeUntil, tap } from 'rxjs/operators';

import { BaseComponent } from '../../../../../../../common/components/base.component';
import {
  fractionsNumbersLengthValidator,
  numberMaxLength,
  numbersOnlyValidator
} from '../../../../../../../common/lib/validators';
import { FeatureCurrency } from '../../../../../interfaces/feature';
import { TournamentRange } from '../../../../../interfaces/tournament';
import { TournamentFormService } from '../../../../tournament-update/tournament-form-service/tournament-form.service';
import { validateMaxRangeItem } from '../tournament-range.validators';
import { formatFloat } from '../../../../../../../common/lib/format-float';
import { SettingsService } from '@skywind-group/lib-swui';

@Component({
  selector: 'sw-tournament-range-group',
  templateUrl: './tournament-range-group.component.html',
  styleUrls: ['./tournament-range-group.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TournamentRangeGroupComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => TournamentRangeGroupComponent),
      multi: true
    },
  ]
})
export class TournamentRangeGroupComponent extends BaseComponent implements OnInit, ControlValueAccessor {
  @Input()
  set currency( val: FeatureCurrency | undefined ) {
    this._currency$.next(val || undefined);
  }

  get currency(): FeatureCurrency | undefined {
    return this._currency$.value;
  }

  @Input() isActionsDisabled?: boolean;

  @Input() isBaseCurrency = false;
  form: FormGroup;
  isDisabled = false;
  inputValidators: any[] = [
    numbersOnlyValidator,
    fractionsNumbersLengthValidator,
    numberMaxLength(12),
  ];
  onChange: ( _: any ) => void = (() => {
  });
  private _currency$ = new BehaviorSubject<FeatureCurrency | undefined>(undefined);

  constructor( private readonly fb: FormBuilder,
               private readonly formService: TournamentFormService,
               readonly settingsService: SettingsService
  ) {
    super();
    this.form = this.initForm();
  }

  ngOnInit(): void {
    combineLatest([this.formService.baseCurrencyControlValue$, this._currency$])
      .pipe(
        filter(() => !this.isBaseCurrency),
        takeUntil(this.destroyed)
      ).subscribe(( [baseCurrencyControlValue, currency] ) => {
      this.rangesArray.clear();
      baseCurrencyControlValue.forEach(( range: TournamentRange ) => {
        const control = this.initRangesItem();
        const rate = currency?.rate || 1;
        const ratedRange = {
          min: range.min || range.min === 0 ? (range.min * rate).toFixed(2) : null,
          max: range.max || range.max === 0 ? (range.max * rate).toFixed(2) : null,
          minQualifyAmount: range.minQualifyAmount || range.minQualifyAmount === 0 ? (range.minQualifyAmount * rate).toFixed(2) : null
        };
        control.patchValue(ratedRange);
        this.rangesArray.push(control);
      });
    });

    this.form.valueChanges
      .pipe(
        tap(() => {
          this.setMaxNextMinValue();
          this.toggleValidators(this.rangesArray);
        }),
        delay(0),
        takeUntil(this.destroyed)
      ).subscribe(() => {
      const formValue = this.form.getRawValue();
      const processedRanges = formValue.ranges.map(( el: any ) => {
        return {
          min: el.min || el.min === 0 ? parseFloat(el.min) : null,
          max: el.max || el.max === 0 ? parseFloat(el.max) : null,
          minQualifyAmount: el.minQualifyAmount || el.minQualifyAmount === 0 ? parseFloat(el.minQualifyAmount) : null
        };
      });

      if (this.isBaseCurrency) {
        this.formService.baseCurrencyControlValue = processedRanges;
      }
      this.onChange(processedRanges);
    });

    this.formService.addRange$
      .pipe(
        filter(val => val),
        takeUntil(this.destroyed)
      ).subscribe(() => {
      this.rangesArray.push(this.initRangesItem());
    });

    this.formService.removeRange$
      .pipe(
        filter(val => !!val),
        takeUntil(this.destroyed)
      ).subscribe(( i: number ) => {
      this.rangesArray.removeAt(i);
    });
  }

  getSimpleValue( i: number, controlName: string, group: AbstractControl ): string {
    const control = group.get(controlName) as FormControl;
    if (i === this.rangesArray.length - 1 && controlName === 'max' && !control?.value) {
      return 'MAX';
    }

    return control.value || '0';
  }

  getPlaceholder( i: number, controlName: string ): string {
    if (i === this.rangesArray.length - 1 && controlName === 'max') {
      return 'MAX';
    }

    return '0';
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( isDisabled: boolean ): void {
    this.isDisabled = isDisabled;
    isDisabled ? this.form.disable() : this.form.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid ? null : { invalidForm: { valid: false } };
  }

  writeValue( val: TournamentRange[] ): void {
    this.rangesArray.clear();
    if (!val) {
      this.rangesArray.push(this.initRangesItem());
      this.rangesArray.push(this.initRangesItem());
    } else {
      val.forEach(( range: TournamentRange ) => {
        const control = this.initRangesItem();
        control.patchValue(range);
        this.rangesArray.push(control);
      });
    }
  }

  get rangesArray(): FormArray {
    return this.form.get('ranges') as FormArray;
  }

  onDeleteRange( i: number ) {
    this.formService.removeRange = i;
  }

  addBetRange() {
    this.formService.addRange = true;
  }

  private initForm(): FormGroup {
    const form = this.fb.group({
      ranges: this.fb.array([
        this.initRangesItem(),
        this.initRangesItem()
      ])
    });
    this.toggleValidators(form.get('ranges') as FormArray);
    return form;
  }

  private initRangesItem(): FormGroup {
    return this.fb.group({
      min: [null, Validators.compose(this.inputValidators)],
      max: [null],
      minQualifyAmount: [null, Validators.compose([Validators.required, ...this.inputValidators])]
    });
  }

  private setMaxNextMinValue() {
    this.rangesArray.controls.forEach(( range: AbstractControl, index: number ) => {
      const currentRangeGroup = range as FormGroup;
      const currentMinControl = currentRangeGroup.get('min') as FormControl;
      const prevRangeGroup = this.rangesArray.controls[index - 1] as FormGroup;
      if (prevRangeGroup) {
        const prevMaxControl = prevRangeGroup.get('max') as FormControl;
        currentMinControl.patchValue(formatFloat(prevMaxControl.value), { onlySelf: true });
      }
    });
  }

  private toggleValidators( rangesArray: FormArray ) {
    rangesArray.controls.forEach(( group: AbstractControl, index ) => {
      const minControl = group.get('min') as FormControl;
      const maxControl = group.get('max') as FormControl;
      index > 0 || this.isDisabled ? minControl.disable({ onlySelf: true }) : minControl.enable({ onlySelf: true });

      if (index === rangesArray.controls.length - 1) {
        maxControl.setValidators(Validators.compose(this.inputValidators));
      } else {
        maxControl.setValidators(Validators.compose([Validators.required, ...this.inputValidators]));
      }
      maxControl.updateValueAndValidity({ onlySelf: true });
      group.setValidators(validateMaxRangeItem);
      group.updateValueAndValidity({ emitEvent: false });
    });
  }
}
