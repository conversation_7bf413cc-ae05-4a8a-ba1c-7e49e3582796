import { CommonModule } from '@angular/common';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TournamentFormService } from '../../../../tournament-update/tournament-form-service/tournament-form.service';
import { TournamentRangeErrorsComponent } from '../tournament-range-errors/tournament-range-errors.component';

import { TournamentRangeGroupComponent } from './tournament-range-group.component';
import { TOURNAMENT_RANGE_GROUP_MODULES } from './tournament-range-group.module';
import { SettingsService } from '@skywind-group/lib-swui';
import { Component } from '@angular/core';
import { FormControl } from '@angular/forms';
import { createHostFactory, createServiceFactory, SpectatorHost, SpectatorService } from '@ngneat/spectator';
import { TournamentRange } from '../../../../../interfaces/tournament';
import { FeatureCurrency } from '../../../../../interfaces/feature';

@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}

describe('TournamentRangeGroupComponent', () => {
  let spectator: SpectatorHost<TournamentRangeGroupComponent, CustomHostComponent>;
  const createHost = createHostFactory({
    component: TournamentRangeGroupComponent,
    host: CustomHostComponent,
    imports: [
      CommonModule,
      NoopAnimationsModule,
      ...TOURNAMENT_RANGE_GROUP_MODULES,
    ],
    declarations: [
      TournamentRangeGroupComponent,
      TournamentRangeErrorsComponent
    ],
    providers: [TournamentFormService, SettingsService]
  });

  let spectatorService: SpectatorService<TournamentFormService>;
  const createService = createServiceFactory({
    service: TournamentFormService
  });

  const currency: FeatureCurrency = {
    code: 'CNY',
    rate: 2,
    currentRate: 3
  };

  const ranges: TournamentRange[] = [
    {
      min: 1.00,
      max: 10.00,
      minQualifyAmount: 10.00
    }
  ];

  beforeEach(() => {
    spectator = createHost(`<sw-tournament-range-group [formControl]="form"></sw-tournament-range-group>`);
    spectatorService = createService();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();

    expect(spectatorService.service).toBeTruthy();
    expect(spectatorService.service).toBeDefined();
  });

  it('rangesArray patched when currency is exist', () => {
    spectatorService.service.baseCurrencyControlValue = ranges;
    spectator.component.currency = currency;
    const newRangeData = [{
      min: (ranges[0].min * currency.rate).toFixed(2),
      max: (ranges[0].max * currency.rate).toFixed(2),
      minQualifyAmount: (ranges[0].minQualifyAmount * currency.rate).toFixed(2),
    }];
    expect(spectator.component.rangesArray.value).toEqual(newRangeData);
  });

  it('rangesArray add range', () => {
    spectatorService.service.addRange = true;
    spectatorService.service.addRange = true;
    expect(spectator.component.rangesArray.value.length).toEqual(2);
  });

  it('rangesArray add bet range', () => {
    spectator.component.addBetRange();
    spectator.component.addBetRange();
    expect(spectator.component.rangesArray.value.length).toEqual(2);
  });

  it('rangesArray remove bet range', () => {
    spectator.component.addBetRange();
    spectator.component.addBetRange();
    expect(spectator.component.rangesArray.value.length).toEqual(2);

    spectator.component.onDeleteRange(1);
    expect(spectator.component.rangesArray.value.length).toEqual(1);
  });

  it('rangesArray remove range', () => {
    spectator.component.addBetRange();
    spectator.component.addBetRange();
    expect(spectator.component.rangesArray.value.length).toEqual(2);

    spectatorService.service.removeRange = 1;
    expect(spectator.component.rangesArray.value.length).toEqual(1);
  });

  it('rangesArray get simple value for max control', () => {
    spectator.component.addBetRange();
    spectator.component.addBetRange();
    expect(spectator.component.getSimpleValue(1, 'max', spectator.component.rangesArray.controls[1])).toEqual('MAX');
  });

  it('rangesArray get simple value for min control', () => {
    spectatorService.service.addRange = true;
    expect(spectator.component.getSimpleValue(0, 'min', spectator.component.rangesArray.controls[0])).toEqual('0');
  });

  it('rangesArray get placeholder value for max control', () => {
    spectator.component.addBetRange();
    spectator.component.addBetRange();
    expect(spectator.component.getPlaceholder(1, 'max')).toEqual('MAX');
  });

  it('rangesArray get placeholder value for min control', () => {
    spectator.component.addBetRange();
    spectator.component.addBetRange();
    expect(spectator.component.getPlaceholder(1, 'min')).toEqual('0');
  });

  it('rangesArray set disabled state', () => {
    spectatorService.service.addRange = true;
    spectator.component.setDisabledState(true);
    expect(spectator.component.isDisabled).toEqual(true);
    expect(spectator.component.form.disabled).toEqual(true);
  });

  it('rangesArray patch empty array', () => {
    spectator.component.currency = currency;
    spectator.hostComponent.form.patchValue(null);
    expect(spectator.component.rangesArray.value.length).toEqual(2);
  });

  it('rangesArray write value', () => {
    spectator.hostComponent.form.patchValue(ranges);
    expect(spectator.component.rangesArray.value.length).toEqual(1);
    expect(spectator.component.rangesArray.value[0]).toEqual({
      min: 1.00,
      max: 10.00,
      minQualifyAmount: 10.00
    });
  });
});
