import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import {
  SwuiControlMessagesModule,
  SwuiCurrencySymbolModule,
  SwuiIsControlInvalidModule
} from '@skywind-group/lib-swui';
import { FloatFormatterModule } from '../../../../../../../common/directives/floatFormatter/floatFormatter.module';
import { TournamentRangeErrorsComponent } from '../tournament-range-errors/tournament-range-errors.component';
import { TournamentRangeGroupComponent } from './tournament-range-group.component';
import { PipesModule } from '../../../../../../../common/pipes/pipes.module';
import { SettingCurrencyFormatterModule } from '../../../../../../../common/directives/appSettingCurrencyFormatter/settingCurrencyFormatter.module';

export const TOURNAMENT_RANGE_GROUP_MODULES = [
  ReactiveFormsModule,
  MatFormFieldModule,
  MatInputModule,
  MatButtonModule,
  MatIconModule,
  FloatFormatterModule,
  SwuiCurrencySymbolModule,
  SwuiControlMessagesModule,
  SwuiIsControlInvalidModule,
  PipesModule,
];

@NgModule({
  declarations: [
    TournamentRangeGroupComponent,
    TournamentRangeErrorsComponent,
  ],
  exports: [
    TournamentRangeGroupComponent,
    TournamentRangeErrorsComponent,
  ],
    imports: [
        CommonModule,
        ...TOURNAMENT_RANGE_GROUP_MODULES,
        SettingCurrencyFormatterModule,
    ]
})
export class TournamentRangeGroupModule {
}
