<div class="sw-range-group">
  <div class="sw-range-group__errors">
    <sw-tournament-range-errors [ranges]="rangesArray"></sw-tournament-range-errors>
  </div>
  <form [formGroup]="form">
    <table class="sw-mat-table ranges-table">
      <thead>
      <tr>
        <th></th>
        <th>From bet</th>
        <th>To bet</th>
        <th>Total bet amount<br />to qualify</th>
        <th class="ranges-table__action"></th>
      </tr>
      </thead>
      <tbody formArrayName="ranges">
        <tr *ngFor="let range of rangesArray.controls; let i=index;" [formGroup]="range">
          <td>Range {{i + 1}}</td>
          <td>
            <mat-form-field
              *ngIf="isBaseCurrency; else disabledMin"
              class="ranges-table__field no-field-padding"
              [ngClass]="{'readonly': i !== 0}"
              appearance="outline">
              <span matPrefix>{{currency?.code | currencySymbol}}</span>
              <input
                [readonly]="i !== 0"
                matInput
                type="number"
                min="0"
                [placeholder]="getPlaceholder(i, 'min')"
                formControlName="min"
                swCurrencyFormatter [currencyCode]="currency?.code">
            </mat-form-field>
            <ng-template #disabledMin>
              {{currency?.code | currencySymbol}} {{getSimpleValue(i, 'min', range) | currencyFormat: currency?.code }}
            </ng-template>
          </td>

          <td>
            <mat-form-field
              *ngIf="isBaseCurrency; else disabledMax"
              class="ranges-table__field no-field-padding"
              appearance="outline">
              <span matPrefix>{{currency?.code | currencySymbol}}</span>
              <input
                matInput
                type="number"
                min="0"
                [placeholder]="getPlaceholder(i, 'max')"
                formControlName="max"
                swCurrencyFormatter [currencyCode]="currency?.code">
            </mat-form-field>
            <ng-template #disabledMax>
              {{currency?.code | currencySymbol}} {{getSimpleValue(i, 'max', range) | currencyFormat: currency?.code }}
            </ng-template>
          </td>

          <td>
            <mat-form-field
              *ngIf="isBaseCurrency; else disabledQualify"
              class="ranges-table__field no-field-padding"
              appearance="outline">
              <span matPrefix>{{currency?.code | currencySymbol}}</span>
              <input
                matInput
                type="number"
                min="0"
                [placeholder]="getPlaceholder(i, 'minQualifyAmount')"
                formControlName="minQualifyAmount"
                swCurrencyFormatter [currencyCode]="currency?.code">
            </mat-form-field>
            <ng-template #disabledQualify>
              {{currency?.code | currencySymbol}} {{getSimpleValue(i, 'minQualifyAmount', range) | currencyFormat: currency?.code }}
            </ng-template>
          </td>

          <td class="ranges-table__action">
            <button
              *ngIf="rangesArray?.controls.length > 2"
              mat-icon-button
              [disabled]="!!isActionsDisabled"
              (click)="onDeleteRange(i)">
              <mat-icon>close</mat-icon>
            </button>
          </td>
        </tr>
      </tbody>
    </table>
    <button
      *ngIf="rangesArray?.controls.length < 10"
      mat-button
      [disabled]="!!isActionsDisabled"
      color="primary"
      class="ranges-table__add mat-button-md"
      (click)="addBetRange()">
      <mat-icon>add</mat-icon>
      Add bet range
    </button>
  </form>
</div>
