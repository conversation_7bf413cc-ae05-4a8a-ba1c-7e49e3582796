<form [formGroup]="form">
  <div class="mat-card margin-bottom20">

    <h3 class="mat-title">{{ 'TOURNAMENT.FORM.PAYOUT.configuration' | translate }}</h3>

    <div fxLayout.lt-sm="column" fxLayout="row">
      <div fxFlex.lt-sm="100" fxFlex="145px" fxFlexAlign="center">
        {{'TOURNAMENT.FORM.PAYOUT.baseCurrency' | translate}}
      </div>
      <div class="form-field" fxFlex.lt-sm="100">
        <div class="form-field__input">
          <mat-form-field class="form-field__mat-field no-field-padding" appearance="outline">
            <lib-swui-select
              formControlName="baseCurrency"
              required
              [showSearch]="true"
              [disableEmptyOption]="true"
              [data]="processedCurrencies">
            </lib-swui-select>
            <mat-error class="form-field__error">
              <lib-swui-control-messages [control]="baseCurrencyControl"></lib-swui-control-messages>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>
  </div>

  <div class="mat-card margin-bottom32">
    <h3 class="mat-title">Ranking Formula</h3>
    <div class="margin-bottom16">The ranking formula defines how players accumulate points in the tournament.</div>
    <sw-tournament-ranking-formula [formControl]="rankingFormulaControl"></sw-tournament-ranking-formula>
  </div>

  <div class="mat-card margin-bottom32">
    <h3 class="mat-title">Bet Range <mat-slide-toggle [formControl]="rangeToggleControl"></mat-slide-toggle></h3>
    <ng-container *ngIf="!!rangeToggleControl?.value; else emptyRangesTpl">
      <div class="margin-bottom16">Rank all players separately by the bet range. There were separate payout tables for each bet range.</div>
      <sw-tournament-range [formControl]="rangesControl"></sw-tournament-range>
    </ng-container>
    <ng-template #emptyRangesTpl>
      Rank all players separately by the bet range. There will be separate payout tables for each bet range.
    </ng-template>
  </div>

  <div class="mat-card margin-bottom32">
    <h3 class="mat-title">Qualifying Bets</h3>
    <sw-qualifying-bets
      [baseCurrency]="baseCurrencyControl?.value"
      [isPrizeMultiplierSupported]="!!prizeMultiplierSupported"
      [selectedCurrencies]="selectedCurrencies"
      [isTotalBetAmountSupported]="true"
      [rangesEnabled]="rangesEnabled"
      [formControl]="qualifyingBetsControl">
    </sw-qualifying-bets>
  </div>
</form>

