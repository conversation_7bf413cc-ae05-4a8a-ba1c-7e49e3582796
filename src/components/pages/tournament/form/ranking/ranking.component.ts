import { Component, forwardRef, Input, OnInit } from '@angular/core';
import {
  ControlValueAccessor, FormBuilder, FormControl, FormGroup, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors,
  Validators
} from '@angular/forms';
import { filter, pluck, takeUntil } from 'rxjs/operators';
import { BaseComponent } from '../../../../../common/components/base.component';
import { CurrenciesManagerService } from '../../../../../common/components/currencies-manager/currencies-manager.service';
import { CurrencyModel } from '../../../../../common/models/currency.model';
import { FeatureCurrency } from '../../../interfaces/feature';
import { TournamentRanking, TournamentRankingFormulaType } from '../../../interfaces/tournament';
import {
  FormTournamentRange, TournamentFormService
} from '../../tournament-update/tournament-form-service/tournament-form.service';

@Component({
  selector: 'sw-ranking',
  templateUrl: './ranking.component.html',
  styleUrls: ['./ranking.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RankingComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => RankingComponent),
      multi: true
    }
  ]
})

export class RankingComponent extends BaseComponent implements ControlValueAccessor, OnInit {
  @Input('currencies')
  set currenciesList( value: CurrencyModel[] | undefined ) {
    if (!value) {
      return;
    }
    this.processedCurrencies = value.map(( { code } ) => ({ id: code, text: code }));
  }

  processedCurrencies: { id: string, text: string }[] = [];
  selectedCurrencies: FeatureCurrency[] = [];
  form: FormGroup;
  rangeToggleControl = new FormControl();
  isDisabled = false;
  prizeMultiplierSupported = false;
  rangesEnabled = false;
  onChange: ( _: any ) => void = (() => {
  });

  constructor( private readonly fb: FormBuilder,
               private readonly formService: TournamentFormService,
               private readonly currenciesManagerService: CurrenciesManagerService,
  ) {
    super();
    this.form = this.initForm();
  }

  ngOnInit(): void {
    this.rangeToggleControl.valueChanges
      .pipe(
        filter(() => !this.isDisabled),
        takeUntil(this.destroyed)
      )
      .subscribe(( val: boolean ) => {
        this.rangesEnabled = val;
        if (val) {
          this.rangesControl.enable();
        } else {
          this.rangesControl.disable();
        }
        this.rangesControl.reset(null, { emitEvent: false });
        this.form.updateValueAndValidity();
      });

    this.currenciesManagerService.selectedCurrencies$
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: FeatureCurrency[] ) => {
        this.selectedCurrencies = val;
      });

    this.rankingFormulaControl.valueChanges
      .pipe(
        pluck('type'),
        takeUntil(this.destroyed)
      )
      .subscribe(( val: TournamentRankingFormulaType ) => {
        this.formService.rankingFormulaType = val;
        this.prizeMultiplierSupported = this.formService.isPrizeMultiplierSupported;
      });

    this.rangesControl.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: FormTournamentRange ) => {
        this.formService.ranges = this.rangesEnabled ? val : null;
      });

    this.baseCurrencyControl.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: string ) => {
        this.formService.baseCurrency = val;
      });

    this.form.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(( val: TournamentRanking ) => {
        if (val) {
          val.ranges = val.ranges || null;
          val.qualifyingBets = val.qualifyingBets || null;
        }
        this.onChange(val);
      });
  }

  writeValue( val: TournamentRanking ): void {
    if (!val) {
      return;
    }

    this.rangeToggleControl.setValue(!!val.ranges);

    this.form.patchValue(val);
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: any ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState?( isDisabled: boolean ): void {
    this.isDisabled = isDisabled;
    if (isDisabled) {
      this.form.disable();
      this.rangeToggleControl.disable();
    } else {
      this.form.enable();
      this.rangeToggleControl.enable();
    }
  }

  validate(): ValidationErrors | null {
    return this.form.valid ? null : { invalidForm: { valid: false } };
  }

  get rankingFormulaControl(): FormControl {
    return this.form.get('rankingFormula') as FormControl;
  }

  get baseCurrencyControl(): FormControl {
    return this.form.get('baseCurrency') as FormControl;
  }

  get rangesControl(): FormControl {
    return this.form.get('ranges') as FormControl;
  }

  get qualifyingBetsControl(): FormControl {
    return this.form.get('qualifyingBets') as FormControl;
  }

  private initForm(): FormGroup {
    return this.fb.group({
      baseCurrency: ['', Validators.required],
      rankingFormula: [],
      qualifyingBets: [],
      ranges: [],
    });
  }
}
