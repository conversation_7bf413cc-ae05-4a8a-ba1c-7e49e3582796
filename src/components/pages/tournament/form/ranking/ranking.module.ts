import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { QualifyingBetsModule } from '../../../../../common/components/qualifying-bets/qualifying-bets.module';
import { RankingComponent } from './ranking.component';
import { TournamentRangeModule } from './tournament-range/tournament-range.module';
import { TournamentRankingFormulaModule } from './tournament-ranking-formula/tournament-ranking-formula.module';

export const RANKING_MODULES = [
  ReactiveFormsModule,
  MatFormFieldModule,
  MatSlideToggleModule,
  SwuiSelectModule,
  FlexLayoutModule,
  SwuiControlMessagesModule,
  TournamentRangeModule,
  TournamentRankingFormulaModule,
  QualifyingBetsModule,
];


@NgModule({
  declarations: [RankingComponent],
  exports: [RankingComponent],
  imports: [
    CommonModule,
    TranslateModule,
    ...RANKING_MODULES,
  ]
})
export class RankingModule {
}
