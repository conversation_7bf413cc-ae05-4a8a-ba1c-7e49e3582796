import { CommonModule } from '@angular/common';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { CurrenciesManagerService } from '../../../../../common/components/currencies-manager/currencies-manager.service';
import { TournamentFormService } from '../../tournament-update/tournament-form-service/tournament-form.service';
import { RankingComponent } from './ranking.component';
import { RANKING_MODULES } from './ranking.module';
import { JpnService, MockJpnService } from '../../../../../common/services/jpn.service';
import { Component } from '@angular/core';
import { FormControl } from '@angular/forms';
import { createHostFactory, createServiceFactory, SpectatorHost, SpectatorService } from '@ngneat/spectator';

@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}
describe('TournamentRankingComponent', () => {
  let spectator: SpectatorHost<RankingComponent, CustomHostComponent>;
  const createHost = createHostFactory({
    component: RankingComponent,
    host: CustomHostComponent,
    imports: [
      CommonModule,
      NoopAnimationsModule,
      TranslateModule.forRoot(),
      ...RANKING_MODULES,
    ],
    declarations: [RankingComponent],
    providers: [
      TournamentFormService,
      CurrenciesManagerService,
      { provide: JpnService, useClass: MockJpnService },
    ]
  });

  let spectatorService: SpectatorService<TournamentFormService>;
  const createService = createServiceFactory({
    service: TournamentFormService
  });

  let currenciesManagerService: SpectatorService<CurrenciesManagerService>;
  const createCurrenciesManagerService = createServiceFactory({
    service: CurrenciesManagerService,
  });

  const selectedCurrencies = [{ code: 'USD', rate: 2 }];

  beforeEach(() => {
    spectator = createHost(`<sw-ranking [formControl]="form"></sw-ranking>`);
    spectatorService = createService();
    currenciesManagerService = createCurrenciesManagerService();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();

    expect(spectatorService.service).toBeTruthy();
    expect(spectatorService.service).toBeDefined();
    expect(currenciesManagerService).toBeTruthy();
    expect(currenciesManagerService).toBeDefined();
  });

  it('range toggle false', () => {
    spectator.component.rangeToggleControl.setValue(false);
    expect(spectator.component.rangesEnabled).toEqual(false);
    expect(spectator.component.rangesControl.disabled).toEqual(true);
    expect(spectator.component.rangesControl.value).toBeNull();
  });

  it('range toggle true', () => {
    spectator.component.rangeToggleControl.setValue(true);
    expect(spectator.component.rangesEnabled).toEqual(true);
    expect(spectator.component.rangesControl.enabled).toEqual(true);
    expect(spectator.component.rangesControl.value).toBeNull();
  });

  it('get selected currencies', () => {
    currenciesManagerService.service.selectedCurrencies = selectedCurrencies;
    expect(spectator.component.selectedCurrencies).toEqual(selectedCurrencies);
  });

  it('get selected currencies', () => {
    spectator.hostComponent.form.setValue({
      baseCurrency: 'AUD',
      rankingFormula: {
        type: 'highestTotalWin',
        winPoints: 2
      },
      ranges: [
        {
          AUD: {
            order: null,
            min: 1,
            max: 2,
            minQualifyAmount: 3
          }
        },
      ],
      qualifyingBets: null
    });

    expect(spectator.component.prizeMultiplierSupported).toEqual(false);
  });
});
