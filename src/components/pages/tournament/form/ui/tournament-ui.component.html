<div class="mat-card mat-elevation-z0" [formGroup]="form">
  <h3 class="no-margin-top mat-title margin-bottom24">Assets configuration</h3>
  <mat-form-field appearance="outline" style="display: block; width: 100%">
    <textarea
      matInput
      rows="15"
      [formControl]="assetsControl"
      (blur)="onBlur()"
      style="overflow: auto !important">
    </textarea>
    <mat-error *ngIf="assetsControl.hasError('JSON')">
      {{'VALIDATION.JSON' | translate: {value: assetsControl.errors['JSON'].error} }}
    </mat-error>
    <mat-error *ngIf="assetsControl.hasError('isArray')">
      {{'Value should be array'}}
    </mat-error>
  </mat-form-field>
</div>
