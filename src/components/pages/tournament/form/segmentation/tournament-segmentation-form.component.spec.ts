import { Component } from '@angular/core';
import { FormControl } from '@angular/forms';
import { createHostFactory, createServiceFactory, SpectatorHost, SpectatorService } from '@ngneat/spectator';
import { CommonModule } from '@angular/common';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { of } from 'rxjs';
import { RestrictedCountriesModule } from '../../../../../common/components/restricted-countries/restricted-countries.module';
import { CountriesService } from '../../../../../common/services/countries.service';
import { TournamentFormService } from '../../tournament-update/tournament-form-service/tournament-form.service';
import { JpnService, MockJpnService } from '../../../../../common/services/jpn.service';
import { TournamentSegmentationFormComponent } from './tournament-segmentation-form.component';
import { SEGMENTATION_MODULES } from './tournament-segmentation-form.module';
import { CurrenciesManagerService } from '../../../../../common/components/currencies-manager/currencies-manager.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SwHubAuthService } from '@skywind-group/lib-swui';

@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}

describe('TournamentSegmentationFormComponent', () => {
  let spectator: SpectatorHost<TournamentSegmentationFormComponent, CustomHostComponent>;
  const createHost = createHostFactory({
    component: TournamentSegmentationFormComponent,
    host: CustomHostComponent,
    imports: [
      CommonModule,
      NoopAnimationsModule,
      ...SEGMENTATION_MODULES,
      TranslateModule.forRoot(),
      HttpClientTestingModule,
      RestrictedCountriesModule,
    ],
    declarations: [TournamentSegmentationFormComponent],
    providers: [
      SwHubAuthService,
      TournamentFormService,
      { provide: JpnService, useClass: MockJpnService },
      {
        provide: CountriesService, useValue: {
          countries: of([])
        }
      }
    ]
  });

  let spectatorService: SpectatorService<TournamentFormService>;
  const createService = createServiceFactory({
    service: TournamentFormService
  });

  let currenciesManagerService: SpectatorService<CurrenciesManagerService>;
  const createCurrenciesManagerService = createServiceFactory({
    service: CurrenciesManagerService,
  });

  beforeEach(() => {
    spectator = createHost(`<sw-tournament-segmentation-from [formControl]="form"></sw-tournament-segmentation-from>`);
    currenciesManagerService = createCurrenciesManagerService();
    spectatorService = createService();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();

    expect(spectatorService.service).toBeTruthy();
    expect(spectatorService.service).toBeDefined();
    expect(currenciesManagerService).toBeTruthy();
    expect(currenciesManagerService).toBeDefined();
  });

  it('brandsControl disabled', () => {
    spectator.component.status = 'expired';
    spectator.component.canPlayersEdit = true;

    expect(spectator.component.brandsControl.disabled).toBeTrue();
  });

  it('brandsControl enabled', () => {
    spectator.component.status = 'normal';
    spectator.component.canPlayersEdit = true;

    expect(spectator.component.brandsControl.enabled).toBeTrue();
  });

  it('baseCurrency has value', () => {
    spectatorService.service.baseCurrency = 'USD';

    expect(spectator.component.baseCurrency).toEqual('USD');
  });

  it('form value', () => {
    spectator.hostComponent.form.setValue({
      currencies: [{
        code: 'USD',
        currentRate: 1,
        rate: 1
      }],
      brands: [{
        id: 'test',
        players: ['test1', 'test2']
      }],
      restrictedCountries: null
    });

    expect(spectator.component.form.value).toEqual({
      currencies: [
        {
          code: 'USD',
          rate: 1
        }
      ],
      brands: [
        {
          id: 'test',
          players: [
            'test1',
            'test2'
          ]
        }
      ],
      restrictedCountries: null
    });
  });
});
