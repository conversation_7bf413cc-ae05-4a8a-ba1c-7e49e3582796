<form [formGroup]="form">
  <div class="mat-card mat-elevation-z0 margin-bottom20">
    <h3 class="no-margin-top mat-title">{{ 'TOURNAMENT.FORM.SEGMENTATION.currencies' | translate }}</h3>
    <sw-currencies-manager
      [formControl]="currenciesControl"
      [baseCurrency]="baseCurrency"
      [currencies]="currencies"
      [initialCurrencies]="initialCurrencies"
      [isDuplicate]="isDuplicate">
    </sw-currencies-manager>
    <div *ngIf="!currenciesControl.value || !currenciesControl.value?.length">
      <div class="currencies-error" *swIsControlInvalid="currenciesControl">
        {{'VALIDATION.currenciesRequired' | translate}}
      </div>
    </div>
  </div>
</form>

<div class="mat-card mat-elevation-z0 margin-bottom20">
  <h3 class="no-margin-top mat-title">{{ 'TOURNAMENT.FORM.SEGMENTATION.players' | translate }}</h3>
  <sw-players-filter [formControl]="brandsControl" [importDescription]="importDescription"></sw-players-filter>
  <sw-restricted-countries [formControl]="restrictedCountriesControl"></sw-restricted-countries>
</div>
