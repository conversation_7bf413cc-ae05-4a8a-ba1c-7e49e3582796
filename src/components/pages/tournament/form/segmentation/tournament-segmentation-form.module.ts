import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiChipsAutocompleteModule, SwuiIsControlInvalidModule } from '@skywind-group/lib-swui';
import { BrandsManagerModule } from '../../../../../common/components/brands-manager/brands-manager.module';
import { CurrenciesManagerModule } from '../../../../../common/components/currencies-manager/currencies-manager.module';
import { PlayersFilterModule } from '../../../../../common/components/players-filter/players-filter.module';
import { RestrictedCountriesModule } from '../../../../../common/components/restricted-countries/restricted-countries.module';

import { TournamentSegmentationFormComponent } from './tournament-segmentation-form.component';

export const SEGMENTATION_MODULES = [
  MatCardModule,
  BrandsManagerModule,
  MatDialogModule,
  MatButtonModule,
  MatFormFieldModule,
  MatInputModule,
  MatIconModule,
  ReactiveFormsModule,
  MatFormFieldModule,
  SwuiChipsAutocompleteModule,
  CurrenciesManagerModule,
  SwuiIsControlInvalidModule,
  PlayersFilterModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    ...SEGMENTATION_MODULES,
    RestrictedCountriesModule,
  ],
  exports: [
    TournamentSegmentationFormComponent,
  ],
  declarations: [
    TournamentSegmentationFormComponent,
  ],
})
export class TournamentSegmentationFormModule {
}
