import { Component, Input } from '@angular/core';
import * as moment from 'moment';
import 'moment-timezone';
import { FORMAT_DATETIME } from '../../../../../../app.constants';


export interface ScheduleDatesConflict {
  current: string;
  next: string;
}

@Component({
  selector: 'sw-tournament-schedule-dates-conflict',
  templateUrl: './tournament-schedule-dates-conflict.component.html',
  styleUrls: ['./tournament-schedule-dates-conflict.component.scss']
})
export class TournamentScheduleDatesConflictComponent {
  @Input()
  set timezone(value: string | undefined) {
    if (!value) { return; }
   this._timezone = value;
   this.offset = moment.tz(value).format('Z');
  }
  get timezone(): string | undefined {
    return this._timezone;
  }

  @Input()
  get dates(): ScheduleDatesConflict[] {
    return this._dates;
  }
  set dates(value: ScheduleDatesConflict[]) {
    if (!value) { return; }
    this._dates = value;
    this.conflicts = this.getScheduleDatesConflicts(this._dates);
  }

  conflicts: ScheduleDatesConflict[] = [];
  offset: string | undefined;
  private _dates: ScheduleDatesConflict[] = [];
  private _timezone: string | undefined;

  getScheduleDatesConflicts(conflicts: ScheduleDatesConflict[]): ScheduleDatesConflict[] {
    return conflicts.slice(0, 3).map( (el: ScheduleDatesConflict) => {
      const current = moment(el.current).utc();
      const next = moment(el.next).utc();
      return {
        current: current.format(FORMAT_DATETIME) + ' (' + this.offset + '), ' + current.format('dddd'),
        next: next.format(FORMAT_DATETIME) + ' (' + this.offset + '), ' + next.format('dddd'),
      };
    });
  }

  getLastConflictsAmount(): number {
    return this.dates.length > 3 ? this.dates.length - 3 : 0;
  }
}
