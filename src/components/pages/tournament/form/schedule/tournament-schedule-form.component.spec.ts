import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { TournamentFormService } from '../../tournament-update/tournament-form-service/tournament-form.service';
import { TournamentScheduleFormComponent } from './tournament-schedule-form.component';
import { MODULES } from './tournament-schedule-form.module';
import { MatIconRegistry } from '@angular/material/icon';
import { FakeMatIconRegistry } from '@angular/material/icon/testing';

describe('TournamentScheduleFormComponent', () => {
  let component: TournamentScheduleFormComponent;
  let fixture: ComponentFixture<TournamentScheduleFormComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TournamentScheduleFormComponent],
      imports: [
        NoopAnimationsModule,
        TranslateModule.forRoot(),
        ...MODULES,
      ],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        TournamentFormService,
        { provide: MatIconRegistry, useClass: FakeMatIconRegistry }
      ],
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TournamentScheduleFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
