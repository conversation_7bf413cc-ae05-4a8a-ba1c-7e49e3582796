import { AbstractControl, FormArray, ValidationErrors } from '@angular/forms';
import * as moment from 'moment';
import 'moment-timezone';
import { getScheduleRuns } from '../../../interfaces/tournament';
import { processScheduleForm, RepetitionMode } from './tournament-schedule-form.helpers';

export function rangeRequiredValidator( control: AbstractControl ): ValidationErrors | null {
  if (!control.value.from || !control.value.to) {
    return { required: true };
  }
  return null;
}

export function scheduleValidator( form: AbstractControl ): ValidationErrors | null {
  const schedule = processScheduleForm(form.value);
  const scheduleRuns = getScheduleRuns(schedule, 10);
  if (scheduleRuns.length === 0) {
    let hasDates = false;
    if (schedule.repetitionMode === RepetitionMode.Dates) {
      hasDates = Array.isArray(schedule.specificDates) && schedule.specificDates.length > 0;
    } else if (schedule.repetitionMode === RepetitionMode.Interval) {
      hasDates = !!schedule.interval && schedule.interval.daysOfWeek.length > 0;
    }
    if (schedule.timeZone && hasDates && Array.isArray(schedule.triggerAt) && schedule.triggerAt.length) {
      return { inFuture: true };
    }
  }
  const invalidDates = [];
  let isValid = true;
  for (let i = 0; i < scheduleRuns.length; i++) {
    const currentPlusDuration = moment(scheduleRuns[i]).add(schedule.duration, 'milliseconds');
    if (scheduleRuns[i + 1] && currentPlusDuration.diff(moment(scheduleRuns[i + 1]), 'milliseconds') > 0) {
      isValid = false;
      invalidDates.push({ current: scheduleRuns[i], next: scheduleRuns[i + 1] });
    }
  }
  return isValid ? null : {
    scheduleValid: {
      dates: invalidDates
    }
  };
}

export function laterThanPrevValidator( arr: AbstractControl ): ValidationErrors | null {
  const array = arr as FormArray;
  const controls = array.controls;
  let isLater = true;
  if (controls && controls.length > 1) {

    for (let i = 1; i < controls.length; i++) {
      const current = controls[i];
      const prev = controls[i - 1];
      isLater = moment(current.value).diff(prev.value) > 0;
      if (!isLater) {
        current.setErrors({ 'isLater': true });
      }
    }
  }
  return null;
}

export function startTimeValidator( control: AbstractControl ): ValidationErrors | null {
  const array = control as FormArray;
  const controls = array.controls;
  let isValid = true;

  for (let cont of controls) {
    cont.hasError('startTimeDuplicate') ? cont.setErrors(null) : cont.setErrors(cont.errors);
  }

  let duplicate: { [key: string]: number[] } = {};

  array.value.forEach(( item: number, index: number ) => {
    duplicate[item] = [...duplicate[item] || [], index];

    if (duplicate[item].length > 1) {
      for (let itemPosition of duplicate[item]) {
        isValid = false;
        controls[itemPosition].setErrors({ 'startTimeDuplicate': true });
        controls[itemPosition].markAsTouched();
      }
    }
  });

  return isValid ? null : {
    duplicates: true
  };
}
