$highlighted: #1468cf;
:host {
  &:focus {
    outline: none;
  }
}
.week {
  display: flex;
  &__checkbox {
    display: none;
  }
  &__day {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    margin-right: 10px;
    font-size: 14px;
    color: rgba(0, 0, 0, .6);
    border: 1px solid rgba(0, 0, 0, .12);
    border-radius: 50%;
    cursor: pointer;
    transition: all .15s ease-in-out;
    &.checked {
      border: none;
      color: #fff;
      background-color: $highlighted;
      &:hover {
        background-color: rgba($highlighted, .9);
      }
    }
    &:not(.checked) {
      &:hover {
        color: $highlighted;
        border-color: $highlighted;
      }
    }
    &:last-child {
      margin-right: 0;
    }
  }
}
