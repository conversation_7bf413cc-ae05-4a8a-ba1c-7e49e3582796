import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { Component } from '@angular/core';
import { FormControl } from '@angular/forms';
import { createHostFactory, SpectatorHost } from '@ngneat/spectator';
import { TournamentScheduleWeekComponent } from './tournament-schedule-week.component';

@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}

describe('TournamentScheduleWeekComponent', () => {
  let spectator: SpectatorHost<TournamentScheduleWeekComponent, CustomHostComponent>;
  const createHost = createHostFactory({
    component: TournamentScheduleWeekComponent,
    host: CustomHostComponent,
    imports: [
      CommonModule,
      TranslateModule.forRoot(),
    ],
    declarations: [TournamentScheduleWeekComponent]
  });

  beforeEach(() => {
    spectator = createHost(`<sw-tournament-schedule-week [formControl]="form"></sw-tournament-schedule-week>`);
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();
  });

  it('set disabled state', () => {
    spectator.component.setDisabledState(true);
    expect(spectator.component.disabled).toEqual(true);
  });
});
