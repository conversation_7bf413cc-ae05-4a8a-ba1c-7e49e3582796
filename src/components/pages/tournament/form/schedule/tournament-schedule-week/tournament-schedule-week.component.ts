import { Component, forwardRef, HostBinding, HostListener, OnInit } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import * as moment from 'moment';
import 'moment-timezone';

export interface WeekDays {
  id: number;
  text: string;
  checked: boolean;
}

@Component({
  selector: 'sw-tournament-schedule-week',
  templateUrl: './tournament-schedule-week.component.html',
  styleUrls: ['./tournament-schedule-week.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TournamentScheduleWeekComponent),
      multi: true
    },
  ],
})
export class TournamentScheduleWeekComponent implements OnInit, ControlValueAccessor {
  disabled = false;
  isChecked = false;
  weekDays: WeekDays[] = [];
  @HostBinding('attr.tabindex') tabindex = 0;
  private _onChange: (_: any) => void = (() => {
  });
  private _onTouched: any;

  @HostListener('blur') onblur() {
    this._onTouched();
  }

  ngOnInit(): void {
    for (let i = 1; i <= 7; i++) {
      this.weekDays.push({
        id: i,
        text: moment().isoWeekday(i).format('dd'),
        checked: this.isChecked
      });
    }
  }

  registerOnChange(fn: (_: any) => void): void {
    this._onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this._onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  writeValue(value: number[]): void {
    value.forEach(selected => this.weekDays && this.weekDays[selected - 1] ? this.weekDays[selected - 1].checked = true : false);
  }

  handleChange(elemId: number, isChecked: boolean) {
    this.weekDays[elemId - 1].checked = isChecked;
    this._onChange(this.weekDays.filter(days => days.checked).map(element => element.id));
  }
}
