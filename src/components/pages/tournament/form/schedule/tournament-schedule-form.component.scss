
.hidden {
  display: none;
}

.dates-radio {
  display: flex;
  flex-direction: column;

  &__button {
    margin-bottom: 8px;
  }

  &__item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__hint {
    display: block;
  }

  &__content {
    padding-left: 28px;
  }
}

.daily-list {
  padding: 0;

  &__item {
    display: inline-block;
    margin-right: 20px;

    &:last-child {
      margin-right: 0;
    }
  }

  &__calendar {
    width: 150px;
  }
}

.dates-list {
  margin: 0;
  padding: 0;
  list-style: none;

  &__item {
    display: flex;
    align-items: center;
  }

  &__calendar {
    width: 150px;
  }

  &__time {
    display: inline-block;
    width: 170px;
  }

  &__delete {
    width: 24px;
    height: 24px;
    line-height: 24px;
    margin-left: 8px;
    margin-bottom: 1.34375em;
  }

  &__add {
    text-transform: uppercase;
  }
}

.days {
  &__list {
    padding: 0;
    margin: 0;
    list-style: none;
  }

  &__item {
    display: inline-block;
    margin: 0 16px 8px 0;

    &:last-child {
      margin-right: 0;
    }
  }

  &__label {
    font-weight: 400;
  }
}

.duration {
  &__time {
    width: 160px;
    margin-right: 32px;
  }

  &__timezone {
    display: block;
    width: 100%;
    max-width: 400px;
  }
}

.duration-hint {
  display: flex;
  width: calc(100% + 1em);
  flex-shrink: 0;
  margin-left: -0.8em;

  &__item {
    position: relative;
    display: block;
    width: calc(100% / 3);
    padding: 0 3px;
    text-align: center;

    &--minutes {
      right: -5px;
    }

    &--days {
      padding-left: 5px;
    }
  }
}

.validity-dates {
  width: 340px;
}

.card-header {
  &__title {
    display: inline-block;
  }

  &__icon {
    display: inline-block;
    position: absolute;
    top: 20px;
    right: 20px;
  }
}

.search-input {
  height: 48px;
  text-align: start;
  padding-left: 20px;
}

.days-error {
  font-size: 14px;
}

.dates-section {
  display: flex;
}

.dates-table {
  min-width: 340px;
  margin-left: 32px;
  border: 1px solid rgba(0, 0, 0, .12);
  border-radius: 3px;

  table {
    font-size: 14px;
    border-collapse: collapse;

    td, th {
      height: 40px;
      padding: 0 16px;
      border-bottom: 1px solid rgba(0, 0, 0, .12);
      white-space: nowrap;
    }

    tr {
      &:last-child {
        td {
          border-bottom: none;
        }
      }
    }
  }
}

.timezone-title {
  margin-left: 32px;
  padding-top: 10px;
}

.icon-disabled {
  color: rgba(0, 0, 0, 0.38);
}
