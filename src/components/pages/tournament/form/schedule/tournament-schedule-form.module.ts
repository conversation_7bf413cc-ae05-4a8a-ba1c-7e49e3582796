import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import {
  SwuiIsControlInvalidModule,
  SwuiDatePickerModule,
  SwuiDateRangeModule,
  SwuiDateTimeRangeModule,
  SwuiSelectModule,
  SwuiStartTimeModule,
  SwuiTimeDurationModule
} from '@skywind-group/lib-swui';
import { PipesModule } from '../../../../../common/pipes/pipes.module';
import {
  TournamentScheduleDatesConflictComponent
} from './tournament-schedule-dates-conflict/tournament-schedule-dates-conflict.component';

import { TournamentScheduleFormComponent } from './tournament-schedule-form.component';
import { TournamentScheduleWeekModule } from './tournament-schedule-week/tournament-schedule-week.module';

export const MODULES = [
  ReactiveFormsModule,
  MatSelectModule,
  MatFormFieldModule,
  MatRadioModule,
  MatButtonModule,
  MatIconModule,
  MatCheckboxModule,
  MatInputModule,
  MatTooltipModule,
  FlexLayoutModule,
  SwuiDatePickerModule,
  SwuiTimeDurationModule,
  SwuiDateTimeRangeModule,
  SwuiStartTimeModule,
  SwuiSelectModule,
  TournamentScheduleWeekModule,
  SwuiDateRangeModule,
  PipesModule,
  SwuiIsControlInvalidModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...MODULES,
  ],
  exports: [
    TournamentScheduleFormComponent,
  ],
  declarations: [
    TournamentScheduleFormComponent,
    TournamentScheduleDatesConflictComponent,
  ],
})
export class TournamentScheduleFormModule {
}
