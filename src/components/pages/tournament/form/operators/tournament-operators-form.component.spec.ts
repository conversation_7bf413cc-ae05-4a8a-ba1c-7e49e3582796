import { createHostFactory, createServiceFactory, SpectatorHost, SpectatorService } from '@ngneat/spectator';
import { TournamentOperatorsFormComponent } from './tournament-operators-form.component';
import { TournamentFormService } from '../../tournament-update/tournament-form-service/tournament-form.service';
import { FeatureOperators } from '../../../interfaces/feature';
import { Component } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { BrandsManagerModule } from '../../../../../common/components/brands-manager/brands-manager.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SwHubAuthService } from '@skywind-group/lib-swui';


@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}

describe('TournamentOperatorsFormComponent', () => {
  let spectator: SpectatorHost<TournamentOperatorsFormComponent, CustomHostComponent>;
  const createHost = createHostFactory({
    component: TournamentOperatorsFormComponent,
    host: CustomHostComponent,
    imports: [
      ReactiveFormsModule,
      BrandsManagerModule,
      HttpClientTestingModule
    ],
    providers: [
      TournamentFormService,
      SwHubAuthService
    ]
  });

  let spectatorService: SpectatorService<TournamentFormService>;
  const createService = createServiceFactory({
    service: TournamentFormService
  });

  const value: FeatureOperators = {
    owner: {
      id: 'test',
      title: 'test'
    },
    brands: []
  };

  beforeEach(() => {
    spectator = createHost(`<sw-tournament-operators-form [formControl]="form"></sw-tournament-operators-form>`);
    spectatorService = createService();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();

    expect(spectatorService.service).toBeTruthy();
    expect(spectatorService.service).toBeDefined();
  });

  it('form submitted', () => {
    spectatorService.service.formSubmitted = true;
    expect(spectator.component.submitted).toBeTrue();

    spectatorService.service.formSubmitted = false;
    expect(spectator.component.submitted).toBeFalse();
  });

  it('operators validation onSelectData', () => {
    spectator.component.onSelectData(value);
    expect(spectator.component.operators).toEqual(value);
    spectator.component.onValidationCheck(false);
    expect(spectator.component.isValid).toBeFalse();
  });

  it('operators validation writeValue', () => {
    spectator.component.onValidationCheck(false);
    spectator.hostComponent.form.patchValue(value);

    expect(spectator.component.operators).toEqual(value);
    expect(spectator.component.isValid).toBeFalse();
    expect(spectator.component.validate()).toEqual({ invalidForm: { valid: false } });
  });

  it('form set disabled state', () => {
    spectator.component.setDisabledState(true);
    expect(spectator.component.disabled).toBeTrue();
  });

  it('operators isValid', () => {
    value.brands = [{
      id: 'test'
    }];
    spectator.hostComponent.form.patchValue(value);
    expect(spectator.component.operators).toEqual(value);
    spectator.component.onValidationCheck(true);
    expect(spectator.component.isValid).toBeTrue();
    expect(spectator.component.validate()).toBeNull();
  });
});
