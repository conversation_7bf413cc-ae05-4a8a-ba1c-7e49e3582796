import { Component, forwardRef, HostB<PERSON>ing, HostL<PERSON>ener, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ControlValueAccessor, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors } from '@angular/forms';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { FeatureOperators } from '../../../interfaces/feature';
import { TournamentFormService } from '../../tournament-update/tournament-form-service/tournament-form.service';


@Component({
  selector: 'sw-tournament-operators-form',
  templateUrl: './tournament-operators-form.component.html',
  styleUrls: ['./tournament-operators-form.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TournamentOperatorsFormComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => TournamentOperatorsFormComponent),
      multi: true
    },
  ]
})
export class TournamentOperatorsFormComponent implements ControlValueAccessor, OnInit, OnDestroy {
  onChange: ( _: any ) => void = (() => {
  });
  disabled = false;
  isValid = true;
  submitted = false;
  operators?: FeatureOperators;
  @HostBinding('attr.tabindex') tabindex = 0;

  private readonly destroyed$ = new Subject<void>();
  private fundingValid$ = new BehaviorSubject<boolean>(true);
  private value$ = new BehaviorSubject<FeatureOperators | undefined>(undefined);

  constructor( private readonly formService: TournamentFormService ) {
  }

  @HostListener('blur') onblur() {
    this.onTouched();
  }

  ngOnInit() {
    this.formService.formSubmitted$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(submitted => {
      this.submitted = submitted;
    });

    combineLatest([this.fundingValid$, this.value$]).pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( [valid, operators] ) => {
      this.operators = Object.assign({}, operators);
      this.isValid = valid && !!operators && !!operators.brands && !!operators.brands.length;
      this.onChange(operators);
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  writeValue( val: FeatureOperators ): void {
    this.value$.next(val);
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( isDisabled: boolean ): void {
    this.disabled = isDisabled;
  }

  validate(): ValidationErrors | null {
    return this.isValid ? null : { invalidForm: { valid: false } };
  }

  onSelectData( featureOperators: FeatureOperators ) {
    this.value$.next(featureOperators);
  }

  onValidationCheck( val: boolean ) {
    this.fundingValid$.next(val);
  }
}
