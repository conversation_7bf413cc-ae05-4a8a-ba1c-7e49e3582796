import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';

import { BrandsManagerModule } from '../../../../../common/components/brands-manager/brands-manager.module';
import { TournamentOperatorsFormComponent } from './tournament-operators-form.component';


@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    BrandsManagerModule,
    MatCardModule,
  ],
  exports: [
    TournamentOperatorsFormComponent,
  ],
  declarations: [
    TournamentOperatorsFormComponent,
  ],
})
export class TournamentOperatorsFormModule {
}
