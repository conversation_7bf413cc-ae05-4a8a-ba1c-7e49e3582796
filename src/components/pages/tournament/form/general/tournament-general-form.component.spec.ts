import { TournamentGeneralFormComponent } from './tournament-general-form.component';
import { createHostFactory, SpectatorHost } from '@ngneat/spectator';
import { AbstractControl, FormControl } from '@angular/forms';
import { Component } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MODULES } from './tournament-general-form.module';

@Component({ selector: 'sw-custom-host', template: '' })
class CustomHostComponent {
  form = new FormControl([]);
}

describe('TournamentGeneralFormComponent', () => {
  let spectator: SpectatorHost<TournamentGeneralFormComponent, CustomHostComponent>;
  const createHost = createHostFactory({
    component: TournamentGeneralFormComponent,
    host: CustomHostComponent,
    imports: [
      ...MODULES,
      TranslateModule.forRoot(),
    ],
    providers: [
      TranslateService
    ]
  });
  const value = {
    name: 'A',
    description: 'A',
    showFeature: true,
    optIn: false
  };
  const initialFormValue = {
    name: '',
    description: '',
    showFeature: true,
    optIn: false
  };

  beforeEach(() => {
    spectator = createHost(`<sw-tournament-general-form [formControl]="form"></sw-tournament-general-form>`);
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component).toBeDefined();
  });

  it('name field validity', () => {
    const name: AbstractControl | null = spectator.component.form.get('name');
    expect(name?.valid).toBeFalse();

    name?.setValue('');
    expect(name?.hasError('required')).toBeTruthy();

    name?.setValue('A');
    expect(name?.valid).toBeTrue();
  });

  it('form field Name setValue null validation should contain an error', () => {
    spectator.component.form.get('name')?.setValue(null);
    expect(spectator.component.validate()).toEqual({ invalidForm: { valid: false } });
  });

  it('form set disabled state', () => {
    spectator.component.setDisabledState(true);
    expect(spectator.component.form.disabled).toBeTrue();
  });

  it('writeValue has been called', () => {
    const onWriteValueSpy = spyOn(spectator.component, 'writeValue');
    spectator.hostComponent.form.patchValue(value);

    expect(onWriteValueSpy).toHaveBeenCalled();
  });

  it('form has been patched', () => {
    spectator.hostComponent.form.patchValue(value);
    expect(spectator.component.form.value).toEqual(value);
  });

  it('form has not patched with null', () => {
    spectator.hostComponent.form.patchValue(null);
    expect(spectator.component.form.value).toEqual(initialFormValue);
  });

  it('form field validity', () => {
    spectator.component.form.get('name')?.setValue('');

    expect(spectator.component.form.valid).toBeFalse();
    expect(spectator.hostComponent.form?.hasError('invalidForm')).toBeTrue();
  });
});
