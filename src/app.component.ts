import { AfterViewInit, Component, OnInit } from '@angular/core';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer, Meta } from '@angular/platform-browser';
import { SwBrowserTitleService, SwHubInitService } from '@skywind-group/lib-swui';
import { environment } from './environments/environment';

@Component({
  selector: 'sw-root',
  template: `
    <lib-swui-top-menu></lib-swui-top-menu>
    <router-outlet></router-outlet>
  `
})
export class AppComponent implements OnInit, AfterViewInit {

  constructor( meta: Meta,
               private readonly hubService: SwHubInitService,
               private readonly matIconRegistry: MatIconRegistry,
               private readonly domSanitizer: DomSanitizer,
               private browserTitleService: SwBrowserTitleService,
  ) {
    meta.addTag({ name: 'version', content: environment.APP_VERSION });
    this.setupIcons();
  }

  ngAfterViewInit(): void {
    this.hubService.init();
  }

  ngOnInit() {
    this.browserTitleService.setupTitles('Engagement');
  }

  private setupIcons() {
    ['icon_report', 'question_mark', 'clear', 'trash', 'date_range', 'trophy', 'gold_pot', 'prize'].forEach(name => {
      this.matIconRegistry.addSvgIcon(
        name,
        this.domSanitizer.bypassSecurityTrustResourceUrl(`icons/${name}.svg`)
      );
    });
  }
}
