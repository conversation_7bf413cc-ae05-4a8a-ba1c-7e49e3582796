import { Label } from './label';

export type PromotionStatus = 'active' | 'inactive';

export type PromotionState = 'new'
  | 'pending'
  | 'alt_pending'
  | 'inProgress'
  | 'in_progress'
  | 'alt_in_progress'
  | 'finished'
  | 'alt_finished'
  | 'expired';

export type RewardType = 'bonus_coin' | 'freebet';

interface BasePromotionParams {
  title?: string;
  description?: string;
  startDate: string;
  endDate: string;
  timezone: string;
  state?: PromotionState;
  status?: PromotionStatus;
  labels?: Label[];
}

interface PromotionDataParams extends BasePromotionParams {
  id?: string;
  externalId?: string;
  brandId?: string;
  brandPath?: string;
  createdAt?: string;
  updatedAt?: string;
  createdUserId?: string;
  modifiedBy?: string;
  activatedAt?: string;
}

export type FreeBetPromotionData = PromotionDataParams & FreeBetPromotionParams;

export type PromotionData = FreeBetPromotionData;

interface PromotionParams extends PromotionDataParams {
  id: string;
  totalParticipated?: number;
}

export type Promotion = (PromotionParams & FreeBetPromotionParams);

export interface FreeBetPromotionParams {
  type: 'freebet';
  rewards?: FreebetRewardInfo[];
  startRewardOnGameOpen: boolean;
}

export type PromotionRewardInfo = FreeBetPromotionParams;

interface CommonRewardData {
  id?: number;
  promoId?: number;
  startRewardOnGameOpen: boolean;
}

interface ExpiringReward {
  expirationPeriod?: number;
  expirationPeriodType?: string;
  expirationDate?: string;
  expirationType?: 'in' | 'on';
}

export interface FreebetRewardInfo extends CommonRewardData, ExpiringReward {
  freebetAmount: number;
  games: FreebetGameConfig[];
}

export interface FreebetGameConfig {
  gameCode: string;
  coins: FreebetGameCoinConfig[];
}

interface FreebetGameCoinConfig {
  [field: string]: { coin: number };
}
