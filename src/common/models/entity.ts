import { SelectOptionItem } from '@skywind-group/lib-swui';

export type EntityType = 'entity' | 'brand' | 'merchant';

export interface ShortEntity {
  id: string;
  type?: EntityType;
  name?: string;
  title?: string;
  path: string;
  child?: ShortEntity[];
  text?: string;
  selected?: boolean;
  funding?: number;
  decryptedBrand?: string;
}

export interface Entity extends ShortEntity {
  currencies: string[];
  isMerchant?: boolean;
  defaultCurrency?: string;
  params?: { [field: string]: any };
}

export interface MerchantEntity extends Entity {
  merchant?: {
    params: { [field: string]: any };
  };
  defaultCurrency?: string;
}

export function isResellerType( entity?: ShortEntity ): boolean {
  if (!entity) {
    return false;
  }
  return isReseller(entity.type);
}

export function isReseller( type?: EntityType ): boolean {
  return type === 'entity';
}

export function isBrandType( entity?: ShortEntity ): boolean {
  if (!entity) {
    return false;
  }
  return isBrand(entity.type);
}

export function isBrand( type?: EntityType ): boolean {
  return type === 'brand' || type === 'merchant';
}

export function isMerchantType( entity?: Entity | null ): boolean {
  if (!entity) {
    return false;
  }
  return 'isMerchant' in entity && entity.isMerchant === true;
}

export function isMerchantWithInternalPromo( entity?: MerchantEntity ): boolean {
  if (!entity) {
    return false;
  }
  return isMerchantType(entity) && !!entity.merchant &&
    'isPromoInternal' in entity.merchant.params && entity.merchant.params.isPromoInternal;
}

export function entityFindBy( entity: ShortEntity, fn: ( entity: ShortEntity ) => boolean ): ShortEntity | null {
  if (fn(entity)) {
    return entity;
  }
  const entities = entity.child;
  if (Array.isArray(entities)) {
    for (const item of entities) {
      const child = entityFindBy(item, fn);
      if (child) {
        return child;
      }
    }
  }
  return null;
}

export function entitiesFilterBy( entities: ShortEntity[], fn: ( entity: ShortEntity ) => boolean ): ShortEntity[] {
  const result: ShortEntity[] = [];
  setSelected(entities || [], fn)
    .map(item => entityRemoveBy(item, ( { selected } ) => !selected))
    .forEach(item => {
      if (item !== null) {
        result.push(item);
      }
    });
  return result;
}

function setSelected( entities: ShortEntity[], fn: ( entity: ShortEntity ) => boolean ): ShortEntity[] {
  return entities.map(entity => {
    const nodeSelected = fn(entity);
    if (nodeSelected) {
      selectAllChild(entity);
      return ({
        ...entity,
        selected: true
      });
    }
    const child = entity.child ? setSelected(entity.child, fn) : undefined;
    const childSelected = child ? child.some(( { selected } ) => selected === true) : false;
    return ({
      ...entity,
      selected: childSelected,
      child
    });
  });
}

function selectAllChild( entity: ShortEntity ): void {
  (entity.child || []).forEach(child => {
    child.selected = true;
    selectAllChild(child);
  });
}

export function entityRemoveBy( entity: ShortEntity, fn: ( entity: ShortEntity ) => boolean ): ShortEntity | null {
  const child: ShortEntity[] = [];
  (entity.child || [])
    .map(item => entityRemoveBy(item, fn))
    .forEach(item => {
      if (item !== null) {
        child.push(item);
      }
    });
  if (child.length === 0 && fn(entity)) {
    return null;
  }
  return { ...entity, child };
}

export function entitiesToSelectOptions( entity: ShortEntity, count: number = 0, result: SelectOptionItem[] = [] ): SelectOptionItem[] {
  const fullPath = entity.path.replace(/:$/g, '').split(':');
  result.push({
    id: entity.path === ':' ? '' : `${fullPath[fullPath.length - 1]}:`,
    text: `${'-'.repeat(count)} ${entity.name} - ${entity.title || 'All'}`,
    disabled: isResellerType(entity)
  });
  if (entity.child) {
    entity.child.reverse().forEach(item => entitiesToSelectOptions(item, count + 1, result));
  }
  return result;
}

export const entitiesStructureToSelectOptions =
  ( item: any, count: number = 0, resultArray: SelectOptionItem[] = [], disableEntityEntries: boolean = true ) => {
    const disabled = disableEntityEntries && isResellerType(item);
    const path = item.path === ':' ? '' : item.path;

    resultArray.push(
      {
        id: path,
        text: `${'-'.repeat(count)} ${item.name} - ${item.title || 'All'}`,
        disabled,
      }
    );

    if (item.child) item.child.reverse()
      .forEach(( i: any ) => entitiesStructureToSelectOptions(i, count + 1, resultArray, disableEntityEntries));

    return resultArray;
  };
