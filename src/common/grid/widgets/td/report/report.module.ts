import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { ReportTdWidgetComponent } from './report.widget';

@NgModule({
  imports: [
    CommonModule,
    RouterModule,
    MatTooltipModule,
    TranslateModule,
    MatButtonModule,
    MatIconModule,
  ],
  declarations: [ReportTdWidgetComponent],
  entryComponents: [ReportTdWidgetComponent]
})
export class ReportTdWidgetModule {
}
