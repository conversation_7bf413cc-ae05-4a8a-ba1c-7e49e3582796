import { Component, Inject } from '@angular/core';
import { SwHubConfigService, SWUI_GRID_WIDGET_CONFIG, SwuiGridField } from '@skywind-group/lib-swui';

export interface SwuiTdReportWidgetConfig {
  row: any;
  schema: SwuiGridField;
  value: any;
  options: {
    classFn?: ( row: any, schema: SwuiGridField ) => any;
  };
}

@Component({
  templateUrl: './report.widget.html',
  styleUrls: ['./report.widget.scss']
})
export class ReportTdWidgetComponent {
  readonly title = '';
  readonly icon?: string;
  readonly svgIcon?: string;

  readonly href?: string;
  readonly useTranslate = false;

  readonly canActivate: boolean;
  readonly classObj: any;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) { row, schema, value }: SwuiTdReportWidgetConfig,
               { hubs }: SwHubConfigService
  ) {
    const classFn = schema.td?.classFn;
    const titleFn = schema.td?.titleFn;
    const activeFn = schema.td?.canActivateFn;
    const urlParams = (schema.td?.urlParamsFn && schema.td.urlParamsFn(row, schema)) || value;

    this.icon = schema.td?.icon ? schema.td.icon : undefined;
    this.svgIcon = schema.td?.svgIcon ? schema.td.svgIcon : undefined;
    this.title = (titleFn && titleFn(row, schema)) || value;

    this.canActivate = (activeFn && activeFn(row, schema)) || false;
    this.classObj = classFn && classFn(row, schema);

    this.href = hubs && hubs.analytics ? `${hubs.analytics.url}/${urlParams}` : undefined;
  }
}
