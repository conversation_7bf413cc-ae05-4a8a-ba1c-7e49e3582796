import { Pipe, PipeTransform } from '@angular/core';
import { formatDate } from '../lib/format-date';

@Pipe({ name: 'formatDate' })
export class FormatDatePipe implements PipeTransform {

  transform( input: string | undefined, format: string, timezone?: string, skipTz = false,
             displayTimeZone = true
  ): string {
    return formatDate(input, format, timezone, skipTz, displayTimeZone);
  }
}
