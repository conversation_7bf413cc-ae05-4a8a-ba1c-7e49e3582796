import { Pipe, PipeTransform } from '@angular/core';
import { SettingsService } from '@skywind-group/lib-swui';
import { currencyFormatter } from '../directives/appSettingCurrencyFormatter/settingCurrencyFormatter.directive';

@Pipe({ name: 'currencyFormat', pure: false })
export class CurrencyFormatPipe implements PipeTransform {
  constructor(private readonly settingsService: SettingsService ) {
  }

  transform(input: number | string, currencyCode: string): string | null {
    const inputNumber = parseFloat(input.toString());
    const currencyFormat = this.settingsService.appSettings.currencyFormat;

    return input.toString() !== 'MAX' ?
      currencyFormatter(currencyFormat, inputNumber, currencyCode) :
      input.toString();
  }
}
