import { SwuiGridField } from '@skywind-group/lib-swui';

export const SCHEMA: SwuiGridField[] = [
  {
    field: 'playerId',
    title: 'TOURNAMENT.FORM.SEGMENTATION.playerId',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    isEditable: true,
  },
  {
    field: 'brandId',
    title: 'TOURNAMENT.FORM.SEGMENTATION.brandId',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    isEditable: true,
  },
  {
    field: 'operator',
    title: 'TOURNAMENT.FORM.SEGMENTATION.operator',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    isEditable: true,
  },
];
