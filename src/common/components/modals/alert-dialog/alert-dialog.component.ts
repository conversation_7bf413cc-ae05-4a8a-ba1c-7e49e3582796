import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

export interface ConfirmDialogData {
  message: string;
}

@Component({
  selector: 'sw-swui-confirm-dialog',
  templateUrl: 'alert-dialog.component.html'
})
export class AlertDialogComponent {

  message: string;

  constructor(
    @Inject(MAT_DIALOG_DATA) data: ConfirmDialogData,
    public dialogRef: MatDialogRef<AlertDialogComponent>,
  ) {
    this.message = data.message;
  }

  onClick() {
    this.dialogRef.close();
  }
}
