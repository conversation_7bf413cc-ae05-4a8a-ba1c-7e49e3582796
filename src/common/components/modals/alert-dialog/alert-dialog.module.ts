import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { AlertDialogComponent } from './alert-dialog.component';

@NgModule({
  imports: [CommonModule, TranslateModule, MatDialogModule, MatButtonModule],
  declarations: [AlertDialogComponent]
})
export class AlertDialogModule {
}
