import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import {
  SwuiChipsAutocompleteModule,
  SwuiControlMessagesModule,
  SwuiCurrencySymbolModule,
  SwuiIsControlInvalidModule
} from '@skywind-group/lib-swui';
import { FloatFormatterModule } from '../../directives/floatFormatter/floatFormatter.module';
import { JpnService } from '../../services/jpn.service';
import { CurrenciesManagerComponent } from './currencies-manager.component';
import { PipesModule } from '../../pipes/pipes.module';
import { SettingCurrencyFormatterModule } from '../../directives/appSettingCurrencyFormatter/settingCurrencyFormatter.module';

export const CURRENCIES_MANAGER_MODULES = [
  ReactiveFormsModule,
  MatFormFieldModule,
  MatInputModule,
  MatIconModule,
  MatButtonModule,
  MatMenuModule,
  SwuiChipsAutocompleteModule,
  SwuiControlMessagesModule,
  SwuiCurrencySymbolModule,
  FloatFormatterModule,
  SwuiIsControlInvalidModule,
  PipesModule,
];

@NgModule({
  declarations: [CurrenciesManagerComponent],
  exports: [CurrenciesManagerComponent],
    imports: [
        CommonModule,
        TranslateModule,
        ...CURRENCIES_MANAGER_MODULES,
        SettingCurrencyFormatterModule,
    ],
  providers: [JpnService]
})
export class CurrenciesManagerModule {
}
