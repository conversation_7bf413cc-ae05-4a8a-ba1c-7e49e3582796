.currencies {
  &__add {
    margin-bottom: 16px;
    &--bottom {
      padding: 0 8px;
      margin-bottom: 0;
      margin-top: 8px;
    }
  }
}

.table-currencies {
  &__input {
    width: 132px;
    text-align: right;
  }

  &__link {
    margin-left: 16px;
    font-size: 14px;
    text-decoration: underline;
    cursor: pointer;

    &.disabled {
      color: rgba(0, 0, 0, .26);
      cursor: default;
    }
  }

  td,
  th {
    &:first-child {
      text-align: left !important;
      width: 100px;
      padding-left: 0;
    }
  }

  &__symbol {
    margin-right: 4px;
  }
}

.cur-dropdown {
  &__input {
    &:focus {
      outline: none;
    }
  }

  &__list {
    height: 210px;
    overflow: auto;
    border-top: 1px solid rgba(0, 0, 0, 0.12);
  }

  &__input {
    display: block;
    width: 100%;
    height: 46px;
    padding: 0 0.75em;
    margin: 0;
    border: unset;
    font-size: 1em;
    color: rgba(0, 0, 0, 0.87);
  }

  &__item {
    user-select: none;
    cursor: pointer;
    outline: none;
    border: none;
    -webkit-tap-highlight-color: transparent;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    line-height: 48px;
    height: 48px;
    padding: 0 16px;
    text-align: left;
    text-decoration: none;
    max-width: 100%;
    position: relative;

    &:hover {
      &:not(.disabled) {
        background: rgba(0, 0, 0, 0.04);
        color: rgba(0, 0, 0, 0.87);
      }
    }

    &.disabled {
      color: rgba(0, 0, 0, 0.38);
      cursor: default;
    }
  }
}

.error-message {
  &__symbol {
    font-weight: 500;
    margin-right: 4px;
  }
}

.info-message {
  padding: 20px;
  border-radius: 4px;
  background-color: #ffd47e;
  margin-bottom: 20px;
}
