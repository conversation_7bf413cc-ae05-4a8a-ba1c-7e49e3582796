import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { FeatureCurrency } from '../../../components/pages/interfaces/feature';

@Injectable()
export class CurrenciesManagerService {
  private _selectedCurrencies$ = new BehaviorSubject<FeatureCurrency[]>([]);
  private _selectedPayoutCurrency$ = new BehaviorSubject<FeatureCurrency | undefined>(undefined);

  set selectedCurrencies( val: FeatureCurrency[] ) {
    this._selectedCurrencies$.next(val);
  }

  get selectedCurrencies(): FeatureCurrency[] {
    return this._selectedCurrencies$.value;
  }

  get selectedCurrencies$(): Observable<FeatureCurrency[]> {
    return this._selectedCurrencies$ as Observable<FeatureCurrency[]>;
  }

  set selectedPayoutCurrency( val: FeatureCurrency | undefined ) {
    this._selectedPayoutCurrency$.next(val);
  }

  get selectedPayoutCurrency$(): Observable<FeatureCurrency | undefined> {
    return this._selectedPayoutCurrency$ as Observable<FeatureCurrency | undefined>;
  }
}


