<div *ngIf="isApplyingRatesMessageVisible && !isDuplicate" class="info-message margin-bottom20">
  {{ 'TOURNAMENT.FORM.SEGMENTATION.applyingRatesMessage' | translate }}
</div>

<div class="currencies">
  <div class="margin-bottom16">
    {{ (currenciesArray.controls.length ? 'TOURNAMENT.FORM.SEGMENTATION.currenciesHint' : 'TOURNAMENT.FORM.SEGMENTATION.currenciesSubTitle') | translate  }}
  </div>
  <form [formGroup]="form" *ngIf="!!currenciesArray.controls.length">
    <div formArrayName="currencies">
      <div class="error-message" *swIsControlInvalid="currenciesArray">
        <ng-container *ngFor="let group of currenciesArray.controls">
          <div *swIsControlInvalid="group">
            <span class="error-message__symbol">{{group.get('code')?.value}}</span>
            <lib-swui-control-messages [control]="group.get('rate')"></lib-swui-control-messages>
          </div>
        </ng-container>
      </div>
      <table class="sw-mat-table table-currencies">
        <thead>
        <tr>
          <th class="table-currencies__cell">Currency</th>
          <th class="table-currencies__cell">
            {{rateTitle}}
          </th>
          <th class="table-currencies__cell">
          </th>
          <th class="table-currencies__cell">
          </th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let item of currenciesArray.controls; let i=index" [formGroupName]="i">
          <td class="table-currencies__cell">
            {{item?.value?.code | uppercase}}
          </td>
          <td class="table-currencies__cell table-currencies__cell--rate">
            <ng-container *ngIf="baseCurrency !== item?.value?.code;">
              <mat-form-field appearance="outline" class="no-field-padding table-currencies__input">
                <span matPrefix>{{item?.value?.code | currencySymbol}}</span>
                <input type="number" matInput min="0" formControlName="rate"
                       swCurrencyFormatter [currencyCode]="item?.value?.code">
              </mat-form-field>
            </ng-container>
          </td>
          <td class="table-currencies__cell">
            <a
              class="link table-currencies__link"
              [ngClass]="{'disabled': isDisabled}"
              (click)="goToPayout($event, item?.value)">
              View payout
            </a>
          </td>
          <td class="table-currencies__cell">
            <button mat-icon-button (click)="removeCurrency(i, item?.value?.code)" [disabled]="isDisabled">
              <mat-icon>close</mat-icon>
            </button>
          </td>
        </tr>
        </tbody>
      </table>
    </div>
  </form>
  <button
    *ngIf="!currenciesArray.controls.length"
    mat-flat-button
    color="primary"
    class="currencies__add mat-button-md"
    [disabled]="isDisabled"
    [matMenuTriggerFor]="currenciesMenu">
    <mat-icon>add</mat-icon>
    Add Currency
  </button>
  <mat-menu #currenciesMenu="matMenu" (close)="onCloseMenu()">
    <div class="cur-dropdown">
      <input
        [formControl]="searchControl"
        type="text"
        placeholder="Search"
        (click)="stopPropagation($event)"
        class="cur-dropdown__input">
      <div class="cur-dropdown__list">
        <div
          *ngFor="let currency of filteredCurrencies"
          class="cur-dropdown__item"
          [ngClass]="{'disabled': !!currency.disabled}"
          (click)="addCurrency($event, currency)">
          {{currency.id | uppercase}}
        </div>
      </div>
    </div>
  </mat-menu>
  <button
    *ngIf="!!currenciesArray.controls.length"
    mat-button
    color="primary"
    class="currencies__add currencies__add--bottom"
    [disabled]="isDisabled"
    [matMenuTriggerFor]="currenciesMenu">
    <mat-icon>add</mat-icon>
    Add Currency
  </button>
</div>
