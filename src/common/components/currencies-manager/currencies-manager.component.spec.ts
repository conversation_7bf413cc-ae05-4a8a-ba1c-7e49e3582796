import { CommonModule } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { JpnService, MockJpnService } from '../../services/jpn.service';

import { CurrenciesManagerComponent } from './currencies-manager.component';
import { CURRENCIES_MANAGER_MODULES } from './currencies-manager.module';
import { CurrenciesManagerService } from './currencies-manager.service';

describe('CurrenciesManagerComponent', () => {
  let component: CurrenciesManagerComponent;
  let fixture: ComponentFixture<CurrenciesManagerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        TranslateModule.forRoot(),
        HttpClientTestingModule,
        ReactiveFormsModule,
        ...CURRENCIES_MANAGER_MODULES
      ],
      declarations: [CurrenciesManagerComponent],
      providers: [
        CurrenciesManagerService,
        { provide: JpnService, useClass: MockJpnService },
      ]
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CurrenciesManagerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
