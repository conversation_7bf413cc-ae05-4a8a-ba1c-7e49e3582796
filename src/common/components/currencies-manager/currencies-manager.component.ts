import { ChangeDetectorRef, Component, forwardRef, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  ControlValueAccessor, FormArray, FormBuilder, FormControl, FormGroup, NG_VALIDATORS, NG_VALUE_ACCESSOR,
  ValidationErrors, Validators
} from '@angular/forms';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { BehaviorSubject, forkJoin, of, Subject } from 'rxjs';
import { distinctUntilChanged, filter, map, switchMap, takeUntil } from 'rxjs/operators';
import { FeatureCurrency } from '../../../components/pages/interfaces/feature';
import {
  fractionsNumbersLengthValidator, numberMaxLength, numbersOnlyValidator, positiveNumbers
} from '../../lib/validators';
import { CurrencyModel } from '../../models/currency.model';
import { JpnService } from '../../services/jpn.service';
import { CurrenciesManagerService } from './currencies-manager.service';


@Component({
  selector: 'sw-currencies-manager',
  templateUrl: './currencies-manager.component.html',
  styleUrls: ['./currencies-manager.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CurrenciesManagerComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => CurrenciesManagerComponent),
      multi: true
    },
  ]
})
export class CurrenciesManagerComponent implements ControlValueAccessor, OnInit, OnDestroy {
  @Input()
  set currencies( val: CurrencyModel[] ) {
    if (!(val && Array.isArray(val))) {
      return;
    }
    this.processedCurrencies$.next(val.map(el => {
      return {
        id: el.code,
        text: el.code,
        disabled: false
      };
    }));
  }

  @Input()
  set baseCurrency( val: string ) {
    this._baseCurrency$.next(val || '');
  }

  get baseCurrency(): string {
    return this._baseCurrency$.value;
  }

  @Input() initialCurrencies?: FeatureCurrency[];
  @Input() isDuplicate = false;

  onChange: ( _: any ) => void = (() => {
  });

  filteredCurrencies: SwuiSelectOption[] = [];
  isDisabled = false;

  isApplyingRatesMessageVisible = false;

  readonly form: FormGroup;
  readonly searchControl: FormControl = new FormControl();
  readonly processedCurrencies$ = new BehaviorSubject<SwuiSelectOption[]>([]);

  private readonly _baseCurrency$ = new BehaviorSubject<string>('');
  private readonly _destroyed$ = new Subject<void>();

  private sourceCurrencies: FeatureCurrency[] = [];

  constructor( private readonly fb: FormBuilder,
               private readonly jpnService: JpnService,
               private readonly cdr: ChangeDetectorRef,
               private readonly currenciesManagerService: CurrenciesManagerService,
  ) {
    this.form = this.initForm();

    this.processedCurrencies$
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: SwuiSelectOption[] ) => {
        this.filteredCurrencies = [...val];
      });

    this.form.valueChanges
      .pipe(
        takeUntil(this._destroyed$),
      )
      .subscribe(
        ( value: { currencies: FeatureCurrency[] } ) => {
          let currentValue = value.currencies;

          this.isApplyingRatesMessageVisible = false;

          if (Array.isArray(this.initialCurrencies) && this.initialCurrencies.length &&
            this.initialCurrencies.every(item => item.currentRate)) {
            this.initialCurrencies.forEach(item => {
              if (currentValue.find(curr => curr.code === item.code && curr.rate !== item.currentRate)) {
                this.isApplyingRatesMessageVisible = true;
                return;
              }
            });
          }
        }
      );
  }

  ngOnInit(): void {
    this.currenciesArray.valueChanges
      .pipe(
        distinctUntilChanged(( a, b ) => JSON.stringify(a) === JSON.stringify(b)),
        takeUntil(this._destroyed$),
      )
      .subscribe(() => {
        const value = this.currenciesArray.getRawValue();
        const sortedValue = this.getSortedCurrencies(value);

        if (JSON.stringify(value) !== JSON.stringify(sortedValue)) {
          this.currenciesArray.patchValue(this.getSortedCurrencies(value));
        }

        this.currenciesArray.updateValueAndValidity();

        this.currenciesManagerService.selectedCurrencies = this.getSortedCurrencies(value);
        this.sourceCurrencies = this.getSortedCurrencies(value);
        this.onChange(this.sourceCurrencies);
      });

    this._baseCurrency$
      .pipe(
        filter(( baseCurrency: string ) => !!baseCurrency),
        switchMap(( baseCurrency: string ) => {
          const formValue = this.form.getRawValue();
          const selectedCurrencies = formValue.currencies as FeatureCurrency[];
          if (selectedCurrencies.length) {
            const rates = selectedCurrencies.map(( currency: FeatureCurrency ) => {
              return this.jpnService.getExchangeRate(baseCurrency, currency.code);
            });
            return forkJoin(rates);
          } else {
            return of([]);
          }
        }),
        takeUntil(this._destroyed$)
      )
      .subscribe(( rates: any ) => {
        const processedRates = rates.map(( el: { rate: number, currency: string } ) => ({
          code: el.currency,
          rate: parseFloat(el.rate.toFixed(2))
        }));
        this.currenciesArray.patchValue(processedRates);
      });

    this.searchControl.valueChanges
      .pipe(
        map<string, string>(searchString => searchString.toLowerCase()),
        takeUntil(this._destroyed$)
      )
      .subscribe(( search: string ) => {
        const found = this.processedCurrencies$.value.filter(( option: SwuiSelectOption ) => {
          return option.text && option.text.toLowerCase().indexOf(search) > -1;
        });
        this.filteredCurrencies = search ? found : this.processedCurrencies$.value;
      });
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  initForm(): FormGroup {
    return this.fb.group({
      currencies: this.fb.array([])
    });
  }

  removeCurrency( i: number, currencyCode: string ) {
    this.currenciesArray.removeAt(i);
    const cur = this.processedCurrencies$.value.find(( el: SwuiSelectOption ) => el.id === currencyCode);
    if (cur) {
      cur.disabled = false;
    }
  }

  addCurrency( event: Event, currency: SwuiSelectOption ) {
    if (currency.disabled) {
      event.preventDefault();
      event.stopPropagation();
    } else {
      currency.disabled = true;
      if (this.baseCurrency) {
        this.jpnService.getExchangeRate(this.baseCurrency, currency.id)
          .pipe(
            takeUntil(this._destroyed$)
          ).subscribe(value => {
          this.currenciesArray.push(this.initArrayItem({
            code: currency.text,
            rate: parseFloat(value.rate.toFixed(2))
          }));
          this.cdr.detectChanges();
        });
      } else {
        this.currenciesArray.push(this.initArrayItem({ code: currency.text, rate: 1 }));
      }
    }
  }

  onCloseMenu() {
    this.filteredCurrencies = this.processedCurrencies$.value;
    this.searchControl.setValue('');
  }

  get currenciesArray(): FormArray {
    return this.form.get('currencies') as FormArray;
  }

  writeValue( val: FeatureCurrency[] ): void {
    if (JSON.stringify(val) !== JSON.stringify(this.sourceCurrencies)) {
      const processedVal = val && Array.isArray(val) ? val : [];
      this.currenciesArray.clear();
      if (processedVal.length) {
        let selectedCurrencies = new Set();
        processedVal.forEach(( item: FeatureCurrency ) => {
          this.currenciesArray.push(this.initArrayItem(item));
          selectedCurrencies.add(item.code);
        });
        this.processedCurrencies$.value.forEach(( el: SwuiSelectOption ) => {
          if (selectedCurrencies.has(el.id)) {
            el.disabled = true;
          }
        });
      }
    }
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( isDisabled: boolean ): void {
    this.isDisabled = isDisabled;
    isDisabled ? this.form.disable() : this.form.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid || this.form.status === 'DISABLED' ? null : { invalidForm: { valid: false } };
  }

  goToPayout( event: MouseEvent, currency: FeatureCurrency ) {
    event.preventDefault();
    this.currenciesManagerService.selectedPayoutCurrency = currency;
  }

  stopPropagation( event: MouseEvent ) {
    event.stopPropagation();
  }

  get rateTitle(): string {
    const selected = this.currenciesManagerService.selectedCurrencies || [];
    if (this.baseCurrency) {
      const isBaseCurrencyInSelected = selected.find(( el: FeatureCurrency ) => el.code === this.baseCurrency);
      if (!(selected.length === 1 && isBaseCurrencyInSelected)) {
        return `1 ${this.baseCurrency} =`;
      }
    }
    return '';
  }

  private getSortedCurrencies( value: FeatureCurrency[] ): FeatureCurrency[] {
    let filtered = value.filter(( el: FeatureCurrency ) => el.code !== this.baseCurrency);
    const base = value.find(( el: FeatureCurrency ) => el.code === this.baseCurrency);
    return base ? [base, ...filtered] : filtered;
  }

  private initArrayItem( val?: FeatureCurrency ): FormGroup {
    return this.fb.group({
      code: [val && val.code ? val.code : ''],
      rate: [
        val && val.rate ? val.rate : null, Validators.compose([
          Validators.required,
          numbersOnlyValidator,
          positiveNumbers,
          fractionsNumbersLengthValidator,
          numberMaxLength(8),
        ])
      ]
    });
  }
}
