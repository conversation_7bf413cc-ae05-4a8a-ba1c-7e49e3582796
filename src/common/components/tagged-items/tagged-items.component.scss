.table-games {
  overflow: auto;
}

.loading-overlay {
  text-align: center;
}

.table-sticky {
  &__scroll {
    height: 100%;
  }

  &__row {
    min-height: 48px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    display: flex;
    justify-content: space-between;
    align-items: center;

    &.selected {
      background: #FFF7E8;
    }
  }

  &__checkbox {
    padding: 4px 0 4px 24px;
    overflow: hidden;
    flex: 1;

    &_label {
      overflow: hidden;
      text-overflow: ellipsis;
    }

    mat-checkbox {
      display: flex;
      overflow: hidden;
    }
  }

  &__chips {
    min-width: 250px;
    max-width: 250px;
    width: 250px;
    padding: 4px 24px;
    flex: 1;
  }

  &__info {
    display: flex;
    align-items: center;
    padding-left: 24px;
    flex: 1;
  }

  &__search {
    width: 330px;
    margin-left: unset;
  }

  &__table {
    border-collapse: collapse;
  }
}

.selected {
  background: #FFF7E8;
}

.search-icon {
  color: rgba(0, 0, 0, 0.42);
  margin-right: 4px;
}

.chip {
  color: #fff;
}
