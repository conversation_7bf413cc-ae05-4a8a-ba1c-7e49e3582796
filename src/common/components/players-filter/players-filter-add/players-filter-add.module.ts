import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { SwuiControlMessagesModule } from '@skywind-group/lib-swui';
import { PlayersFilterAddComponent } from './players-filter-add.component';

export const PLAYERS_FILTER_ADD_MODULES = [
  MatFormFieldModule,
  MatInputModule,
  MatIconModule,
  MatButtonModule,
  MatCheckboxModule,
  ReactiveFormsModule,
  SwuiControlMessagesModule,
];


@NgModule({
  declarations: [PlayersFilterAddComponent],
  exports: [PlayersFilterAddComponent],
  imports: [
    CommonModule,
    ...PLAYERS_FILTER_ADD_MODULES,
  ]
})
export class PlayersFilterAddModule { }
