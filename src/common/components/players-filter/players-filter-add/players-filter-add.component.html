<form [formGroup]="form" class="add-player">
  <table class="sw-mat-table">
    <thead>
      <tr>
        <th>PlayerID</th>
        <th>BrandID</th>
        <th class="cell-action"></th>
      </tr>
    </thead>
      <tbody>
      <tr *ngFor="let group of playersArray.controls; let i=index;" [formGroup]="group">
        <td>
          <mat-form-field appearance="outline" class="no-field-padding">
            <input type="text" matInput formControlName="playerId">
          </mat-form-field>
        </td>
        <td>
          <ng-container *ngIf="brandId; else inputTpl">
            {{brandId}}
          </ng-container>
          <ng-template #inputTpl>
            <mat-form-field appearance="outline" class="no-field-padding">
              <input type="text" matInput formControlName="brandId">
            </mat-form-field>
          </ng-template>
        </td>
        <td class="cell-action">
          <button mat-icon-button (click)="removePlayer(i)">
            <mat-icon>clear</mat-icon>
          </button>
        </td>
      </tr>
    </tbody>
  </table>
  <button mat-button color="primary" (click)="addPlayer()" class="add-player__add mat-button-md">
    <mat-icon>add</mat-icon>
    Add player
  </button>

  <mat-checkbox *ngIf="!brandId" [formControl]="removeExistingControl" class="add-player__existing">
    Remove existing players
  </mat-checkbox>
</form>
