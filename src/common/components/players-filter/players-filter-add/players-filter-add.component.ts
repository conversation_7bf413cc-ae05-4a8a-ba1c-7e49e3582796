import { Component, EventEmitter, forwardRef, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import { Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { getProcessedPlayers, PlayerRecord, PlayerRecordsData, PlayersFilter, PlayersFilterOperator } from '../players-filter.model';
import { PlayersFilterService } from '../players-filter.service';


@Component({
  selector: 'sw-players-filter-add',
  templateUrl: './players-filter-add.component.html',
  styleUrls: ['./players-filter-add.component.scss'],
  providers: [
    { provide: ErrorStateMatcher, useExisting: forwardRef(() => PlayersFilterService) }
  ]
})
export class PlayersFilterAddComponent implements OnInit, OnD<PERSON>roy {
  @Output() playersFilter = new EventEmitter<PlayersFilter>();
  @Output() isValid = new EventEmitter<boolean>();
  @Input() operators: Map<string, PlayersFilterOperator> = new Map<string, PlayersFilterOperator>();
  @Input() decryptedOperators: Map<string, PlayersFilterOperator> = new Map<string, PlayersFilterOperator>();

  @Input()
  set brandId( val: string | undefined ) {
    if (!val) {
      return;
    }
    this._brandId = val;
    this.playersArray.controls.forEach(( control: AbstractControl ) => {
      control.patchValue({ brandId: val }, { emitEvent: false });
    });
  }

  get brandId(): string | undefined {
    return this._brandId;
  }

  form: FormGroup;
  private _brandId: string | undefined;
  private readonly _destroyed$ = new Subject<void>();

  constructor( private fb: FormBuilder,
               private formService: PlayersFilterService ) {
    this.form = this.initForm();
  }

  ngOnInit(): void {
    this.formService.formSubmitted$
      .pipe(
        filter(val => !!val),
        takeUntil(this._destroyed$)
      )
      .subscribe(() => {
        this.form.markAllAsTouched();
      });

    this.form.valueChanges
      .pipe(
        filter(val => !!val),
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: PlayerRecordsData ) => {
        const result = {
          brands: getProcessedPlayers(this.processDecryptedBrands(val.players)),
          removeExisting: val.removeExisting
        };
        if (this.form.valid) {
          this.playersFilter.emit(result);
        }
      });

    this.form.statusChanges
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe( val => {
        this.isValid.emit(val === 'VALID');
      });
  }

  ngOnDestroy(): void {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  addPlayer() {
    this.playersArray.push(this.initPlayerGroup());
  }

  removePlayer( i: number ) {
    this.playersArray.removeAt(i);
  }

  get playersArray(): FormArray {
    return this.form.get('players') as FormArray;
  }

  get removeExistingControl(): FormControl {
    return this.form.get('removeExisting') as FormControl;
  }


  private processDecryptedBrands( players: PlayerRecord[] ): PlayerRecord[] {
    const regexDigitsOnly = /^\d*$/;
    return players.map(( record: PlayerRecord ) => {
      const isDecrypted = record.brandId && !!regexDigitsOnly.test(record.brandId);
      const operator = isDecrypted ? this.decryptedOperators.get(record.brandId)?.name : this.operators.get(record.brandId)?.name;
      record.operator = operator || '';
      record.decryptedBrand = isDecrypted ? record.brandId : this.operators.get(record.brandId)?.decryptedBrand;
      record.brandId = !isDecrypted ? record.brandId : this.decryptedOperators.get(record.brandId)?.id || '';
      return record;
    });
  }

  private initForm(): FormGroup {
    return this.fb.group({
      players: this.fb.array([this.initPlayerGroup()]),
      removeExisting: [false]
    });
  }

  private initPlayerGroup(): FormGroup {
    return this.fb.group({
      playerId: [],
      brandId: [this.brandId || '', Validators.required]
    });
  }
}
