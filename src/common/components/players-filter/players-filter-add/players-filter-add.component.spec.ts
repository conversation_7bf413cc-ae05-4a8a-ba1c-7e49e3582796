import { CommonModule } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { PlayersFilterService } from '../players-filter.service';

import { PlayersFilterAddComponent } from './players-filter-add.component';
import { PLAYERS_FILTER_ADD_MODULES } from './players-filter-add.module';

describe('PlayersFilterAddComponent', () => {
  let component: PlayersFilterAddComponent;
  let fixture: ComponentFixture<PlayersFilterAddComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        NoopAnimationsModule,
        ...PLAYERS_FILTER_ADD_MODULES,
      ],
      declarations: [ PlayersFilterAddComponent ],
      providers: [
        PlayersFilterService
      ],
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PlayersFilterAddComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
