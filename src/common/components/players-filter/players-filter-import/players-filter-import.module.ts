import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { PlayersFilterImportComponent } from './players-filter-import.component';

export const PLAYERS_FILTER_IMPORT_MODULES = [
  MatButtonModule,
];


@NgModule({
  declarations: [PlayersFilterImportComponent],
  exports: [PlayersFilterImportComponent],
  imports: [
    CommonModule,
    TranslateModule,
    ...PLAYERS_FILTER_IMPORT_MODULES,
  ]
})
export class PlayersFilterImportModule { }
