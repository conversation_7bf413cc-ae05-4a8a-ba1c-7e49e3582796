<div class="import-csv">
  <p *ngIf="importDescription">{{importDescription | translate}}</p>
  <p>Your CSV file should have <b>PlayerID</b> and <b>BrandID</b> columns.</p>
  <p>Example:</p>
  <img src="data:image/png;base64,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" alt>
  <div class="import-csv__wrapper">
    <div class="import-csv__area">
      <button mat-flat-button color="primary" class="import-csv__button mat-button-md">
        <label for="import-csv">
          <input
            #fileImportInput
            type="file"
            accept=".csv"
            id="import-csv"
            (change)="fileChangeListener($event)">
          Choose file
        </label>
      </button>
      <div class="import-csv__text">
        <div class="import-csv__type">File type CSV</div>
        <div class="import-csv__rows">Max rows: 50,000</div>
      </div>
    </div>
    <div *ngIf="!isCsvValid" class="import-csv__invalid">
      {{ 'VALIDATION.invalidCsv' | translate }}
    </div>
    <div *ngIf="isMaximumNumberOfRowsExceeded" class="import-csv__invalid">
      {{ 'VALIDATION.maximumNumberOfRowsIsExceeded' | translate }}
    </div>
  </div>

</div>
