import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';
import { PlayerRecord } from '../players-filter.model';


@Component({
  selector: 'sw-players-filter-import',
  templateUrl: './players-filter-import.component.html',
  styleUrls: ['./players-filter-import.component.scss']
})
export class PlayersFilterImportComponent implements OnInit, OnDestroy {
  @Input() importDescription = '';
  @Output() csvFileImport = new EventEmitter<PlayerRecord[]>();
  @Output() csvValid = new EventEmitter<boolean>();
  fileName = '';
  isCsvValid = true;
  isMaximumNumberOfRowsExceeded = false;
  readonly MAXIMUM_ROWS = 50000;
  @ViewChild('fileImportInput') fileImportInput: any;
  private readonly _destroyed$ = new Subject<void>();

  constructor( private translate: TranslateService ) {
  }

  ngOnInit(): void {
  }

  ngOnDestroy(): void {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  fileChangeListener( $event: Event ) {
    this.isCsvValid = true;
    this.isMaximumNumberOfRowsExceeded = false;
    const target = $event.target as HTMLInputElement;
    let files = target.files;

    if (files && files[0] && PlayersFilterImportComponent.isCSVFile(files[0])) {
      this.fileName = files[0].name;
      let reader = new FileReader();
      if (target.files !== null) {
        reader.readAsText(target.files[0]);
      }

      reader.onload = () => {
        let csvData = reader.result;
        let csvRecordsArray = (csvData as string).split(/\r\n|\n/);

        let headersRow = this.getHeaderArray(csvRecordsArray);

        if (Array.isArray(headersRow) && headersRow.length === 2) {
          this.isCsvValid = headersRow[0].toLowerCase() === 'playerid' && headersRow[1].toLowerCase() === 'brandid';
        } else {
          this.isCsvValid = false;
        }

        if (this.isCsvValid) {
          let dataArr = this.getDataRecordsArrayFromCSVFile(csvRecordsArray);
          if (dataArr?.length > this.MAXIMUM_ROWS) {
            this.isMaximumNumberOfRowsExceeded = true;
          } else {
            this.csvFileImport.emit(dataArr);
          }
        }
        this.csvValid.emit(this.isCsvValid && !this.isMaximumNumberOfRowsExceeded);
      };

      reader.onerror = () => {
        alert(this.translate.instant('TOURNAMENT.FORM.SEGMENTATION.unableToRead') + (target.files ? target.files[0] : ''));
      };

    }
     else {
      alert(this.translate.instant('TOURNAMENT.FORM.SEGMENTATION.importValidFile'));
      this.fileReset();
    }
  }

  private fileReset() {
    this.fileImportInput.nativeElement.value = '';
    this.csvFileImport.emit([]);
  }

  private getDataRecordsArrayFromCSVFile( csvRecordsArray: string[] ): PlayerRecord[] {
    let dataArr: PlayerRecord[] = [];
    let headersRow = this.getHeaderArray(csvRecordsArray);
    if (csvRecordsArray) {
      csvRecordsArray.forEach(( record: any ) => {
        let data = this.createArray(record as string);
        if (Array.isArray(data) && data.length === headersRow.length) {
          const csvRecord = {
            playerId: data[0].trim(),
            brandId: data[1].trim()
          };
          dataArr.push(csvRecord);
        }
      });
      dataArr.shift();
    }
    return dataArr;
  }

  private static isCSVFile( file: any ) {
    return file.name.endsWith('.csv');
  }

  private getHeaderArray( csvRecordsArr: any ) {
    let headerArray: any[] = [];
    if (Array.isArray(csvRecordsArr)) {
      let headers = this.createArray(csvRecordsArr[0] as string);
      headers.forEach(header => headerArray.push(header));
    }
    return headerArray;
  }

  private createArray(str: string) {
    const firstArr = str.split(';');
    let result: string[] = [];
    firstArr.forEach( (el: any) => {
      el.split(',').forEach( (item: string) => {
        result.push(item);
      });
    });
    return result;
  }

}
