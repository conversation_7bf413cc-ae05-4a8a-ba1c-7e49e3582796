import { CommonModule } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TranslateModule } from '@ngx-translate/core';

import { PlayersFilterImportComponent } from './players-filter-import.component';
import { PLAYERS_FILTER_IMPORT_MODULES } from './players-filter-import.module';

describe('PlayersFilterImportComponent', () => {
  let component: PlayersFilterImportComponent;
  let fixture: ComponentFixture<PlayersFilterImportComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        TranslateModule.forRoot(),
        ...PLAYERS_FILTER_IMPORT_MODULES
      ],
      declarations: [ PlayersFilterImportComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PlayersFilterImportComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
