.players-filter-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}
.players-filter-title {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80px;
  margin: 0;
  font-size: 24px;
  line-height: 24px;
  text-align: center;
}
.players-filter-content {
  position: relative;
  width: 100%;
  height: calc(100% - 152px);
  margin: 0;
  padding: 0 32px 32px;
}
.players-filter-close {
  position: absolute;
  top: 0;
  right: 0;
  color: rgba(0, 0, 0, 0.6);
}
.players-filter-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 72px;
  width: 100%;
  background: #EBEFF3;
  button {
    text-transform: uppercase;
    font-size: 14px;
    line-height: 36px;
    font-weight: 500;
    margin-left: 16px;
    &:first-child {
      margin-left: 0;
    }
  }
}
.color-blue {
  color: #1468CF;
}
