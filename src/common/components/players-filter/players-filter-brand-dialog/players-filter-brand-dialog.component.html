<div class="players-filter-wrapper">
  <h3 mat-dialog-title class="players-filter-title">{{'Add Player Filter'}}</h3>
  <div mat-dialog-content class="players-filter-content">
    <sw-players-filter-add
      [brandId]="brandId"
      (playersFilter)="onPlayersFilterChange($event)">
    </sw-players-filter-add>
  </div>
  <div mat-dialog-actions class="players-filter-actions">
    <button mat-flat-button (click)="onClose()" class="color-blue">Cancel</button>
    <button mat-flat-button color="primary" (click)="onSave()">Save filter</button>
  </div>
  <button mat-icon-button class="players-filter-close" (click)="onClose()">
    <mat-icon>clear</mat-icon>
  </button>
</div>

