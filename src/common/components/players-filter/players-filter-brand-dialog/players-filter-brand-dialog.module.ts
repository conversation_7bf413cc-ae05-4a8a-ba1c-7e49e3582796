import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { PlayersFilterAddModule } from '../players-filter-add/players-filter-add.module';
import { PlayersFilterBrandDialogComponent } from './players-filter-brand-dialog.component';

export const PLAYERS_FILTER_BRAND_MODULES = [
  MatDialogModule,
  MatButtonModule,
  MatIconModule,
  PlayersFilterAddModule,
];

@NgModule({
  declarations: [PlayersFilterBrandDialogComponent],
  exports: [PlayersFilterBrandDialogComponent],
  imports: [
    CommonModule,
    ...PLAYERS_FILTER_BRAND_MODULES,
  ]
})
export class PlayersFilterBrandDialogModule { }
