import { CommonModule } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { PlayersFilterService } from '../players-filter.service';

import { PlayersFilterBrandDialogComponent } from './players-filter-brand-dialog.component';
import { PLAYERS_FILTER_BRAND_MODULES } from './players-filter-brand-dialog.module';

describe('PlayersFilterBrandDialogComponent', () => {
  let component: PlayersFilterBrandDialogComponent;
  let fixture: ComponentFixture<PlayersFilterBrandDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        NoopAnimationsModule,
        ...PLAYERS_FILTER_BRAND_MODULES
      ],
      declarations: [ PlayersFilterBrandDialogComponent ],
      providers: [
        PlayersFilterService,
        { provide: MAT_DIALOG_DATA, useValue: {} },
        { provide: MatDialogRef, useValue: {} },
      ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PlayersFilterBrandDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
