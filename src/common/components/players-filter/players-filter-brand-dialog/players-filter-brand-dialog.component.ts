import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { PlayersFilter } from '../players-filter.model';

@Component({
  selector: 'sw-players-filter-brand-dialog',
  templateUrl: './players-filter-brand-dialog.component.html',
  styleUrls: ['./players-filter-brand-dialog.component.scss']
})
export class PlayersFilterBrandDialogComponent {
  brandId: string;

  private playersFilter?: PlayersFilter;

  constructor(private dialogRef: MatDialogRef<PlayersFilterBrandDialogComponent>,
              @Inject(MAT_DIALOG_DATA) public data: { brandId: string }) {
    this.brandId = data.brandId;
  }

  onClose(): void {
    this.dialogRef.close();
  }

  onSave(): void {
    this.dialogRef.close(this.playersFilter);
  }

  onPlayersFilterChange( value: PlayersFilter ) {
    this.playersFilter = value;
  }

}
