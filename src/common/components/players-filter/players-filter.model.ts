import { SegmentationBrand } from '../../../components/pages/interfaces/feature';

export interface PlayerRecord {
  playerId: string;
  brandId: string;
  operator?: string;
  decryptedBrand?: string;
}

export interface PlayersFilterOperator {
  name: string;
  decryptedBrand?: string;
  id?: string;
}

export interface PlayerRecordsData {
  players: PlayerRecord[];
  removeExisting: boolean;
}

export interface PlayersFilter {
  brands: SegmentationBrand[];
  removeExisting?: boolean;
}

export interface PlayersFilterTranslations {
  visibleTo: string;
  visibleAll: string;
}

export interface ListRecordPlayer {
  id: string;
  checked: boolean;
}

export interface ListRecord {
  operator?: PlayersFilterOperator;
  players: ListRecordPlayer[];
  expanded?: boolean;
}

export function getProcessedPlayers( records: PlayerRecord[] ): SegmentationBrand[] {
  const map = records.reduce(( res: Record<string, {players: string[], decryptedBrand: string}>, obj ) => {
    const players: string[] = res[obj.brandId]?.players || [];
    const decryptedBrand = obj.decryptedBrand || '';
    if (obj.playerId && !players.some(id => id === obj.playerId)) {
      players.push(obj.playerId);
    }
    res[obj.brandId] = {players, decryptedBrand};
    return res;
  }, {});

  return Object.entries(map)
    .reduce(( res: SegmentationBrand[], [id, { players, decryptedBrand }] ) => {
      res.push({
        id,
        players: players,
        decryptedBrand: decryptedBrand
      });
      return res;
    }, []);
}









