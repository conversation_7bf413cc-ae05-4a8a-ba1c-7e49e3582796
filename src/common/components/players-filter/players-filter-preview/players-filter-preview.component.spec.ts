import { CommonModule } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { SwDexieService, SwHubAuthService, SwuiGridModule } from '@skywind-group/lib-swui';
import { MockAuthService } from '../../../services/mock-auth.service';

import { PlayersFilterPreviewComponent } from './players-filter-preview.component';
import { PLAYERS_FILTER_PREVIEW_MODULES } from './players-filter-preview.module';

describe('PlayersFilterPreviewComponent', () => {
  let component: PlayersFilterPreviewComponent;
  let fixture: ComponentFixture<PlayersFilterPreviewComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        NoopAnimationsModule,
        HttpClientTestingModule,
        RouterTestingModule,
        SwuiGridModule.forRoot(),
        TranslateModule.forRoot(),
        ...PLAYERS_FILTER_PREVIEW_MODULES,
      ],
      declarations: [ PlayersFilterPreviewComponent ],
      providers: [
        { provide: SwHubAuthService, useClass: MockAuthService },
        {
          provide: SwDexieService, useValue: {
            getFilterState() {
              return Promise.resolve({});
            }
          }
        }
      ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PlayersFilterPreviewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
