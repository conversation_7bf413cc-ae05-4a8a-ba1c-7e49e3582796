import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { SwHubAuthService, SwuiGridField } from '@skywind-group/lib-swui';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { filter, startWith, takeUntil } from 'rxjs/operators';
import { getProcessedPlayers, PlayerRecord, PlayersFilter } from '../players-filter.model';
import { SCHEMA } from './schema';

@Component({
  selector: 'sw-players-filter-preview',
  templateUrl: './players-filter-preview.component.html',
  styleUrls: ['./players-filter-preview.component.scss']
})
export class PlayersFilterPreviewComponent implements OnInit, OnDestroy {
  @Input()
  set csvRecords( val: PlayerRecord[] ) {
    this._csvRecords$.next(val);
  }

  get csvRecords(): PlayerRecord[] {
    return this._csvRecords$.value;
  }

  @Output() playersFilter = new EventEmitter<PlayersFilter>();

  schema: SwuiGridField[] = [];
  removeExistingControl = new FormControl(false);
  isSuperAdmin = false;
  private _csvRecords$ = new BehaviorSubject<PlayerRecord[]>([]);
  private readonly _destroyed$ = new Subject<void>();

  constructor( { isSuperAdmin }: SwHubAuthService ) {
    this.isSuperAdmin = isSuperAdmin;
    this.schema = this.getSchema(SCHEMA);
  }

  ngOnInit(): void {
    combineLatest([this._csvRecords$, this.removeExistingControl.valueChanges.pipe(startWith(false))])
      .pipe(
        filter(( [records] ) => !!records.length),
        takeUntil(this._destroyed$)
      )
      .subscribe(( [records, removeExisting] ) => {
        const result = {
          brands: getProcessedPlayers(records),
          removeExisting: removeExisting
        };
        this.playersFilter.emit(result);
      });
  }

  ngOnDestroy(): void {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  private getSchema(schema: SwuiGridField[]): SwuiGridField[] {
    if (!this.isSuperAdmin) {
      return schema.filter( el => el.field !== 'decryptedBrand');
    }
    return schema;
  }

}
