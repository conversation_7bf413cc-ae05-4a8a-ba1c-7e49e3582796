import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { SwuiGridModule } from '@skywind-group/lib-swui';
import { PlayersFilterPreviewComponent } from './players-filter-preview.component';

export const PLAYERS_FILTER_PREVIEW_MODULES = [
  MatCheckboxModule,
  ReactiveFormsModule,
];


@NgModule({
  declarations: [PlayersFilterPreviewComponent],
  exports: [PlayersFilterPreviewComponent],
  imports: [
    CommonModule,
    SwuiGridModule,
    ...PLAYERS_FILTER_PREVIEW_MODULES,
  ]
})
export class PlayersFilterPreviewModule { }
