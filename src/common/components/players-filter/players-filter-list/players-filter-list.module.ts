import { ScrollingModule } from '@angular/cdk/scrolling';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { PlayersFilterBrandDialogModule } from '../players-filter-brand-dialog/players-filter-brand-dialog.module';
import { PlayersFilterListComponent } from './players-filter-list.component';

export const PLAYERS_FILTER_LIST_MODULES = [
  MatButtonModule,
  MatCheckboxModule,
  MatIconModule,
  MatDialogModule,
  MatInputModule,
  ReactiveFormsModule,
  FormsModule,
  PlayersFilterBrandDialogModule,
  ScrollingModule,
];

@NgModule({
  declarations: [PlayersFilterListComponent],
  exports: [PlayersFilterListComponent],
  imports: [
    CommonModule,
    ...PLAYERS_FILTER_LIST_MODULES,
  ]
})
export class PlayersFilterListModule {
}
