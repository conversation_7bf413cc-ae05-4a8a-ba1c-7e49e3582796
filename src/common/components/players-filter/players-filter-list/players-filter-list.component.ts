import {
  ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatDialog } from '@angular/material/dialog';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { debounceTime, filter, map, takeUntil } from 'rxjs/operators';
import { SegmentationBrand } from '../../../../components/pages/interfaces/feature';
import { ShortEntity } from '../../../models/entity';
import { EntityService } from '../../../services/entity.service';
import { PlayersFilterBrandDialogComponent } from '../players-filter-brand-dialog/players-filter-brand-dialog.component';
import { PlayersFilterDialogComponent } from '../players-filter-dialog/players-filter-dialog.component';
import { ListRecord, ListRecordPlayer, PlayersFilter, PlayersFilterOperator } from '../players-filter.model';


@Component({
  selector: 'sw-players-filter-list',
  templateUrl: './players-filter-list.component.html',
  styleUrls: ['./players-filter-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PlayersFilterListComponent implements OnInit, OnDestroy {
  @Input()
  set filter( val: PlayersFilter ) {
    if (!val) {
      return;
    }
    this._filter$.next(val);
  }

  @Input()
  set disabled( val: boolean ) {
    this._disabled = !!val;
  }

  get disabled(): boolean {
    return this._disabled;
  }

  @Input() importDescription = '';

  @Output() filterBrands = new EventEmitter<SegmentationBrand[]>();
  searchControl = new FormControl();
  records$ = new BehaviorSubject<Map<string, ListRecord> | undefined>(undefined);
  filteredRecords = new Map<string, ListRecord>();
  isSuperAdmin = false;

  get records(): Map<string, ListRecord> | undefined {
    return this.records$.value;
  }

  private _filter$ = new BehaviorSubject<PlayersFilter | null>(null);
  private _operators = new Map<string, PlayersFilterOperator>();
  private _decryptedOperators = new Map<string, PlayersFilterOperator>();
  private _disabled = false;
  private readonly _destroyed$ = new Subject<void>();

  constructor( private dialog: MatDialog,
               private entityService: EntityService,
               private cdr: ChangeDetectorRef,
               { isSuperAdmin }: SwHubAuthService,
  ) {
    this.isSuperAdmin = isSuperAdmin;
  }

  ngOnInit(): void {
    this.searchControl.valueChanges
      .pipe(
        debounceTime(300),
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: string ) => {
        this.filteredRecords = this.getFilteredRecords(val);
        this.cdr.detectChanges();
      });

    this.records$
      .pipe(
        filter(val => !!val),
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: Map<string, ListRecord> | undefined ) => {
        const brands = this.getFilteredBrands(val as Map<string, ListRecord>);
        this.filteredRecords = this.getFilteredRecords(this.searchControl.value);
        this.filterBrands.emit(brands);
      });

    combineLatest([this.entityService.structure$, this._filter$])
      .pipe(
        filter(( [operators] ) => {
          return !!operators;
        }),
        map(( [operators, playersFilter] ) => {
          const newOperators = new Map<string, PlayersFilterOperator>();
          const decryptedOperators = new Map<string, PlayersFilterOperator>();
          if (operators) {
            shortStructureToOperatorsArray(operators).forEach(operator => {
              newOperators.set(operator.id, {
                name: operator.name,
                decryptedBrand: operator.decryptedBrand
              });
              if (operator.decryptedBrand) {
                decryptedOperators.set(operator.decryptedBrand.toString(), {
                  name: operator.name,
                  id: operator.id
                });
              }
            });
          }
          return [newOperators, decryptedOperators, playersFilter as PlayersFilter];
        }),
        takeUntil(this._destroyed$)
      )
      .subscribe(( [operators, decryptedOperators, playersFilter] ) => {
        this._operators = operators as Map<string, PlayersFilterOperator>;
        this._decryptedOperators = decryptedOperators as Map<string, PlayersFilterOperator>;
        const newFilter = playersFilter as PlayersFilter;
        this.records$.next(this.getNewFilterRecords(newFilter ? newFilter.brands : [], this._operators));
      });

  }

  ngOnDestroy(): void {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  addFilter( event: MouseEvent ) {
    event.preventDefault();
    const dialogRef = this.dialog.open(PlayersFilterDialogComponent, {
      width: '766px',
      panelClass: 'players-filter-dialog',
      data: {
        operators: this._operators,
        decryptedOperators: this._decryptedOperators,
        importDescription: this.importDescription
      }
    });

    dialogRef.afterClosed()
      .pipe(
        filter(val => !!val),
        takeUntil(this._destroyed$)
      )
      .subscribe(( playersFilter: PlayersFilter ) => {
        this.records$.next(this.concatRecords(playersFilter));
      });
  }

  addBrandFilter( $event: MouseEvent, brandId: string ) {
    $event.preventDefault();
    $event.stopPropagation();
    const dialogRef = this.dialog.open(PlayersFilterBrandDialogComponent, {
      width: '766px',
      panelClass: 'players-filter-dialog',
      data: {
        brandId
      }
    });

    dialogRef.afterClosed()
      .pipe(
        filter(val => !!val),
        takeUntil(this._destroyed$)
      )
      .subscribe(( playersFilter: PlayersFilter ) => {
        this.records$.next(this.concatRecords(playersFilter));
      });
  }

  deleteBrandFilter( $event: MouseEvent, brandId: string ) {
    $event.preventDefault();
    $event.stopPropagation();

    const newRecords = new Map<string, ListRecord>(this.records || []);
    const record = newRecords.get(brandId);
    if (record) {
      record.players = [];
      this.records$.next(newRecords);
    }
  }

  deleteCheckedPlayers( brandId: string ) {
    const newRecords = new Map<string, ListRecord>(this.records || []);
    const record = newRecords.get(brandId);

    if (record) {
      record.players = record.players.filter(t => !t.checked);
      if (record.players.length === 0) {
        record.expanded = false;
      }
      this.records$.next(newRecords);
    }
  }

  deletePlayer( player: ListRecordPlayer, brandId: string ) {
    const newRecords = new Map<string, ListRecord>(this.records || []);
    const record = newRecords.get(brandId);
    if (record) {
      record.players = record.players.filter(el => el.id !== player.id);
      this.records$.next(newRecords);
    }
  }

  togglePanel( brandId: string ) {
    const newRecords = new Map<string, ListRecord>(this.records || []);
    const record = newRecords.get(brandId);
    if (!!record?.players.length) {
      record.expanded = !record.expanded;
    }
    this.records$.next(newRecords);
  }

  toggleCheckAll( $event: MatCheckboxChange, brandId: string ) {
    const newRecords = new Map<string, ListRecord>(this.records || []);
    const record = newRecords.get(brandId);
    if (record) {
      record.players.forEach(( player: ListRecordPlayer ) => {
        player.checked = $event.checked;
      });
      this.records$.next(newRecords);
    }
  }

  allChecked( players: ListRecordPlayer[] ): boolean {
    return players && !!players.length && players.every(t => t.checked);
  }

  somePlayersChecked( players: ListRecordPlayer[] ): boolean {
    return players.filter(t => t.checked).length > 0 && !players.every(t => t.checked);
  }

  checkedPlayersAmount( players: ListRecordPlayer[] ): number {
    return players.filter(t => t.checked).length;
  }

  private concatRecords( playersFilter: PlayersFilter ): Map<string, ListRecord> {
    const newFilterRecords = this.getNewFilterRecords(playersFilter ? playersFilter.brands : [], this._operators);
    let existingRecords = new Map<string, ListRecord>(this.records || []);

    if (playersFilter && playersFilter.removeExisting) {
      existingRecords = newFilterRecords;
    } else {
      newFilterRecords.forEach(( record: ListRecord, brandId: string ) => {
        if (existingRecords.has(brandId)) {
          const existingRecord = existingRecords.get(brandId);
          if (existingRecord) {
            const existingPlayers = existingRecord.players.map(( player: ListRecordPlayer ) => player.id);
            const existingRecordPlayersSet = new Set(existingPlayers);
            record.players.forEach(( player: ListRecordPlayer ) => {
              if (!existingRecordPlayersSet.has(player.id)) {
                existingRecord.players.push(player);
              }
            });
            if (!existingPlayers.length) {
              record.expanded = record.players.length < 6;
            }
          }
        } else {
          existingRecords.set(brandId, record);
        }
      });
    }
    return existingRecords;
  }

  private getFilteredBrands( records: Map<string, ListRecord> ): SegmentationBrand[] {
    const brands: SegmentationBrand[] = [];
    records.forEach((( value: ListRecord, key: string ) => {
      if (!!this._operators.get(key)) {
        const players = value.players.map(( player: ListRecordPlayer ) => player.id);
        brands.push({
          id: key,
          players
        });
      }
    }));
    return brands;
  }

  private getNewFilterRecords( brands: SegmentationBrand[], operators: Map<string, PlayersFilterOperator> ): Map<string, ListRecord> {
    const recordsMap = new Map<string, ListRecord>();
    brands.forEach(( el: SegmentationBrand ) => {
      const players = el.players.map(( id: string ) => {
        return {
          id,
          checked: false
        };
      });
      recordsMap.set(el.id, {
        operator: operators.get(el.id),
        expanded: players.length < 6,
        players
      });
    });

    return recordsMap;
  }

  private getFilteredRecords( val: string ): Map<string, ListRecord> {
    if (!val) {
      return new Map<string, ListRecord>(this.records || []);
    }

    const searchStr = val.toLowerCase();
    const recordsToFilter = new Map<string, ListRecord>();
    const newRecords = new Map<string, ListRecord>(this.records || []);
    newRecords.forEach(( value: ListRecord, key: string ) => {
      const record = Object.assign({}, value);

      if (key.toLowerCase().includes(searchStr)) {
        recordsToFilter.set(key, record);
      }

      if (this.isSuperAdmin && record.operator?.decryptedBrand?.toString().toLowerCase().includes(searchStr)) {
        recordsToFilter.set(key, record);
      }

      if (record.operator?.name.toLowerCase().includes(searchStr)) {
        recordsToFilter.set(key, record);
      }

      const players: ListRecordPlayer[] = [];
      record.players.forEach(( player: ListRecordPlayer ) => {
        if (player.id.toLowerCase().includes(searchStr)) {
          players.push(player);
        }
      });

      if (!!players.length) {
        record.expanded = true;
        record.players = players;
        recordsToFilter.set(key, record);
      }
    });

    return recordsToFilter;
  }
}

const shortStructureToOperatorsArray = ( item: ShortEntity, resultArray: any[] = [] ) => {
  resultArray.push({ id: item.id, name: item.name, decryptedBrand: item.decryptedBrand });

  if (item.child) {
    item.child.forEach(( i: any ) => shortStructureToOperatorsArray(i, resultArray));
  }

  return resultArray;
};
