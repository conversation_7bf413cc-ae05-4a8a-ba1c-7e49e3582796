$color-border: rgba(0, 0, 0, .12);
.players-table {
  width: 100%;
  border: 1px solid $color-border;
  border-radius: 4px;
  margin-bottom: 16px;
  &--hidden {
    display: none;
  }
  &__wrapper {
    height: 100%;
    width: 100%;
  }
  &__header {
    display: flex;
  }
  &__record {
    border-bottom: 1px solid $color-border;
    &:last-child {
      border: none;
    }
  }
  &__search {
    width: 100%;
    max-width: 350px;
    mat-icon {
      top: 6px;
      margin-right: 5px;
    }
  }
  &__cell {
    display: flex;
    align-items: center;
    height: 56px;
    padding: 0 16px;
    &--th {
      font-weight: 500;
    }
    &--th,
    &--td {
      height: 56px;
      padding: 0 16px;
      text-align: left;
      vertical-align: middle;
      border-bottom: 1px solid $color-border;
    }
    &--operator {
      width: 260px;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    &--brand,
    &--players {
      width: 150px;
    }
    &--actions {
      flex: 1;
    }
  }
}

.player-record {
  overflow: hidden;
  &__panel {
    display: flex;
    &--expandable {
      cursor: pointer;
    }
    button {
      visibility: hidden;
      opacity: 0;
    }
    &:hover {
      button {
        visibility: visible;
        opacity: 1;
        transition: opacity .15s ease-in-out;
      }
    }
  }
  &__actions {
    position: relative;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    height: 100%;
    padding-right: 28px;
  }
  &__indicator {
    position: absolute;
    right: 8px;
    top: 50%;
    margin-top: -7px;
    border-style: solid;
    border-color: rgba(0, 0, 0, .87);
    border-width: 0 2px 2px 0;
    padding: 3px;
    transform: rotate(45deg);
    transition: transform .15s ease;
    &--expanded {
      margin-top: -4px;
      transform: rotate(225deg);
    }
  }
  &__list {
    height: auto;
  }
  &__subpanel {
    display: flex;
    align-items: center;
    width: 100%;
    height: 56px;
    padding: 0 16px 0 48px;
    background: #fafafa;
    border-bottom: 1px solid $color-border;
    border-top: 1px solid $color-border;
  }
  &__player {
    display: flex;
    align-items: center;
    height: 56px;
    width: 100%;
    padding: 0 16px 0 48px;
    border-bottom: 1px solid $color-border;
    &:last-child {
      border-bottom: none;
    }
    button {
      visibility: hidden;
      opacity: 0;
    }
    &:hover {
      button {
        visibility: visible;
        opacity: 1;
        transition: opacity .15s ease-in-out;
      }
    }
  }
  &__checkbox-inner {
    margin-left: 4px;
  }
  &__checkbox-title {
    font-weight: 500;
  }
  &__button {
    margin-left: auto;
  }
}

.filter-button {
  color: #1468cf;
  background: #fff;
  &--red {
    color: #B00020;
  }
}
