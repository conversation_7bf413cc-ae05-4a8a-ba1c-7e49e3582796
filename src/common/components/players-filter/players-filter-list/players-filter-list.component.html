<div class="players-table" [ngClass]="{'players-table--hidden': !records?.size}">
  <div class="players-table__wrapper">
    <div class="players-table__header">
      <div class="players-table__cell players-table__cell--th players-table__cell--operator">Operator</div>
      <div class="players-table__cell players-table__cell--th players-table__cell--brand">Brand ID</div>
      <div *ngIf="isSuperAdmin" class="players-table__cell players-table__cell--th players-table__cell--brand">
        Decrypted ID
      </div>
      <div class="players-table__cell players-table__cell--th players-table__cell--players"># of players</div>
      <div class="players-table__cell players-table__cell--th players-table__cell--actions">
        <mat-form-field appearance="outline" class="no-field-padding players-table__search">
          <mat-icon matPrefix>search</mat-icon>
          <input matInput [formControl]="searchControl" placeholder="Search">
        </mat-form-field>
      </div>
    </div>
    <div class="players-table__body">
      <div *ngFor="let key of filteredRecords?.keys()" class="players-table__record player-record">
        <div
          class="player-record__panel"
          [ngClass]="{
            'player-record__panel--expandable': !!filteredRecords.get(key)?.players.length,
            'player-record__panel--open': filteredRecords.get(key)?.expanded && filteredRecords.get(key)?.players.length
          }"
          (click)="togglePanel(key)">
          <div class="players-table__cell players-table__cell--operator">{{filteredRecords.get(key)?.operator?.name}}</div>
          <div class="players-table__cell players-table__cell--brand">{{key}}</div>
          <div *ngIf="isSuperAdmin" class="players-table__cell players-table__cell--brand">
            {{filteredRecords.get(key)?.operator?.decryptedBrand}}
          </div>
          <div class="players-table__cell players-table__cell--players">{{filteredRecords.get(key)?.players.length || 'All players'}}</div>
          <div class="players-table__cell players-table__cell--actions">
            <div class="player-record__actions">
              <button
                *ngIf="!!filteredRecords.get(key)?.players.length && !disabled"
                mat-stroked-button
                class="player-record__button filter-button filter-button--red mat-button-md"
                (click)="deleteBrandFilter($event, key)">
                Delete Filter
              </button>
              <button
                *ngIf="!filteredRecords.get(key)?.players.length && !disabled"
                mat-stroked-button
                class="player-record__button filter-button"
                (click)="addBrandFilter($event, key)">
                <mat-icon>add</mat-icon>
                Add Filter
              </button>
              <div
                *ngIf="!!filteredRecords.get(key)?.players.length"
                class="player-record__indicator"
                [ngClass]="{'player-record__indicator--expanded': filteredRecords.get(key)?.expanded}">
              </div>
            </div>
          </div>
        </div>
        <div class="player-record__dropdown" *ngIf="filteredRecords.get(key)?.expanded && filteredRecords.get(key)?.players?.length">
          <div class="player-record__subpanel">
            <mat-checkbox
              [disabled]="disabled"
              [checked]="allChecked(filteredRecords.get(key)?.players)"
              [indeterminate]=somePlayersChecked(filteredRecords.get(key)?.players)
              (change)="toggleCheckAll($event, key)">
              <span class="player-record__checkbox-inner player-record__checkbox-title">
                {{checkedPlayersAmount(filteredRecords.get(key)?.players) ? checkedPlayersAmount(filteredRecords.get(key)?.players) + ' selected' : 'Player ID'}}
              </span>
            </mat-checkbox>
            <button
              *ngIf="!!checkedPlayersAmount(filteredRecords.get(key)?.players)"
              mat-stroked-button
              class="player-record__button filter-button filter-button--red"
              (click)="deleteCheckedPlayers(key)">
              DELETE PLAYERS
            </button>
          </div>
          <ng-container *ngIf="filteredRecords.get(key)?.players.length >= 6; else simpleTpl">
            <cdk-virtual-scroll-viewport
              itemSize="56"
              class="player-record__list"
              [ngStyle]="{'height': '336px'}">
              <div class="player-record__player" *cdkVirtualFor="let player of filteredRecords.get(key)?.players">
                <ng-container *ngTemplateOutlet="playerRecord; context: { player: player, key: key }"></ng-container>
              </div>
            </cdk-virtual-scroll-viewport>
          </ng-container>

          <ng-template #simpleTpl>
            <div class="player-record__list">
              <div class="player-record__player" *ngFor="let player of filteredRecords.get(key)?.players">
                <ng-container *ngTemplateOutlet="playerRecord; context: { player: player, key: key }"></ng-container>
              </div>
            </div>
          </ng-template>

        </div>
      </div>
    </div>
  </div>
</div>

<button
  mat-stroked-button
  color="primary"
  class="filter-button mat-button-md"
  [disabled]="disabled"
  (click)="addFilter($event)">
  <mat-icon>add</mat-icon>
  Add player filter
</button>

<ng-template #playerRecord let-player="player" let-key="key">
  <mat-checkbox
    [disabled]="disabled"
    [checked]="player.checked"
    [(ngModel)]="player.checked">
    <span class="player-record__checkbox-inner">{{player.id}}</span>
  </mat-checkbox>
  <button
    *ngIf="!disabled"
    mat-stroked-button
    class="player-record__button filter-button filter-button--red mat-button-md"
    (click)="deletePlayer(player, key)">
    Delete Player
  </button>
</ng-template>
