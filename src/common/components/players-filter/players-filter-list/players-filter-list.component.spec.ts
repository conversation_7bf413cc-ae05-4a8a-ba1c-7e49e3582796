import { CommonModule } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { EntityService } from '../../../services/entity.service';

import { PlayersFilterListComponent } from './players-filter-list.component';
import { PLAYERS_FILTER_LIST_MODULES } from './players-filter-list.module';

describe('PlayersFilterListComponent', () => {
  let component: PlayersFilterListComponent;
  let fixture: ComponentFixture<PlayersFilterListComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        NoopAnimationsModule,
        HttpClientTestingModule,
        RouterTestingModule,
        ...PLAYERS_FILTER_LIST_MODULES,
      ],
      declarations: [ PlayersFilterListComponent ],
      providers: [
        EntityService,
        SwHubAuthService,
      ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PlayersFilterListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
