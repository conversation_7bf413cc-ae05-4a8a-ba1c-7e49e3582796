import { CommonModule } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SwHubAuthService, SwHubConfigService } from '@skywind-group/lib-swui';
import { EntityService, MockEntityService } from '../../services/entity.service';
import { MockAuthService } from '../../services/mock-auth.service';

import { PlayersFilterComponent } from './players-filter.component';
import { PLAYERS_FILTER_MODULES } from './players-filter.module';

describe('PlayersFilterComponent', () => {
  let component: PlayersFilterComponent;
  let fixture: ComponentFixture<PlayersFilterComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        NoopAnimationsModule,
        HttpClientTestingModule,
        ...PLAYERS_FILTER_MODULES
      ],
      declarations: [ PlayersFilterComponent ],
      providers: [
        { provide: SwHubAuthService, useClass: MockAuthService },
        { provide: EntityService, useClass: MockEntityService },
        { provide: SwHubConfigService, useValue: {} },
      ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PlayersFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
