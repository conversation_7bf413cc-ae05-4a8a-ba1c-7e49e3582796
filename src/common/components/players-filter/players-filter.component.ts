import { ChangeDetectorRef, Component, forwardRef, Input, OnDestroy } from '@angular/core';
import { ControlValueAccessor, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors } from '@angular/forms';
import { Subject } from 'rxjs';
import { SegmentationBrand } from '../../../components/pages/interfaces/feature';
import { PlayersFilter, PlayersFilterTranslations } from './players-filter.model';


@Component({
  selector: 'sw-players-filter',
  templateUrl: './players-filter.component.html',
  styleUrls: ['./players-filter.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PlayersFilterComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => PlayersFilterComponent),
      multi: true
    },
  ]
})
export class PlayersFilterComponent implements ControlValueAccessor, OnD<PERSON>roy {
  @Input() translations: PlayersFilterTranslations = {
    visibleTo: 'This feature is visible to:',
    visibleAll: 'This feature is visible to all players'
  };

  @Input() importDescription = '';

  isDisabled = false;
  onChange: ( _: any ) => void = (() => {
  });
  filter?: PlayersFilter;
  brands: SegmentationBrand[] = [];

  private readonly _destroyed$ = new Subject<void>();

  constructor(private cdr: ChangeDetectorRef) {
  }

  ngOnDestroy(): void {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( isDisabled: boolean ): void {
    this.isDisabled = isDisabled;
  }

  writeValue( val: SegmentationBrand[] ): void {
    if (!val) {
      return;
    }
    this.filter = { brands: val };
  }

  onFilterBrands( brands: SegmentationBrand[] ) {
    this.brands = brands;
    this.cdr.detectChanges();
    this.onChange(brands);
  }

  validate(): ValidationErrors | null {
    return null;
  }
}
