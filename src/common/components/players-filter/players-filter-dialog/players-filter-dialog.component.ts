import { Component, Inject, OnDestroy, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTabGroup } from '@angular/material/tabs';
import { PlayerR<PERSON><PERSON>, PlayersFilter, PlayersFilterOperator } from '../players-filter.model';
import { PlayersFilterService } from '../players-filter.service';


@Component({
  selector: 'sw-players-filter-dialog',
  templateUrl: './players-filter-dialog.component.html',
  styleUrls: ['./players-filter-dialog.component.scss'],
})
export class PlayersFilterDialogComponent implements OnDestroy {
  playersFilter?: PlayersFilter;
  csvRecords: PlayerRecord[] = [];
  isCsvValid = false;
  isAddPlayerValid = false;
  operators = new Map<string, PlayersFilterOperator>();
  decryptedOperators = new Map<string, PlayersFilterOperator>();
  selectedIndex = 0;
  isPreview = false;
  importDescription = '';
  @ViewChild('tabs', { static: false }) tabs?: MatTabGroup;

  constructor( private dialogRef: MatDialogRef<PlayersFilterDialogComponent>,
               private formService: PlayersFilterService,
               @Inject(MAT_DIALOG_DATA) public data: {
                  operators: Map<string, PlayersFilterOperator>,
                  decryptedOperators: Map<string, PlayersFilterOperator>,
                  importDescription: string,
               }
  ) {
    this.operators = data.operators;
    this.decryptedOperators = data.decryptedOperators;
    this.importDescription = data.importDescription;
  }

  ngOnDestroy() {
    this.formService.formSubmitted = false;
  }

  onClose(): void {
    this.dialogRef.close();
  }

  onSave(): void {
    this.formService.formSubmitted = true;
    if (this.isAddPlayerValid || this.isCsvValid) {
      this.dialogRef.close(this.playersFilter);
    }
  }

  onSelectedIndexChange( index: number ) {
    this.isAddPlayerValid = false;
    this.selectedIndex = index;
  }

  onPlayersFilterChange( value: PlayersFilter ) {
    this.playersFilter = value;
  }

  onCSVFileImport( value: PlayerRecord[] ) {
    const regexDigitsOnly = /^\d*$/;
    this.csvRecords = value.map((record: PlayerRecord) => {
      const isDecrypted = record.brandId && !!regexDigitsOnly.test(record.brandId);
      const operator = isDecrypted ? this.decryptedOperators.get(record.brandId)?.name : this.operators.get(record.brandId)?.name;
      record.operator = operator || '';
      record.decryptedBrand = isDecrypted ? record.brandId : this.operators.get(record.brandId)?.decryptedBrand;
      record.brandId = !isDecrypted ? record.brandId : this.decryptedOperators.get(record.brandId)?.id || '';
      return record;
    });
  }

  onNext() {
    this.isPreview = true;
  }

  onPrevious() {
    this.isPreview = false;
  }

  onCsvValidate( isValid: boolean ) {
    this.isCsvValid= isValid;
  }

  onAddPlayerValidate( isValid: boolean ) {
    this.isAddPlayerValid = isValid;
  }
}
