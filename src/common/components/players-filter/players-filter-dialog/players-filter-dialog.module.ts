import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { PlayersFilterAddModule } from '../players-filter-add/players-filter-add.module';
import { PlayersFilterImportModule } from '../players-filter-import/players-filter-import.module';
import { PlayersFilterPreviewModule } from '../players-filter-preview/players-filter-preview.module';
import { PlayersFilterService } from '../players-filter.service';
import { PlayersFilterDialogComponent } from './players-filter-dialog.component';

export const PLAYERS_FILTER_DIALOG_MODULES = [
  MatTabsModule,
  MatButtonModule,
  MatDialogModule,
  MatIconModule,
  PlayersFilterAddModule,
  PlayersFilterImportModule,
  PlayersFilterPreviewModule,
];


@NgModule({
  declarations: [PlayersFilterDialogComponent],
  exports: [PlayersFilterDialogComponent],
  imports: [
    CommonModule,
    ...PLAYERS_FILTER_DIALOG_MODULES
  ],
  providers: [
    PlayersFilterService,
  ]
})
export class PlayersFilterDialogModule { }
