<div class="players-filter-wrapper">
  <h3 mat-dialog-title class="players-filter-title">
    {{isPreview ? 'Preview Player List' : 'Add Player Filter'}}
  </h3>
  <div mat-dialog-content class="players-filter-content">
    <mat-tab-group
      #tabs
      class="players-filter-tabs"
      [ngClass]="{'preview-visible': isPreview}"
      animationDuration="0ms"
      (selectedIndexChange)="onSelectedIndexChange($event)"
      [selectedIndex]="selectedIndex">
      <mat-tab>
        <ng-template matTabLabel>Add players</ng-template>
        <ng-template matTabContent>
          <sw-players-filter-add
            [operators]="operators"
            [decryptedOperators]="decryptedOperators"
            (isValid)="onAddPlayerValidate($event)"
            (playersFilter)="onPlayersFilterChange($event)">
          </sw-players-filter-add>
        </ng-template>
      </mat-tab>
      <mat-tab>
        <ng-template matTabLabel>Import players list</ng-template>
        <ng-template matTabContent>
          <sw-players-filter-import
            [importDescription]="importDescription"
            (csvFileImport)="onCSVFileImport($event)"
            (csvValid)="onCsvValidate($event)"
          ></sw-players-filter-import>
        </ng-template>
      </mat-tab>
    </mat-tab-group>
    <div class="players-filter-preview" [ngClass]="{'preview-visible': isPreview}">
      <sw-players-filter-preview [csvRecords]="csvRecords" (playersFilter)="onPlayersFilterChange($event)">
      </sw-players-filter-preview>
    </div>
  </div>
  <div mat-dialog-actions class="players-filter-actions">
    <button *ngIf="!isPreview || selectedIndex === 0" mat-flat-button (click)="onClose()" class="color-blue">
      Cancel
    </button>
    <button *ngIf="isPreview" mat-flat-button (click)="onPrevious()" class="color-blue">Previous</button>
    <button
      *ngIf="!isPreview && selectedIndex === 1"
      mat-flat-button
      color="primary"
      [disabled]="!isCsvValid"
      (click)="onNext()">Next</button>
    <button *ngIf="isPreview || selectedIndex === 0" mat-flat-button color="primary" (click)="onSave()">Save filter</button>
  </div>

  <button mat-icon-button class="players-filter-close" (click)="onClose()">
    <mat-icon>clear</mat-icon>
  </button>
</div>
