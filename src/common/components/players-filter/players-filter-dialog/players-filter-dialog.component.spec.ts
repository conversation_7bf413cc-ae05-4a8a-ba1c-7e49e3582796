import { CommonModule } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { SwDexieService, SwHubAuthService, SwuiGridModule } from '@skywind-group/lib-swui';
import { MockAuthService } from '../../../services/mock-auth.service';
import { PlayersFilterService } from '../players-filter.service';

import { PlayersFilterDialogComponent } from './players-filter-dialog.component';
import { PLAYERS_FILTER_DIALOG_MODULES } from './players-filter-dialog.module';

describe('PlayersFilterDialogComponent', () => {
  let component: PlayersFilterDialogComponent;
  let fixture: ComponentFixture<PlayersFilterDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        NoopAnimationsModule,
        HttpClientTestingModule,
        RouterTestingModule,
        SwuiGridModule.forRoot(),
        TranslateModule.forRoot(),
        ...PLAYERS_FILTER_DIALOG_MODULES
      ],
      declarations: [ PlayersFilterDialogComponent ],
      providers: [
        PlayersFilterService,
        { provide: SwHubAuthService, useClass: MockAuthService },
        { provide: MAT_DIALOG_DATA, useValue: {} },
        { provide: MatDialogRef, useValue: {} },
        {
          provide: SwDexieService, useValue: {
            getFilterState() {
              return Promise.resolve({});
            }
          }
        }
      ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PlayersFilterDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
