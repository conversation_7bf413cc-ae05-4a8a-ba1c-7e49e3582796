import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { PlayersFilterDialogModule } from './players-filter-dialog/players-filter-dialog.module';
import { PlayersFilterListModule } from './players-filter-list/players-filter-list.module';

import { PlayersFilterComponent } from './players-filter.component';


export const PLAYERS_FILTER_MODULES = [
  MatButtonModule,
  MatIconModule,
  MatDialogModule,
  PlayersFilterDialogModule,
  PlayersFilterListModule,
];

@NgModule({
  declarations: [PlayersFilterComponent],
  exports: [PlayersFilterComponent],
  imports: [
    CommonModule,
    ...PLAYERS_FILTER_MODULES,
  ]
})
export class PlayersFilterModule { }
