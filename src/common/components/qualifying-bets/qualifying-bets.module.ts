import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { SwuiCurrencySymbolModule } from '@skywind-group/lib-swui';
import { FloatFormatterModule } from '../../directives/floatFormatter/floatFormatter.module';
import { QualifyingBetsComponent } from './qualifying-bets.component';
import { SettingCurrencyFormatterModule } from '../../directives/appSettingCurrencyFormatter/settingCurrencyFormatter.module';
import { PipesModule } from '../../pipes/pipes.module';

export const QUALIFYING_BETS_MODULES = [
  ReactiveFormsModule,
  MatSlideToggleModule,
  MatFormFieldModule,
  MatInputModule,
  SwuiCurrencySymbolModule,
  FloatFormatterModule,
];


@NgModule({
  declarations: [QualifyingBetsComponent],
  exports: [QualifyingBetsComponent],
    imports: [
        CommonModule,
        ...QUALIFYING_BETS_MODULES,
        SettingCurrencyFormatterModule,
        PipesModule,
    ]
})
export class QualifyingBetsModule {
}
