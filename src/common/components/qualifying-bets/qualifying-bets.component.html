<form [formGroup]="form" *ngIf="!rangesEnabled || (rangesEnabled && isPrizeMultiplierSupported); else emptyTpl">
  <table class="sw-mat-table qualify-table">
    <thead>
    <tr>
      <th></th>
      <ng-container *ngIf="!rangesEnabled">
        <th>
          <div class="qualify-table__heading">
            <div class="qualify-table__name">Min qualifying<br/>single bet</div>
            <mat-slide-toggle [formControl]="minEnabledControl"></mat-slide-toggle>
          </div>
        </th>
        <th *ngIf="isTotalBetAmountSupported">
          <div class="qualify-table__heading">
            <div class="qualify-table__name">Total bet amount<br/>to qualify</div>
            <mat-slide-toggle [formControl]="totalEnabledControl"></mat-slide-toggle>
          </div>
        </th>
      </ng-container>
      <th *ngIf="isPrizeMultiplierSupported">
        <div class="qualify-table__heading">
          <div class="qualify-table__name">Prize Multiplier<br/>Bet Cap</div>
          <mat-slide-toggle [formControl]="multiplierEnabledControl"></mat-slide-toggle>
        </div>
      </th>
    </tr>
    </thead>
    <tbody [formGroup]="betsControl">
    <tr *ngFor="let currencyCode of getCurrencies(betsControl)" formGroupName="{{currencyCode}}">
      <td>{{currencyCode | uppercase}}</td>

      <ng-container *ngIf="!rangesEnabled">
        <td [ngClass]="{'disabled': !minEnabledControl.value}">
          <ng-container *ngIf="currencyCode === baseCurrency;else minTpl">
            <mat-form-field appearance="outline" class="qualify-table__field no-field-padding">
              <span matPrefix>{{currencyCode | currencySymbol}}</span>
              <input
                matInput
                [placeholder]="calcRatedValue(currencyCode, 'min')"
                formControlName="min"
                type="number"
                min="0"
                swCurrencyFormatter [currencyCode]="currencyCode">
            </mat-form-field>
          </ng-container>
          <ng-template #minTpl>
            {{currencyCode | currencySymbol}} {{calcRatedValue(currencyCode, 'min') | currencyFormat: currencyCode}}
          </ng-template>
        </td>
        <td *ngIf="isTotalBetAmountSupported" [ngClass]="{'disabled': !totalEnabledControl.value}">
          <ng-container *ngIf="currencyCode === baseCurrency;else totalTpl">
            <mat-form-field appearance="outline" class="qualify-table__field no-field-padding">
              <span matPrefix>{{currencyCode | currencySymbol}}</span>
              <input
                matInput
                [placeholder]="calcRatedValue(currencyCode, 'total')"
                formControlName="total"
                type="number"
                min="0"
                swCurrencyFormatter [currencyCode]="currencyCode">
            </mat-form-field>
          </ng-container>
          <ng-template #totalTpl>
            {{currencyCode | currencySymbol}} {{calcRatedValue(currencyCode, 'total') | currencyFormat: currencyCode}}
          </ng-template>
        </td>
      </ng-container>

      <td *ngIf="isPrizeMultiplierSupported" [ngClass]="{'disabled': !multiplierEnabledControl.value}">
        <ng-container *ngIf="currencyCode === baseCurrency;else prizeMultiplierTpl">
          <mat-form-field appearance="outline" class="qualify-table__field no-field-padding">
            <span matPrefix>{{currencyCode | currencySymbol}}</span>
            <input
              matInput
              [placeholder]="calcRatedValue(currencyCode, 'prizeMultiplier')"
              formControlName="prizeMultiplier"
              type="number"
              min="0"
              swCurrencyFormatter
              [currencyCode]="currencyCode">
          </mat-form-field>
        </ng-container>
        <ng-template #prizeMultiplierTpl>
          {{currencyCode | currencySymbol}} {{calcRatedValue(currencyCode, 'prizeMultiplier') | currencyFormat: currencyCode }}
        </ng-template>
      </td>
    </tr>
    </tbody>
  </table>
</form>

<ng-template #emptyTpl>
  Set using Bet Ranges
</ng-template>
