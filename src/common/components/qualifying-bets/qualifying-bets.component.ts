import { Component, forwardRef, Input, OnDestroy, OnInit } from '@angular/core';
import {
  ControlValueAccessor,
  FormBuilder,
  FormControl,
  FormGroup,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ValidationErrors,
  Validators
} from '@angular/forms';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { FeatureCurrency, FeatureQualify, FeatureQualifyingBet } from '../../../components/pages/interfaces/feature';
import { fractionsNumbersLengthValidator, numberMaxLength } from '../../lib/validators';


@Component({
  selector: 'sw-qualifying-bets',
  templateUrl: './qualifying-bets.component.html',
  styleUrls: ['./qualifying-bets.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => QualifyingBetsComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => QualifyingBetsComponent),
      multi: true
    },
  ]
})
export class QualifyingBetsComponent implements OnInit, OnDestroy, ControlValueAccessor {
  @Input()
  set baseCurrency( val: string ) {
    this._baseCurrency$.next(val || '');
  }

  get baseCurrency(): string {
    return this._baseCurrency$.value;
  }

  @Input()
  set selectedCurrencies( val: FeatureCurrency[] ) {
    this._selectedCurrencies$.next(val || []);
  }

  get selectedCurrencies(): FeatureCurrency[] {
    return this._selectedCurrencies$.value;
  }

  @Input()
  set isPrizeMultiplierSupported( val: boolean ) {
    this._isPrizeMultiplierSupported$.next(val);
  }

  get isPrizeMultiplierSupported(): boolean {
    return this._isPrizeMultiplierSupported$.value;
  }

  @Input() isTotalBetAmountSupported = false;

  @Input()
  set rangesEnabled( val: boolean ) {
    this._rangesEnabled$.next(val);
  }

  get rangesEnabled(): boolean {
    return this._rangesEnabled$.value;
  }

  isDisabled = false;
  onChange: ( _: any ) => void = (() => {
  });

  readonly form: FormGroup;

  private readonly _baseCurrency$ = new BehaviorSubject<string>('');
  private readonly _selectedCurrencies$ = new BehaviorSubject<FeatureCurrency[]>([]);
  private readonly _isPrizeMultiplierSupported$ = new BehaviorSubject<boolean>(true);
  private readonly _rangesEnabled$ = new BehaviorSubject<boolean>(false);
  private readonly _destroyed$ = new Subject<void>();

  constructor( private readonly fb: FormBuilder ) {
    this.form = this.initForm();
  }

  ngOnInit(): void {
    this.form.valueChanges
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(() => {
        const formValue = this.form.getRawValue();
        const bets = formValue.bets;

        if (!this.isTotalBetAmountSupported) {
          delete formValue?.totalEnabled;
        }
        Object.keys(bets).forEach(( currency: string ) => {
          if (currency !== this.baseCurrency) {
            delete bets[currency];
          } else {
            if (!this.isTotalBetAmountSupported) {
              delete bets[currency]?.total;
            }
          }
        });
        this.onChange(formValue);
      });

    combineLatest([this._baseCurrency$, this._selectedCurrencies$])
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(( [baseCurrency, currencies] ) => {
        Object.keys(this.betsControl.controls).forEach(( key: string ) => {
          if (key !== baseCurrency) {
            this.betsControl.removeControl(key);
          }
        });

        if (baseCurrency) {
          this.betsControl.addControl(baseCurrency, this.initBetGroup());
        }

        currencies.forEach(( cur: FeatureCurrency ) => {
          if (cur.code !== baseCurrency) {
            this.betsControl.addControl(cur.code, this.initBetGroup());
          }
        });

        this.toggleControlsDisabled(this.minEnabledControl.value, 'min');
        this.toggleControlsDisabled(this.totalEnabledControl.value, 'total');
        this.toggleControlsDisabled(this.multiplierEnabledControl.value, 'prizeMultiplier');
      });

    this._isPrizeMultiplierSupported$
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: boolean ) => {
        if (val) {
          this.multiplierEnabledControl.enable();
        } else {
          this.multiplierEnabledControl.disable();
          this.multiplierEnabledControl.reset(false);
          this.toggleControlsDisabled(false, 'prizeMultiplier');
        }
      });

    this._rangesEnabled$
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: boolean ) => {
        if (val) {
          this.minEnabledControl.reset(false);
          this.totalEnabledControl.reset(false);
          this.minEnabledControl.disable();
          this.totalEnabledControl.disable();
        } else {
          this.minEnabledControl.enable();
          this.totalEnabledControl.enable();
        }
      });

    this.minEnabledControl.valueChanges
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: boolean ) => {
        this.toggleControlsDisabled(val, 'min');
      });

    this.totalEnabledControl.valueChanges
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: boolean ) => {
        this.toggleControlsDisabled(val, 'total');
      });

    this.multiplierEnabledControl.valueChanges
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: boolean ) => {
        this.toggleControlsDisabled(val, 'prizeMultiplier');
      });
  }

  ngOnDestroy(): void {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  getCurrencies( group: FormGroup ): string[] {
    return Object.keys(group.controls);
  }

  writeValue( val: FeatureQualify ): void {
    if (!val) {
      return;
    }
    this.form.patchValue(val, { emitEvent: false });
  }

  onTouched: () => void = () => {
  };

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( isDisabled: boolean ): void {
    this.isDisabled = isDisabled;
    isDisabled ? this.form.disable() : this.form.enable();
  }

  validate(): ValidationErrors | null {
    return this.form.valid || this.form.status === 'DISABLED' ? null : { invalidForm: { valid: false } };
  }

  calcRatedValue( currencyCode: string, controlName: string ): number {
    const baseControl = this.baseCurrency ? this.betsControl.controls[this.baseCurrency] : undefined;
    const currencyRate = this.selectedCurrencies.find(( el: FeatureCurrency ) => el.code === currencyCode);
    if (baseControl && baseControl.value[controlName]) {
      return currencyRate ? (baseControl.value[controlName] * currencyRate.rate).toFixed(2) : baseControl.value[controlName].toFixed(2);
    } else {
      return 0;
    }
  }

  get minEnabledControl(): FormControl {
    return this.form.get('minEnabled') as FormControl;
  }

  get totalEnabledControl(): FormControl {
    return this.form.get('totalEnabled') as FormControl;
  }

  get multiplierEnabledControl(): FormControl {
    return this.form.get('multiplierEnabled') as FormControl;
  }

  get betsControl(): FormGroup {
    return this.form.get('bets') as FormGroup;
  }

  private initForm(): FormGroup {
    return this.fb.group({
      minEnabled: [],
      totalEnabled: [],
      multiplierEnabled: [],
      bets: this.fb.group({})
    });
  }

  private initBetGroup( val?: FeatureQualifyingBet ): FormGroup {
    return this.fb.group({
      order: [],
      min: [
        val && val.min || null, Validators.compose([
          fractionsNumbersLengthValidator,
          numberMaxLength(8),
        ])
      ],
      total: [
        val && val.total || null, Validators.compose([
          fractionsNumbersLengthValidator,
          numberMaxLength(8),
        ])
      ],
      prizeMultiplier: [
        val && val.total || null, Validators.compose([
          fractionsNumbersLengthValidator,
          numberMaxLength(8),
        ])
      ]
    });
  }

  private toggleControlsDisabled( enable: boolean, controlName: string ) {
    Object.keys(this.betsControl.controls).forEach(( key: string ) => {
      const control = this.betsControl.controls[key].get(controlName);
      if (control) {
        if (enable && !this.isDisabled) {
          control.enable();
        } else {
          if (!this.isDisabled) {
            control.reset();
          }
          control.disable();
        }
      }
    });
  }
}
