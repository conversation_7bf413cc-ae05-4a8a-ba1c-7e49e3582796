import { CommonModule } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';

import { QualifyingBetsComponent } from './qualifying-bets.component';
import { QUALIFYING_BETS_MODULES } from './qualifying-bets.module';

describe('QualifyingBetsComponent', () => {
  let component: QualifyingBetsComponent;
  let fixture: ComponentFixture<QualifyingBetsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        ReactiveFormsModule,
        QUALIFYING_BETS_MODULES,
      ],
      declarations: [ QualifyingBetsComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(QualifyingBetsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
