import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiChipsAutocompleteModule } from '@skywind-group/lib-swui';
import { GameService } from '../../services/game.service';
import { TaggedItemsModule } from '../tagged-items/tagged-items.module';
import { GamesFormComponent } from './games-form.component';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    SwuiChipsAutocompleteModule,
    TaggedItemsModule,
  ],
  exports: [
    GamesFormComponent,
  ],
  declarations: [
    GamesFormComponent,
  ],
  providers: [
    GameService,
  ],
})
export class GamesFormModule {
}
