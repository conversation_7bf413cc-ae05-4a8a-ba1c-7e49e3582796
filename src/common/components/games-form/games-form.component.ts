import { Component, forwardRef, HostListener, Inject, Input, OnDestroy, OnInit, Optional } from '@angular/core';
import { ControlValueAccessor, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors, Validator } from '@angular/forms';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';

import { GameInfo } from '../../models/game';
import { TaggedItem } from '../tagged-items/tagged-items.component';
import { FormService, SW_FORM_SERVICE } from './form-service.model';

function from( game: GameInfo ): TaggedItem | undefined {
  if (!game.labels.find(( { id } ) => id === game.providerCode)) {
    game.labels.push({
      id: game.providerCode,
      title: game.providerTitle,
      group: 'provider',
    });
  }
  if ('code' in game && 'providerCode' in game) {
    return {
      id: game.code,
      title: game.title || '',
      labels: game.labels,
      checked: false
    };
  }
}

@Component({
  selector: 'sw-games-form',
  templateUrl: './games-form.component.html',
  styleUrls: ['./games-form.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => GamesFormComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => GamesFormComponent),
      multi: true
    }
  ]
})
export class GamesFormComponent implements OnDestroy, OnInit, ControlValueAccessor, Validator {

  @Input() set sourceGames( games: GameInfo[] ) {
    if (games && Array.isArray(games)) {
      this._sourceGames$.next(games && Array.isArray(games) ? games : []);
    }
  }

  gameCodes: string[] = [];
  disabled = false;
  isValid = true;
  submitted = false;
  isTouched = false;
  gameCodesCounter = 0;
  checkedOnly = false;

  toggleTexts = {
    true: 'COMPONENTS.GAMES_SELECT_MANAGER.VIEW_ALL',
    false: 'COMPONENTS.GAMES_SELECT_MANAGER.VIEW_SELECTED'
  };
  readonly items$: Observable<TaggedItem[]>;
  readonly totalItems$: Observable<number>;

  private _sourceGames$ = new BehaviorSubject<GameInfo[]>([]);
  private readonly destroyed = new Subject<void>();

  private onChange: ( _: any ) => void = (() => {
  });

  constructor( @Optional() @Inject(SW_FORM_SERVICE) private readonly formService: FormService ) {
    this.items$ = this._sourceGames$.pipe(
      map(games => {
        const selectItems: TaggedItem[] = [];
        games.forEach(game => {
          const item = from(game);
          if (item) {
            selectItems.push(item);
          }
        });
        return selectItems;
      })
    );
    this.totalItems$ = this.items$.pipe(
      map(items => items.length)
    );
  }

  ngOnInit() {
    if (this.formService) {
      this.formService.formSubmitted$.pipe(
        takeUntil(this.destroyed)
      ).subscribe(val => {
        this.submitted = val;
        this.isTouched = val;
      });
    }
  }

  ngOnDestroy() {
    this.destroyed.next();
    this.destroyed.complete();
  }

  @HostListener('blur') onblur() {
    this.isTouched = true;
    this.onTouched();
  }

  writeValue( val: string[] ): void {
    this.gameCodes = Array.isArray(val) ? val : [];
    this.gameCodesCounter = this.gameCodes.length;
  }

  registerOnChange( fn: any ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( isDisabled: boolean ): void {
    this.disabled = isDisabled;
  }

  validate(): ValidationErrors | null {
    return this.isValid ? null : { invalidForm: { valid: false } };
  }

  onSelectedChange( gameCodes: string[] ) {
    this.isValid = gameCodes.length > 0;
    this.gameCodesCounter = gameCodes.length;

    if (!this.gameCodesCounter) {
      this.checkedOnly = false;
    }

    setTimeout(() => {
      this.onChange(gameCodes);
    }, 0);
  }

  toggleVisible() {
    this.checkedOnly = !this.checkedOnly;
  }

  private onTouched: () => void = () => {
  };
}
