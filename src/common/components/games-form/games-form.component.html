<div class="mat-card">
  <div class="error-message margin-bottom16" *ngIf="!gameCodesCounter && submitted && !disabled">
    {{ 'COMPONENTS.GAMES_SELECT_MANAGER.validationMessage' | translate }}
  </div>
  <div class="margin-bottom12">
    {{ 'COMPONENTS.GAMES_SELECT_MANAGER.chooseTheGames' | translate }}
  </div>
  <div class="games-label">
    <h4 class="margin-bottom12 no-margin-top">Selected: {{gameCodesCounter}} of {{(totalItems$ | async)}}</h4>
    <span *ngIf="gameCodesCounter" class="checked-toggle" (click)="toggleVisible()">{{ toggleTexts[checkedOnly] | translate }}</span>
  </div>
  <sw-tagged-items
    [items]="items$ | async"
    [selectedItems]="gameCodes"
    [disabled]="disabled"
    [checkedOnly]="checkedOnly"
    (changed)="onSelectedChange($event)">
  </sw-tagged-items>
</div>
