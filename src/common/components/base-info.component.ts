import { SettingsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import 'moment-timezone';
import { takeUntil } from 'rxjs/operators';
import { BaseComponent } from './base.component';

function formatDate( date: string, format: string, timezone: string ): string {
  return moment.tz(moment.utc(date), timezone).format(format);
}

export abstract class BaseInfoComponent extends BaseComponent {
  created?: string;
  updated?: string;
  activatedAt?: string;

  protected constructor(protected readonly settingsService: SettingsService ) {
    super();
  }

  initDates( feature?: { created?: string; updated?: string, activatedAt?: string } ): void {
    this.settingsService.appSettings$.pipe(
      takeUntil(this.destroyed)
    ).subscribe(( { dateFormat, timeFormat, timezoneName } ) => {
      const format = `${dateFormat} ${timeFormat}`;
      this.created = feature && feature.created ? formatDate(feature.created, format, timezoneName) : '-';
      this.updated = feature && feature.updated ? formatDate(feature.updated, format, timezoneName) : '-';
      this.activatedAt = feature && feature.activatedAt ? formatDate(feature.activatedAt, format, timezoneName) : '-';
    });
  }
}
