$color-border: rgba(0, 0, 0, 0.12);

.sw-total-table {
  position: relative;
  display: inline-block;
  &.average-enabled {
    padding-top: 76px;
  }
  &__wrapper {
    border: 1px solid $color-border;
    border-radius: 4px;
    box-shadow: 0 0 6px rgba(42, 44, 68, .2);
  }
  table {
    border-collapse: collapse;
  }
  th,
  td {
    height: 65px;
    width: 200px;
    padding-left: 24px;
    border-bottom: 1px solid $color-border;
    &:first-child {
      width: 150px;
    }
  }
  &__title,
  &__subtitle {
    font-size: 14px;
    line-height: 18px;
    font-weight: 500;
    color: #2A2C44;
    text-align: left;
  }
  &__value {
    white-space: nowrap;
    &--multiplier {
      position: relative;
      padding-left: 12px;
      &:before {
        content: "×";
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 12px;
        padding-right: 4px;
        height: 100%;
        line-height: 1;
      }
    }
  }
  &__payout {
    display: flex;
    align-items: center;
  }
  &__value {
    max-width: 115px;
    margin-right: 4px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  &__players {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    height: 22px;
    min-width: 22px;
    padding: 2px 2px 2px 10px;
    border: 1px solid $color-border;
    border-radius: 7px;
    &:before {
      content: "×";
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 10px;
      height: 100%;
      padding-left: 2px;
      line-height: 1;
    }
  }
  &__expected {
    display: inline-flex;
    align-items: center;
    height: 20px;
    margin-top: 4px;
    padding: 0 5px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.87);
    background-color: #ECECEC;
    border-radius: 3px;
    white-space: nowrap;
  }
}

.sw-total-footer {
  width: 100%;
  display: flex;
  min-height: 72px;
  padding: 12px 0;
  color: #fff;
  background: rgba(0, 0, 0, 0.87);
  &__left {
    width: 150px;
    padding-left: 24px;
  }
  &__right {
    padding: 0 24px;
  }
  &__title {
    line-height: 24px;
    font-weight: 500;
  }
  &__subtitle {
    font-size: 14px;
    line-height: 20px;
    font-weight: 500;
    letter-spacing: 0.25px;
    color: rgba(255, 255, 255, 0.74);
  }
  &__item {
    line-height: 24px;
  }
}

.average {
  min-width: 125px;
  &__inner {
    position: absolute;
    bottom: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    height: 64px;
    width: 100%;
    background: #ECECEC;
  }
  &__title {
    font-size: 14px;
    line-height: 16px;
    font-weight: 500;
  }
  &__subtitle {
    font-size: 12px;
    line-height: 16px;
    font-weight: 400;
  }
  &__field {
    display: flex;
    width: 100%;
    font-size: 14px;
    mat-form-field {
      width: 97px;
    }
  }
  &__currency {
    padding-right: 2px;
  }
  th {
    position: relative;
    padding: 0 !important;
    height: 0 !important;
    &:not(:first-child) {
      .average {
        &__inner {
          padding-left: 16px;
          border-top-left-radius: 3px;
          border-bottom-left-radius: 3px;
        }
      }
    }
    &:first-child {
      min-width: 150px;
      .average {
        &__inner {
          padding-left: 24px;
          border-top-left-radius: 3px;
          border-bottom-left-radius: 3px;
        }
      }
    }
    &:last-child {
      .average {
        &__inner {
          padding-right: 24px;
          border-top-right-radius: 3px;
          border-bottom-right-radius: 3px;
        }
      }
    }
  }
}
