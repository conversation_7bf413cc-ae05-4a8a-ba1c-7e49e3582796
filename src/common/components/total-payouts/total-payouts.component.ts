import { AfterViewChecked, Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatTabGroup } from '@angular/material/tabs';
import { SettingsService, SwuiConstantsService } from '@skywind-group/lib-swui';
import { BehaviorSubject, combineLatest, forkJoin, Observable, of, Subject } from 'rxjs';
import { filter, map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { ApiCurrency, FeatureCurrency, FeaturePayout, FeaturePayoutPrizeType } from '../../../components/pages/interfaces/feature';
import { TournamentLuckyDraw, TournamentRange, TournamentRanges } from '../../../components/pages/interfaces/tournament';
import { JpnService } from '../../services/jpn.service';
import { currencyFormatter } from '../../directives/appSettingCurrencyFormatter/settingCurrencyFormatter.directive';


export class ColumnNames {
  columnTier: string;
  columnTierTotal: string;
  columnRange?: string;

  constructor(names?: ColumnNames) {
    this.columnTier = names?.columnTier || 'Rank';
    this.columnTierTotal = names?.columnTierTotal || 'Rank Total';
    this.columnRange = names?.columnRange || 'Total Range';
  }
}

interface CellData {
  type: FeaturePayoutPrizeType | 'rank' | 'lucky';
  title?: string;
  players?: number;
  payout?: string | number;
  rate?: number;
  expected?: number;
}

interface HeaderCellData {
  title: string;
  subtitle?: string;
}

@Component({
  selector: 'sw-total-payouts',
  templateUrl: './total-payouts.component.html',
  styleUrls: ['./total-payouts.component.scss']
})
export class TotalPayoutsComponent implements OnInit, AfterViewChecked, OnDestroy {
  @Input()
  set columnNames(val: ColumnNames) {
    this._columnNames = new ColumnNames(val);
  }

  get columnNames(): ColumnNames {
    return this._columnNames;
  }
  @Input()
  set activeCurrencyCode(val: string) {
    const currencies = this._currencies$.value;
    if (currencies && val) {
      const tabIndex = currencies.findIndex((currency: FeatureCurrency) => currency.code === val);
      this.selectedTabIndex = tabIndex || 0;
    }
  }

  @Input()
  set currencies( val: FeatureCurrency[] ) {
    this._currencies$.next(val || []);
  }

  @Input()
  set payouts( value: FeaturePayout[] ) {
    this._payouts$.next(value || []);
  }

  @Input()
  set ranges( value: TournamentRanges | null ) {
    this._ranges$.next(value || null);
  }

  @Input()
  set luckyDraw( value: TournamentLuckyDraw ) {
    this._luckyDraw$.next(value || null);
  }

  @ViewChild('tabSet', { static: true }) tabsRef: MatTabGroup | undefined;

  selectedTabIndex = 0;
  data$: BehaviorSubject<Map<string, CellData[][]>> = new BehaviorSubject<Map<string, CellData[][]>>(new Map<string, CellData[][]>());
  headerColumns: Map<string, HeaderCellData[]> = new Map<string, HeaderCellData[]>();
  averageForm: FormGroup;
  isMultiplier$ = new BehaviorSubject<boolean>(false);
  totalByColumn: Map<string, any[]> = new Map<string, any[]>();
  totalData: Map<string, any[]> = new Map<string, any[]>();

  private _payouts$ = new BehaviorSubject<FeaturePayout[]>([]);
  private _ranges$ = new BehaviorSubject<TournamentRanges | null>(null);
  private _luckyDraw$ = new BehaviorSubject<TournamentLuckyDraw | null>(null);
  private _currencies$ = new BehaviorSubject<FeatureCurrency[]>([]);
  private _ratedCurrencies$ = new BehaviorSubject<FeatureCurrency[]>([]);
  private readonly _destroyed$ = new Subject<void>();
  private _columnNames = new ColumnNames();

  constructor( private jpnService: JpnService,
               private fb: FormBuilder,
               private readonly settingsService: SettingsService
  ) {
    this.averageForm = this.initForm();
  }

  ngOnInit(): void {

    this.data$
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(( data: Map<string, CellData[][]> ) => {
        this.countTotalByColumn(data);
        this.totalData = this.getTotalData(data);
      });

    this._currencies$
      .pipe(
        switchMap(( currencies: FeatureCurrency[] ) => {
          return this.getRatedCurrencies(currencies as FeatureCurrency[], 'USD');
        }),
        takeUntil(this._destroyed$)
      )
      .subscribe(( currencies: FeatureCurrency[] ) => {
        this._ratedCurrencies$.next(currencies);
      });

    this.averageForm.valueChanges
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(( averageBets: { [key: string]: number[] } ) => {
        const data = this.data$.value;
        data.forEach(( value, key ) => {
          const rates = averageBets[key];
          if (rates) {
            value.map(( row: CellData[] ) => {
              return row.map(( el: CellData, index ) => {
                if (el.type === 'multiplier') {
                  const payout = el.payout as number || 0;
                  const players = el.players || 0;
                  el.expected = parseFloat((payout * players * rates[index - 1]).toFixed(2));
                }
                return el;
              });
            });
          }
        });
        this.data$.next(data);
      });

    combineLatest([this._ranges$, this._ratedCurrencies$, this.isMultiplier$])
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(( [ranges, ratedCurrencies] ) => {
        this.buildAverageForm(ranges, ratedCurrencies);
      });

    combineLatest([this._currencies$, this._payouts$, this._ranges$, this._luckyDraw$])
      .pipe(
        filter(( [currencies] ) => !!currencies.length),
        tap(( [currencies, payouts, ranges, luckyDraw] ) => {
          this.isMultiplier$.next(!!(payouts as FeaturePayout[])?.find(( el: FeaturePayout ) => el.prizeType === 'multiplier'));
          this.headerColumns = this.getHeaderColumns(ranges, currencies);
          this.buildTableData(currencies, payouts, luckyDraw);
        }),
        takeUntil(this._destroyed$)
      )
      .subscribe();
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  ngAfterViewChecked() {
    if (this.tabsRef) {
      this.tabsRef.realignInkBar();
    }
  }

  onSelectedIndexChange( index: number ) {
    this.selectedTabIndex = index;
  }

  formatCurrency( value?: number ): string {
    if (value) {
      return value?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
    return '';
  }

  private getTotalData(data: Map<string, CellData[][]>): Map<string, any[]> {
    const totalData = new Map<string, any[]>();
    const totalFixedMultiplier = this.countTotalFixedMultiplier();
    data.forEach((value: CellData[][], key) => {
      const prizes: string[] = [];
      value.forEach((row: CellData[]) => {
        row.forEach((cell: CellData) => {
          if (cell.type === 'text' && cell.payout) {
           prizes.push(`${cell.payout} ${cell.players ? '× ' + cell.players : ''}`);
          }
        });
      });
      const fixedValue = `${SwuiConstantsService.currencySymbol(key)} ${totalFixedMultiplier.get(key)}`;
      totalData.set(key, [fixedValue, ...prizes]);
    });

    return totalData;
  }

  private countTotalFixedMultiplier(): Map<string, number> {
    const totalFixedMultiplier = new Map<string, number>();
    this.totalByColumn.forEach(( value: any[], key ) => {
      const arr = [...value];
      arr.splice(0, 1);
      let result = arr.length > 0 ? arr.reduce(( a, c ) =>  a + c).toFixed(2) : 0;
      result = currencyFormatter(this.settingsService.appSettings.currencyFormat, result, key);
      totalFixedMultiplier.set(key, result);
    });

    return totalFixedMultiplier;
  }

  private countTotalByColumn( data: Map<string, CellData[][]> ) {
    data.forEach(( value: CellData[][], key ) => {
      const total: any[] = ['Total'];
      value.forEach(( row: CellData[], i ) => {
        if (i === 0) {
          for (let j = 0; j < row.length - 1; j++) {
            total.push(0);
          }
        }
        row.forEach(( el: CellData, index ) => {
          if (el.type === 'fixed' || el.type === 'multiplier') {
            let val = total[index];
            if (el.type === 'fixed') {
              val += el.payout as number;
            } else if (el.type === 'multiplier') {
              val += el.expected as number;
            }
            total[index] = parseFloat(val?.toFixed(2) || 0);
          }
        });
      });
      this.totalByColumn.set(key, total);
    });
  }

  private buildAverageForm( ranges: TournamentRanges | null, ratedCurrencies: FeatureCurrency[] ) {
    Object.keys(this.averageForm.controls).forEach(currency => {
      this.averageForm.removeControl(currency);
    });

    this._currencies$.value.forEach(( currency: FeatureCurrency ) => {
      const rangesArray = this.fb.array([]);
      if (ranges && ranges[currency.code]) {
        ranges[currency.code].forEach(( range: TournamentRange, index ) => {
          const max = range.max;
          const min = range.min;
          const rate = ranges[currency.code].length - 1 === index ? min : (max - min) / 2;
          rangesArray.push(this.fb.control(parseFloat(rate?.toFixed(2))));
        });
      } else {
        const ratedCurrency = ratedCurrencies.find(( cur: FeatureCurrency ) => cur.code === currency.code);
        const rate = ratedCurrency ? 0.9 * ratedCurrency.rate : 1;
        rangesArray.push(this.fb.control(parseFloat(rate?.toFixed(2))));
      }
      this.averageForm.addControl(currency.code, rangesArray);
    });
  }

  private buildTableData( currencies: FeatureCurrency[], payouts: FeaturePayout[], luckyDraw: TournamentLuckyDraw | null ) {
    const data = new Map<string, CellData[][]>();
    (currencies as FeatureCurrency[]).forEach(( currency: FeatureCurrency ) => {
      let prevPayoutPlayers = 0;
      const tableData: CellData[][] = (payouts as FeaturePayout[]).map(( payout: FeaturePayout, index: number ) => {
        const row = this.getTableRow(payout, currency, false, prevPayoutPlayers, index);
        prevPayoutPlayers += payout.players;
        return row;
      });
      const luckyDrawData = luckyDraw as TournamentLuckyDraw;
      if (luckyDrawData && !!luckyDrawData.enabled) {
        tableData.push(this.getTableRow(luckyDraw as FeaturePayout, currency, true));
      }
      data.set(currency.code, tableData);
    });
    this.data$.next(data);
  }

  private getTableRow( payout: FeaturePayout,
                       currency: FeatureCurrency,
                       isLuckyDraw: boolean,
                       prevPayoutPlayers?: number,
                       index?: number
  ): CellData[] {
    const firstCell: CellData = {
      type: 'rank',
      title: isLuckyDraw ? 'LuckyDraw' : getRankTitle(payout, prevPayoutPlayers, index),
    };
    const tableRow = [firstCell];
    let averageRates: number[] = [];
    if (payout.prizeType) {
      averageRates = this.averageForm?.get(currency.code)?.value || [];
    }

    payout.payoutsPerRange.forEach(( el: number | string, i: number ) => {
      const roundedPayout = Math.round( (el as number * currency.rate * payout.players) * 100 + Number.EPSILON ) / 100;
      const processedPayout = payout.prizeType === 'fixed' ? parseFloat(roundedPayout.toFixed(2)) : el;
      let expected;
      if (payout.prizeType === 'multiplier') {
        expected = el as number * payout.players * averageRates[i];
      }
      tableRow.push({
        type: payout.prizeType,
        payout: processedPayout,
        expected: expected ? parseFloat(expected.toFixed(2)) : 0,
        players: payout.players,
        rate: currency.rate
      });
    });
    return tableRow;
  }

  private getHeaderColumns( ranges: TournamentRanges | null, currencies: FeatureCurrency[] ): Map<string, HeaderCellData[]> {
    const columns = new Map<string, HeaderCellData[]>();
    const { columnTier, columnTierTotal, columnRange } = this.columnNames;
    if (ranges) {
      Object.keys(ranges).forEach(( currency: string ) => {
        let rangesTitles = [{ title: columnTier }];
        if (ranges[currency]) {
          const currencyFormat = this.settingsService.appSettings.currencyFormat;
          const titles = ranges[currency].reduce(( acc: HeaderCellData[], range: TournamentRange, index ) => {
            const from = range?.min ? SwuiConstantsService.currencySymbol(currency) +
              currencyFormatter(currencyFormat, range.min, currency) : 'MIN';
            const to = range?.max ? SwuiConstantsService.currencySymbol(currency) +
              currencyFormatter(currencyFormat, range.max, currency) : 'MAX';
            const headerCell = {
              title: `${columnRange} ${index + 1}`,
              subtitle: `${from} - ${to}`
            };
            acc.push(headerCell);
            return acc;
          }, []);
          rangesTitles.push(...titles);
        }
        columns.set(currency, rangesTitles);
      });
    } else {
      currencies.forEach(( currency: FeatureCurrency ) => {
        columns.set(currency.code, [
          { title: columnTier },
          { title: columnTierTotal }
        ]);
      });
    }

    return columns;
  }

  private getRatedCurrencies( currencies: FeatureCurrency[], baseCurrency: string ): Observable<FeatureCurrency[]> {
    if (currencies.length) {
      const rates = currencies.map(( currency: FeatureCurrency ) => {
        return this.jpnService.getExchangeRate(baseCurrency, currency.code);
      });
      return forkJoin(rates).pipe(
        map(( val: ApiCurrency[] ) => {
          return val.map(( el: ApiCurrency ) => ({
            code: el.currency,
            rate: parseFloat(el.rate.toFixed(2))
          }));
        })
      );
    } else {
      return of([]);
    }
  }

  private initForm(): FormGroup {
    return this.fb.group({});
  }
}

function getRankTitle( payout: FeaturePayout, prevPayoutPlayers?: number, index?: number ): string {
  let title = '';
  const prevPlayers = prevPayoutPlayers || 0;
  const i = index || 0;
  if (payout.payoutsPerRange.length > 1) {
    let from = prevPlayers + 1;
    let to = prevPlayers + payout.players;
    let rankTitle = from + '-' + to;

    if (from === to || to < from) {
      rankTitle = from.toString();
    }
    title = rankTitle;
  } else {
    title = (i + 1).toString();
  }
  return title;
}

