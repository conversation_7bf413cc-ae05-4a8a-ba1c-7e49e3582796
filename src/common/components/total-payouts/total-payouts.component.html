<mat-tab-group
  #tabSet
  [formGroup]="averageForm"
  class="nested sw-total-tabs"
  animationDuration="0ms"
  [selectedIndex]="selectedTabIndex"
  [disableRipple]="true"
  (selectedIndexChange)="onSelectedIndexChange($event)">
  <mat-tab *ngFor="let currency of (data$ | async).keys()">
    <ng-template matTabLabel>
      {{currency | uppercase}}
    </ng-template>
    <div class="sw-total-table" [ngClass]="{'average-enabled': isMultiplier$ | async}">
      <div class="sw-total-table__wrapper">
        <table>
          <thead>
            <tr *ngIf="isMultiplier$ | async" class="average" [formArrayName]="currency">
              <th>
                <div class="average__inner">
                  <div class="average__title">Average bet</div>
                  <div class="average__subtitle">Preview for multipliers</div>
                </div>
              </th>
              <th *ngFor="let control of averageForm.get(currency)?.controls">
                <div class="average__inner">
                  <div class="average__field">
                    <mat-form-field>
                      <div matPrefix class="average__currency">{{currency | currencySymbol}}</div>
                      <input type="number" matInput min="0" [formControl]="control"
                             swCurrencyFormatter [currencyCode]="currency">
                    </mat-form-field>
                  </div>
                </div>
              </th>
            </tr>
            <tr>
              <th *ngFor="let cell of headerColumns.get(currency)">
                <div class="sw-total-table__title">{{cell.title}}</div>
                <div class="sw-total-table__subtitle">{{cell.subtitle}}</div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let row of (data$ | async).get(currency)">
              <td *ngFor="let cell of row">
                <ng-container [ngSwitch]="cell.type">
                  <div *ngSwitchCase="'fixed'" class="sw-total-table__inner">
                    <div class="sw-total-table__payout" *ngIf="cell.payout">
                      <div class="sw-total-table__value">
                        {{currency | currencySymbol}} {{cell.payout | currencyFormat: currency}}
                      </div>
                    </div>
                  </div>
                  <div *ngSwitchCase="'text'" class="sw-total-table__inner">
                    <div class="sw-total-table__payout" *ngIf="cell.payout">
                      <div class="sw-total-table__value">
                        {{cell.payout}}
                      </div>
                      <div class="sw-total-table__players" *ngIf="cell.players > 1">
                        {{cell.players}}
                      </div>
                    </div>
                  </div>
                  <div *ngSwitchCase="'multiplier'" class="sw-total-table__inner">
                    <ng-container *ngIf="cell.payout">
                      <div class="sw-total-table__payout">
                        <div class="sw-total-table__value sw-total-table__value--multiplier">
                          {{cell.payout}}
                        </div>
                        <div class="sw-total-table__players" *ngIf="cell.players > 1">
                          {{cell.players}}
                        </div>
                      </div>
                      <div class="sw-total-table__expected" *ngIf="cell.payout">
                        {{currency | currencySymbol}} {{cell.expected | currencyFormat: currency}} expected
                      </div>
                    </ng-container>
                  </div>
                  <div *ngSwitchDefault class="sw-total-table__inner">
                    {{cell?.title}}
                  </div>
                </ng-container>
              </td>
            </tr>
            <tr *ngIf="totalByColumn.get(currency).length > 2">
              <td *ngFor="let cell of totalByColumn.get(currency); let first = first">
                <ng-container *ngIf="first; else cellValueTpl">
                  {{cell}}
                </ng-container>
                <ng-template  #cellValueTpl>
                  <ng-container *ngIf="cell">
                    {{currency | currencySymbol}} {{cell | currencyFormat: currency}}
                  </ng-container>
                </ng-template>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="sw-total-table__footer sw-total-footer">
          <div class="sw-total-footer__left">
            <div class="sw-total-footer__title">Total Payout</div>
            <div class="sw-total-footer__subtitle" *ngIf="isMultiplier$ | async">(Expected)</div>
          </div>
          <div class="sw-total-footer__right">
            <div class="sw-total-footer__item" *ngFor="let item of totalData.get(currency)">
              {{item}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </mat-tab>
</mat-tab-group>
