import { CommonModule } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { JpnService, MockJpnService } from '../../services/jpn.service';

import { TotalPayoutsComponent } from './total-payouts.component';
import { TOTAL_PAYOUTS_MODULES } from './total-payouts.module';
import { SettingsService } from '@skywind-group/lib-swui';

describe('TotalPayoutsComponent', () => {
  let component: TotalPayoutsComponent;
  let fixture: ComponentFixture<TotalPayoutsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        HttpClientTestingModule,
        NoopAnimationsModule,
        ...TOTAL_PAYOUTS_MODULES,
      ],
      declarations: [TotalPayoutsComponent],
      providers: [
        FormBuilder,
        SettingsService,
        { provide: JpnService, useClass: MockJpnService },
      ],
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TotalPayoutsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
