import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTabsModule } from '@angular/material/tabs';
import { SwuiCurrencySymbolModule } from '@skywind-group/lib-swui';
import { FloatFormatterModule } from '../../directives/floatFormatter/floatFormatter.module';
import { PipesModule } from '../../pipes/pipes.module';
import { TotalPayoutsComponent } from './total-payouts.component';
import { SettingCurrencyFormatterModule } from '../../directives/appSettingCurrencyFormatter/settingCurrencyFormatter.module';

export const TOTAL_PAYOUTS_MODULES = [
  MatTabsModule,
  MatFormFieldModule,
  MatInputModule,
  SwuiCurrencySymbolModule,
  PipesModule,
  FloatFormatterModule,
];

@NgModule({
  declarations: [TotalPayoutsComponent],
  exports: [TotalPayoutsComponent],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ...TOTAL_PAYOUTS_MODULES,
        SettingCurrencyFormatterModule,
    ],
})
export class TotalPayoutsModule { }
