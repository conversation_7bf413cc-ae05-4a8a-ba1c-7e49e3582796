import { Directive, On<PERSON><PERSON>roy } from '@angular/core';
import { Subject } from 'rxjs';
import { SwBrowserTitleService } from '@skywind-group/lib-swui';

@Directive()
export abstract class BaseComponent implements OnDestroy {
  protected readonly destroyed = new Subject<void>();

  protected constructor(protected readonly browserTitleService?: SwBrowserTitleService) {
    if (browserTitleService) {
      browserTitleService.setupTitles('Engagement');
    }
  }

  ngOnDestroy() {
    this.destroyed.next();
    this.destroyed.complete();
  }
}
