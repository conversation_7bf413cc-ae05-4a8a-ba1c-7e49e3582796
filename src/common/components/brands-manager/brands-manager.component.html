<sw-brand-funding-errors *ngIf="fundingFormEnabled" [errors]="selection.getFundingErrors()"></sw-brand-funding-errors>

<div class="margin-bottom12" *ngIf="description">
  {{ description }}
</div>
<div class="checked-toggle-wrapper">
  <sw-total-brands [entities]="entities" [selected]="selection.length"></sw-total-brands>
  <div class="checked-toggle" *ngIf="selection.length" (click)="selectedOnlyToggle()">{{ toggleTexts[selectedOnly] | translate }}</div>
</div>
<div class="table-sticky bordered">

  <div class="table-sticky__header">
    <mat-form-field [formGroup]="searchForm" appearance="outline" class="table-sticky__search no-field-padding">
      <mat-icon matPrefix class="search-icon">search</mat-icon>
      <input matInput type="text" [formControl]="searchControl" placeholder="Search">
      <button mat-button *ngIf="searchControl.value" matSuffix mat-icon-button aria-label="Clear"
              (click)="clearSearch()">
        <mat-icon style="top: 0">close</mat-icon>
      </button>
    </mat-form-field>
    <div class="table-sticky__info">
      <div class="table-sticky__id">Brand ID</div>
      <div class="table-sticky__funding red" *ngIf="fundingFormEnabled">Skywind Funding</div>
    </div>
  </div>
  <div class="table-sticky__body">
    <mat-tree [dataSource]="dataSource" [treeControl]="treeControl">

      <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding>
        <div class="node">
          <div class="node__row">
            <span class="node__title">{{node.title}}</span>
          </div>
          <div class="node__info">
            <div class="node__id"></div>
            <div class="node__funding" *ngIf="fundingFormEnabled"></div>
          </div>
        </div>
      </mat-tree-node>

      <mat-tree-node *matTreeNodeDef="let node; when: isBrand" matTreeNodePadding
                     [ngClass]="{'selected': selection.isSelected(node)}">
        <div class="node">
          <mat-checkbox (change)="onCheckboxChange(node)"
                        (click)="onCheckBoxClick($event)"
                        [checked]="selection.isSelected(node)"
                        [disabled]="disabled || hasSegmentationPlayers(node.id)"
                        class="node__checkbox">
            <span class="node__title">{{node.title}}</span>
          </mat-checkbox>
          <div class="node__info">
            <div class="node__id">
              {{node.id}}
              <div class="node__decrypted" *ngIf="isSuperAdmin && node?.decryptedBrand">{{node?.decryptedBrand}}</div>
            </div>
            <div class="node__funding" *ngIf="fundingFormEnabled">
              <sw-brand-funding (valueChange)="selection.setFunding(node, $event)"
                                (statusChanges)="onFundingStatusChanges(node, $event)"
                                *ngIf="selection.isSelected(node)"
                                [disabled]="disabled"
                                [funding]="selection.getFunding(node)"></sw-brand-funding>
            </div>
          </div>
        </div>
      </mat-tree-node>

      <mat-tree-node *matTreeNodeDef="let node; when: hasChild" matTreeNodePadding>
        <div class="node">
          <button mat-icon-button matTreeNodeToggle class="node__chevron">
            <mat-icon class="mat-icon-rtl-mirror">
              {{treeControl.isExpanded(node) ? 'expand_more' : 'chevron_right'}}
            </mat-icon>
          </button>
          <div class="node__row" *ngIf="multiple">
            <span class="node__title">{{node.title}}</span>
          </div>
          <span class="node__title node__row"
                *ngIf="!multiple">{{node.title}}</span>
          <div class="node__info">
            <div class="node__id"></div>
            <div class="node__funding" *ngIf="fundingFormEnabled"></div>
          </div>
        </div>
      </mat-tree-node>

    </mat-tree>
    <div class="table-sticky__loading" *ngIf="isLoading$ | async">
      <mat-spinner [diameter]="40" class="table-sticky__spinner"></mat-spinner>
    </div>
  </div>
</div>

<div class="margin-top20" fxLayout.lt-sm="column" fxLayout="row" *ngIf="resellersControlEnabled">
  <sw-owner-entity (valueChange)="onSelectReseller($event)"
                   [owner]="owner"
                   [entity]="masterEntity"
                   [disabled]="disabled"
                   [ownerTooltip]="ownerTooltip"></sw-owner-entity>
</div>
