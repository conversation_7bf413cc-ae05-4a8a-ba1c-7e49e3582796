.selected {
  background: #FFF7E8;
}

.search-icon {
  color: rgba(0, 0, 0, 0.42);
  margin-right: 4px;
}

.red {
  color: #D51832;
}

.node {
  position: relative;
  display: flex;
  align-items: flex-start;
  width: 100%;
  height: 48px;
  padding: 0 12px 0 44px;

  &__chevron {
    position: absolute;
    top: 0;
    left: 0;
  }

  &__checkbox {
    position: relative;
    left: -29px;
    margin-top: 10px;

    .node__title {
      margin-left: 6px;
    }
  }

  &__row {
    margin-top: 10px;
  }

  &__info {
    display: flex;
    align-items: center;
    height: 100%;
    margin-left: auto;
  }

  &__funding {
    display: flex;
    width: 110px;
    flex-shrink: 0;
  }

  &__id {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: 180px;
    text-align: left;
    padding: 0 24px;
  }

  &__input {
    width: 90px;
  }

  &__suffix {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    width: 30px;
    margin-top: 4px;
  }
  &__decrypted {
    margin-left: 18px;
  }
}

mat-tree-node {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.table-sticky {
  &__header {
    padding-right: 27px;
  }

  &__info {
    display: flex;
    align-items: center;
    margin-left: auto;
  }

  &__id {
    flex-shrink: 0;
    width: 180px;
    padding: 0 24px;
    line-height: 1.2em;
    text-align: center;
  }

  &__funding {
    width: 110px;
    flex-shrink: 0;
    line-height: 1.2em;
  }

  &__body {
    position: relative;
  }

  &__info {
    display: flex;
    align-items: center;
    margin-left: auto;
  }

  &__loading {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .06);
  }

  &__search {
    width: 330px;
    margin-right: 24px;
    margin-left: unset;
  }
}

.checked-toggle-wrapper {
  display: flex;
}

.checked-toggle {
  color: #1468cf;
  transition: opacity .5s;
  cursor: pointer;
  margin-left: 10px;

  &:hover {
    opacity: 0.5;
  }
}

