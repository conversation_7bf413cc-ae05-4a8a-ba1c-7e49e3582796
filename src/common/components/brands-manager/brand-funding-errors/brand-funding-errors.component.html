<div class="error-message margin-bottom20" *ngIf="errors.length">
  <strong class="error-message__heading">Following fields has validation errors:</strong>
  <ul class="error-message__list margin-top8" *ngFor="let ctrl of errors">
    <li>
      <span style="font-weight: 500">Brand ID: {{ctrl.id}}</span>
      <ul class="error-message__list" style="margin-top: 8px">
        <li *ngIf="ctrl.errors.required">
          {{ 'VALIDATION.fieldRequired' | translate }}
        </li>
        <li *ngIf="ctrl.errors.min">
          {{ 'VALIDATION.min' | translate: {min: 0} }}
        </li>
        <li *ngIf="ctrl.errors.max">
          {{ 'VALIDATION.max' | translate: {max: 100} }}
        </li>
        <li *ngIf="ctrl.errors.fractionsNumbersLength">
          {{ 'VALIDATION.maxDecimalAllowed' | translate }}
        </li>
      </ul>
    </li>
  </ul>
</div>
