import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnDestroy, OnInit, Output, } from '@angular/core';
import { FormControl, ValidationErrors, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { fractionsNumbersLengthValidator } from '../../../lib/validators';

@Component({
  selector: 'sw-brand-funding',
  templateUrl: './brand-funding.component.html',
  styleUrls: ['./brand-funding.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BrandFundingComponent implements OnInit, OnDestroy {

  @Input('funding') set setFunding( value: number | undefined ) {
    this.ctrl.setValue(value || 0, { emitEvent: false });
  }

  @Input('disabled') set setDisabled( value: boolean | undefined ) {
    const disabled = coerceBooleanProperty(value);
    if (disabled) {
      this.ctrl.disable({ emitEvent: false });
    } else {
      this.ctrl.enable({ emitEvent: false });
    }
  }

  @Output() valueChange = new EventEmitter<number>();
  @Output() statusChanges = new EventEmitter<{ status: string; errors: ValidationErrors }>();

  readonly ctrl = new FormControl('', [
    Validators.min(0),
    Validators.max(100),
    fractionsNumbersLengthValidator,
  ]);

  private readonly destroyed$ = new Subject();

  ngOnInit() {
    this.ctrl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(value => {
      this.valueChange.emit(parseFloat(value));
    });
    this.ctrl.statusChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(value => {
      this.statusChanges.emit({ status: value, errors: this.ctrl.errors || {} });
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

}
