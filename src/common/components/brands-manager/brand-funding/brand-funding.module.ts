import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { BrandFundingComponent } from './brand-funding.component';


@NgModule({
  declarations: [BrandFundingComponent],
  exports: [BrandFundingComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
  ]
})
export class BrandFundingModule {
}
