import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatTreeModule } from '@angular/material/tree';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiSelectModule } from '@skywind-group/lib-swui';
import { BrandFundingErrorsModule } from './brand-funding-errors/brand-funding-errors.module';
import { BrandFundingModule } from './brand-funding/brand-funding.module';
import { BrandsManagerComponent } from './brands-manager.component';
import { OwnerEntityModule } from './owner-entity/owner-entity.module';
import { TotalBrandsModule } from './total-brands/total-brands.module';


export const BRANDS_MANAGER_MODULE = [
  MatCheckboxModule,
  MatTreeModule,
  MatButtonModule,
  MatIconModule,
  MatTableModule,
  MatChipsModule,
  MatFormFieldModule,
  MatInputModule,
  ReactiveFormsModule,
  MatProgressSpinnerModule,
  MatSelectModule,
  MatTooltipModule,
  SwuiSelectModule,
  FlexLayoutModule,
  OwnerEntityModule,
  TotalBrandsModule,
  BrandFundingModule,
  BrandFundingErrorsModule
];


@NgModule({
  declarations: [BrandsManagerComponent],
  exports: [BrandsManagerComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...BRANDS_MANAGER_MODULE
  ]
})
export class BrandsManagerModule {
}
