import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { SwHubAuthService } from '@skywind-group/lib-swui';

import { EntityService, MockEntityService } from '../../services/entity.service';
import { MockAuthService } from '../../services/mock-auth.service';
import { BrandsManagerComponent } from './brands-manager.component';
import { BRANDS_MANAGER_MODULE } from './brands-manager.module';
import { MatIconRegistry } from '@angular/material/icon';
import { FakeMatIconRegistry } from '@angular/material/icon/testing';


describe('BrandsManagerComponent', () => {
  let component: BrandsManagerComponent;
  let fixture: ComponentFixture<BrandsManagerComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        NoopAnimationsModule,
        TranslateModule.forRoot(),
        ...BRANDS_MANAGER_MODULE,
      ],
      declarations: [
        BrandsManagerComponent,
      ],
      providers: [
        { provide: SwHubAuthService, useClass: MockAuthService },
        { provide: EntityService, useClass: MockEntityService },
        { provide: MatIconRegistry, useClass: FakeMatIconRegistry }
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(BrandsManagerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
