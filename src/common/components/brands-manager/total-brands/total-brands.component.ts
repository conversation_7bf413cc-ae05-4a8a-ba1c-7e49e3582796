import { ChangeDetectionStrategy, Component, Input, } from '@angular/core';
import { isBrandType, ShortEntity } from '../../../models/entity';

function totalBrands( entities: ShortEntity[] ): number {
  let total = 0;
  entities.forEach(entity => {
    total += isBrandType(entity) ? 1 : 0;
    total += entity.child ? totalBrands(entity.child) : 0;
  });
  return total;
}

@Component({
  selector: 'sw-total-brands',
  templateUrl: './total-brands.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TotalBrandsComponent {
  @Input() selected: number | undefined;

  @Input('entities') set setEntities( entities: ShortEntity[] | undefined ) {
    if (typeof entities === 'undefined') {
      return;
    }
    this.total = totalBrands(entities);
  }

  total = 0;

  get hasSelected(): any {
    return typeof this.selected !== 'undefined';
  }
}
