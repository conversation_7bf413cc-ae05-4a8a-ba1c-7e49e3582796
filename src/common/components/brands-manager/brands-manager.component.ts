import { coerceBooleanProperty } from '@angular/cdk/coercion';
import {
  AfterContentInit, ChangeDetectorRef, Component, EventEmitter, Input, isDevMode, OnDestroy, OnInit, Output,
} from '@angular/core';
import { FormControl, FormGroup, ValidationErrors } from '@angular/forms';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { BehaviorSubject, merge, ReplaySubject, Subject } from 'rxjs';
import { map, takeUntil, tap } from 'rxjs/operators';
import { FeatureOperators, OperatorsBrand, OperatorsOwner } from '../../../components/pages/interfaces/feature';
import { entitiesFilterBy, entityFindBy, isBrand, isResellerType, ShortEntity } from '../../models/entity';

import { EntityService } from '../../services/entity.service';
import { EntityDataSource, EntityNode, EntitySelectionModel } from './entity-data-source.model';


type ResellerShortEntity = Omit<ShortEntity, 'child'> & { totalBrands?: number; child?: ResellerShortEntity[] };

function setTotalBrands( entity: ShortEntity | ResellerShortEntity ): ResellerShortEntity {
  const child: ResellerShortEntity[] = [];
  let totalBrands = 0;
  (entity.child || []).forEach(item => {
    if (isBrand(item.type)) {
      totalBrands++;
      child.push({
        ...item,
        totalBrands: 0
      });
    } else {
      item = setTotalBrands(item);
      totalBrands += (item as ResellerShortEntity).totalBrands || 0;
      child.push(item);
    }
  });
  return {
    ...entity,
    totalBrands,
    child
  };
}

export function removeByTotalBrands( entity: ResellerShortEntity ): ShortEntity | null {
  const child: ShortEntity[] = [];
  (entity.child || [])
    .map(item => removeByTotalBrands(item))
    .forEach(item => {
      if (item !== null) {
        child.push(item);
      }
    });
  if (isResellerType(entity) && entity.totalBrands === 0) {
    return null;
  }
  return { ...entity, child };
}

function searchFn( searchText: string,
                   checkedOnly: boolean,
                   selectedFn: ( sel: any ) => boolean,
                   isSuperAdmin: boolean
): ( { name }: ShortEntity ) => boolean {
  searchText = searchText.replace(/\W|_/g, '[$&]');
  const regexp = new RegExp(searchText, 'gi');
  return ( { name, id, decryptedBrand }: ShortEntity ) => {
    if (checkedOnly && !selectedFn({ id })) {
      return false;
    }

    if (searchText.length < 3 || id.search(regexp) !== -1 ||
        (isSuperAdmin && !!decryptedBrand && decryptedBrand.toString().search(regexp) !== -1) ) {
      return true;
    }

    if (!name) {
      return false;
    }
    return name.search(regexp) !== -1;
  };
}

@Component({
  selector: 'sw-brands-manager',
  templateUrl: './brands-manager.component.html',
  styleUrls: ['./brands-manager.component.scss'],
})
export class BrandsManagerComponent implements OnInit, AfterContentInit, OnDestroy {
  @Input('disabled') set setDisabled( value: boolean | undefined ) {
    this.disabled = coerceBooleanProperty(value);

    if (this.disabled) {
      this.searchControl.disable({ emitEvent: false });
    } else {
      this.searchControl.enable({ emitEvent: false });
    }
  }

  @Input()
  set featureOperators( value: FeatureOperators | undefined ) {
    if (typeof value === 'undefined') {
      return;
    }
    this._featureOperators = value;
    this.selection.setBrands(value ? value.brands || [] : []);
    this.owner = value ? value.owner || null : null;
    if (this.selectedOnly) {
      this.selectedOnly$.next(this.searchControl.value);
    }
  }

  @Input() ownerTooltip: string | undefined;
  @Input() description: string | undefined;

  @Input()
  get multiple(): boolean {
    return this._multiple;
  }

  set multiple( value: boolean ) {
    const multiple = coerceBooleanProperty(value);
    if (multiple !== this._multiple) {
      if (isDevMode() && this._contentInitialized) {
        throw new Error('Cannot change `multiple` mode of sw-brands-manager after initialization.');
      }
      this._multiple = value;
      this.selection.setMultiple(this._multiple);
    }
  }

  @Input() showResellers = true;
  @Input() isFundingFormEnabled = false;

  @Output() selectedData = new EventEmitter<FeatureOperators>();
  @Output() isValid = new EventEmitter<boolean>(true);

  readonly dataSource = new EntityDataSource();
  readonly treeControl = this.dataSource.treeControl;

  readonly searchControl = new FormControl('');
  readonly searchForm = new FormGroup({ search: this.searchControl });
  readonly toggleTexts = {
    true: 'COMPONENTS.GAMES_SELECT_MANAGER.VIEW_ALL',
    false: 'COMPONENTS.GAMES_SELECT_MANAGER.VIEW_SELECTED'
  };

  selection: EntitySelectionModel;
  disabled = false;
  isLoading$ = new BehaviorSubject(false);
  entities: ShortEntity[] = [];
  masterEntity: ShortEntity | undefined;
  owner: OperatorsOwner | null = null;

  private readonly isSuperAdmin: boolean;
  private _featureOperators?: FeatureOperators;
  private _multiple = true;
  private _contentInitialized = false;
  private selectedOnly = false;
  private selectedOnly$ = new ReplaySubject(1);
  private readonly selectedReseller$ = new BehaviorSubject<ShortEntity | undefined>(undefined);
  private readonly destroyed$ = new Subject();

  constructor( private readonly cd: ChangeDetectorRef,
               private readonly entityService: EntityService,
               { isSuperAdmin }: SwHubAuthService
  ) {
    this.isSuperAdmin = isSuperAdmin;
    this.selection = new EntitySelectionModel(this._multiple);
  }

  hasSegmentationPlayers(id: string): boolean {
    let result = false;
    if( this._featureOperators ) {
      const brand = this._featureOperators?.brands?.find( (item: OperatorsBrand) => item.id === id);
      if (brand) {
       result = !!brand.disabled;
      }
    }

    return result;
  }

  ngOnInit() {
    this.entityService.structure$.pipe(
      tap(() => this.isLoading$.next(true)),
      map(entity => {
        if (entity) {
          const reseller = removeByTotalBrands(setTotalBrands(entity));
          if (reseller) {
            return reseller;
          }
          delete entity.child;
        }
        return entity;
      }),
      takeUntil(this.destroyed$)
    ).subscribe(entity => {
      if (entity) {
        this.masterEntity = entity;
        this.entities = entity.child || [];
        const owner = this.owner;
        if (!owner || entity.id === owner.id) {
          this.selectedReseller$.next(entity);
        } else {
          this.selectedReseller$.next(entityFindBy(entity, ( { id } ) => id === owner.id) || entity);
        }
      }
      this.isLoading$.next(false);
      this.cd.detectChanges();
    });

    this.selectedReseller$.pipe(
      tap(() => this.isLoading$.next(true)),
      takeUntil(this.destroyed$)
    ).subscribe(reseller => {
      if (reseller) {
        this.entities = isBrand(reseller.type) ? [reseller] : reseller.child || [];
        this.searchControl.reset('', { emitEvent: false });
        this.entities.sort(( a: ShortEntity, b: ShortEntity ) => {
          if (a && a.name && b && b.name) {
            return a.name > b.name ? 1 : -1;
          }
          return 0;
        });
        this.dataSource.data = this.entities;
        const deselected = this.selection.deselect(this.treeControl.getNodes(this.selection.selected).map(( { id } ) => id));
        if (!deselected) {
          this.selectedData.emit({
            owner: this.getOwner(),
            brands: this.selection.selectedBrands
          });
        }
        this.treeControl.expandAllParents(this.selection.selected);
        this.isLoading$.next(false);
        this.cd.detectChanges();
      }
    });

    const isSelectedFn = this.selection.isSelected.bind(this.selection);

    merge(this.selectedOnly$, this.searchControl.valueChanges).pipe(
      tap(() => this.isLoading$.next(true)),
      map(searchText => {
        const entities = searchText.length >= 3 || this.selectedOnly
          ? entitiesFilterBy(this.entities, searchFn(searchText.toLowerCase(), this.selectedOnly, isSelectedFn, this.isSuperAdmin)) || null
          : null;
        return { entities, filterValue: searchText };
      }),
      takeUntil(this.destroyed$)
    ).subscribe(( { entities, filterValue }: any ) => {
      if (entities === null) {
        if (this.dataSource.data !== this.entities) {
          this.dataSource.data = this.entities;
        }
        this.treeControl.expandAllParents(this.selection.selected);
      } else {
        this.dataSource.data = entities;
        if (filterValue) {
          this.treeControl.expandAll();
        } else {
          this.treeControl.expandAllParents(this.selection.selected);
        }
      }
      this.isLoading$.next(false);
      this.cd.detectChanges();
    });

    this.selection.changed.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(brands => {
      if (this.disabled) {
        return;
      }

      this.selectedData.emit({
        owner: this.getOwner(),
        brands
      });
    });
  }

  ngAfterContentInit(): void {
    this._contentInitialized = true;
  }

  ngOnDestroy() {
    this.selection.destroy();
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get resellersControlEnabled(): boolean {
    return this.isSuperAdmin && this.showResellers;
  }

  get fundingFormEnabled(): boolean {
    return this.isSuperAdmin && this.isFundingFormEnabled;
  }

  isBrand = ( _: number, { type }: EntityNode ) => isBrand(type);

  hasChild = ( _: number, { expandable }: EntityNode ) => expandable;

  onFundingStatusChanges( node: EntityNode, { status, errors }: { status: string; errors: ValidationErrors } ) {
    this.selection.setFundingErrors(node, status === 'INVALID' ? errors : undefined);
    this.isValid.emit(!this.selection.hasFundingErrors());
    this.cd.detectChanges();
  }

  onCheckBoxClick( event: Event ) {
    event.stopPropagation();
  }

  onCheckboxChange( node: EntityNode ) {
    this.selection.toggle(node);

    if (!this.selectedOnly) {
      return;
    }

    if (!this.selection.length) {
      this.selectedOnly = false;
    }
    this.selectedOnly$.next(this.searchControl.value);
  }

  onSelectReseller( value: ShortEntity | undefined ) {
    this.selectedReseller$.next(value);
  }

  selectedOnlyToggle(): void {
    this.selectedOnly = !this.selectedOnly;
    this.selectedOnly$.next(this.searchControl.value);
  }

  clearSearch() {
    this.searchControl.setValue('');
  }

  private getOwner(): OperatorsOwner | null {
    if (this.resellersControlEnabled) {
      const entity = this.selectedReseller$.value;
      if (entity) {
        return {
          id: entity.id,
          title: entity.name || entity.title || ''
        };
      }
      return null;
    }
    return this.owner;
  }
}
