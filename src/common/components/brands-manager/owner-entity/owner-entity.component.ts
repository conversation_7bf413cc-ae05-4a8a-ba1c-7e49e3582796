import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { Component, EventEmitter, Input, OnDestroy, Output, } from '@angular/core';
import { FormControl } from '@angular/forms';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { OperatorsOwner } from '../../../../components/pages/interfaces/feature';
import { isResellerType, ShortEntity } from '../../../models/entity';

function toSelectOption( entity: ShortEntity, level: number = 0, result: SwuiSelectOption[] = [] ): SwuiSelectOption[] {
  if (Array.isArray(entity.child) && entity.child.length > 0) {
    result.push({
      id: entity.id,
      text: `${'-'.repeat(level)} ${entity.name}${entity.title ? ` - ${entity.title}` : ''}`,
      data: entity
    });
    entity.child.forEach(item => toSelectOption(item, level + 1, result));
  }
  return result;
}

@Component({
  selector: 'sw-owner-entity',
  templateUrl: './owner-entity.component.html',
  styleUrls: ['./owner-entity.component.scss'],
})
export class OwnerEntityComponent implements OnDestroy {
  @Input('owner') set setOwner( owner: OperatorsOwner | null | undefined ) {
    if (typeof owner === 'undefined') {
      return;
    }
    this.owner = owner;
    this.setValue();
  }

  @Input('entity') set setEntity( entity: ShortEntity | undefined ) {
    if (typeof entity === 'undefined') {
      return;
    }
    this.entity = entity;
    const options = toSelectOption(entity);
    this.entities = options.reduce(( result, { id, data } ) => ({ ...result, [id]: data }), {});
    this.selectOptions = options.filter(( { data } ) => isResellerType(data)).map(( { id, text } ) => ({ id, text }));
    this.setValue();
  }

  @Input() ownerTooltip: string | undefined;

  @Input('disabled') set setDisabled( value: boolean | undefined ) {
    this.disabled = coerceBooleanProperty(value);
    if (this.disabled) {
      this.ctrl.disable({ emitEvent: false });
    } else {
      this.ctrl.enable({ emitEvent: false });
    }
  }

  @Output() valueChange = new EventEmitter<ShortEntity | undefined>();

  selectOptions: SwuiSelectOption[] = [];
  entities: { [id: string]: ShortEntity } = {};
  disabled = false;

  readonly ctrl = new FormControl('');

  private owner: OperatorsOwner | null | undefined;
  private entity: ShortEntity | undefined;
  private readonly destroyed$ = new Subject();

  constructor() {
    this.ctrl.valueChanges.pipe(
      map(id => this.entities[id]),
      takeUntil(this.destroyed$)
    ).subscribe(entity => {
      if (entity) {
        this.valueChange.emit(entity);
      } else if (this.entity) {
        this.ctrl.setValue(this.entity.id, { emitEvent: false });
        this.valueChange.emit(this.entity);
      }
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  isRootEntity( id: string | undefined ): boolean {
    if (id && this.entities[id]) {
      return this.entities[id].path === ':';
    }
    return false;
  }

  resetEntity( event: Event ) {
    event.preventDefault();
    event.stopPropagation();
    this.ctrl.reset();
  }

  private setValue() {
    const value = this.owner ? this.owner.id : this.entity && this.entity.id;
    if (value) {
      this.ctrl.setValue(value, { emitEvent: false });
    }
  }
}
