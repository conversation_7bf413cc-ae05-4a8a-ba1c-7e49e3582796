import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { OwnerEntityComponent } from './owner-entity.component';
import { FakeMatIconRegistry } from '@angular/material/icon/testing';
import { MatIconModule, MatIconRegistry } from '@angular/material/icon';
import { ReactiveFormsModule } from '@angular/forms';
import { FlexModule } from '@angular/flex-layout';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatFormFieldModule } from '@angular/material/form-field';
import { SwuiSelectModule } from '@skywind-group/lib-swui';
import { MatButtonModule } from '@angular/material/button';


describe('OwnerEntityComponent', () => {
  let component: OwnerEntityComponent;
  let fixture: ComponentFixture<OwnerEntityComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        NoopAnimationsModule,
        TranslateModule.forRoot(),
        ReactiveFormsModule,
        FlexModule,
        MatIconModule,
        MatTooltipModule,
        MatFormFieldModule,
        SwuiSelectModule,
        MatButtonModule,
      ],
      declarations: [
        OwnerEntityComponent,
      ],
      providers: [
        { provide: MatIconRegistry, useClass: FakeMatIconRegistry }
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(OwnerEntityComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
