<div fxLayout.lt-sm="column" fxLayout="row">
  <div fxFlex.lt-sm="100" fxFlex="140px" fxFlexAlign="center" style="margin-bottom: 14px" class="red">
    {{ 'COMPONENTS.BRANDS_MANAGER.ownerEntity' | translate }}*
  </div>
  <div style="margin-right: 52px" fxFlexAlign="center" fxFlex="24px">
    <mat-icon
      class="help-icon"
      style="margin-bottom: 14px"
      svgIcon="question_mark"
      matTooltip="{{ ownerTooltip | translate }}">
    </mat-icon>
  </div>
  <div fxFlex.lt-sm="150" fxFlex="312px">
    <mat-form-field appearance="outline" style="width: 312px">
      <mat-label>Select reseller</mat-label>
      <lib-swui-select
        [startSearchLength]="3"
        [data]="selectOptions"
        [showSearch]="true"
        [disableEmptyOption]="true"
        [formControl]="ctrl">
      </lib-swui-select>
      <button
        *ngIf="!isRootEntity(ctrl.value)"
        mat-button
        matSuffix
        mat-icon-button
        aria-label="Clear"
        [disabled]="disabled"
        (click)="resetEntity($event)">
        <mat-icon>close</mat-icon>
      </button>
    </mat-form-field>
  </div>
</div>
