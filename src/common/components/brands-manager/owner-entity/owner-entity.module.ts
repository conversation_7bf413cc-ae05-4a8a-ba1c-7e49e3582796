import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiSelectModule } from '@skywind-group/lib-swui';
import { EntityService } from '../../../services/entity.service';
import { OwnerEntityComponent } from './owner-entity.component';


@NgModule({
  declarations: [OwnerEntityComponent],
  exports: [OwnerEntityComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ReactiveFormsModule,
    FlexModule,
    MatIconModule,
    MatTooltipModule,
    MatFormFieldModule,
    SwuiSelectModule,
    MatButtonModule,
  ],
  providers: [
    EntityService,
  ]
})
export class OwnerEntityModule {
}
