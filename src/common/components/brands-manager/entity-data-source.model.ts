import { SelectionModel } from '@angular/cdk/collections';
import { FlatTreeControl } from '@angular/cdk/tree';
import { ValidationErrors } from '@angular/forms';
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree';
import { Subject, Subscription } from 'rxjs';
import { OperatorsBrand } from '../../../components/pages/interfaces/feature';

import { EntityType, ShortEntity } from '../../models/entity';

export interface EntityNode {
  id: string;
  path: string;
  type?: EntityType;
  title: string;
  level: number;
  expandable: boolean;
}

export interface FundingErrors {
  id: string;
  errors: ValidationErrors;
}

export class TreeControl extends FlatTreeControl<EntityNode> {

  constructor() {
    super(
      ( { level } ) => level,
      ( { expandable } ) => expandable
    );
  }

  expandAllParents( ids: string[] ) {
    this.getNodes(ids).forEach(node => {
      this.expandParentNode(node);
    });
  }

  getNodes( ids: string[] ): EntityNode[] {
    return this.dataNodes.filter(( { id } ) => ids.includes(id));
  }

  private expandParentNode( node: EntityNode ): void {
    const parent = this.getParentNode(node);
    if (parent && !this.isExpanded(parent)) {
      this.expand(parent);
      if (this.getLevel(parent) > 0) {
        this.expandParentNode(parent);
      }
    }
  }

  private getParentNode( node: EntityNode ): EntityNode | null {
    const currentLevel = this.getLevel(node);
    if (currentLevel < 1) {
      return null;
    }
    const startIndex = this.dataNodes.indexOf(node) - 1;
    for (let i = startIndex; i >= 0; i--) {
      const currentNode = this.dataNodes[i];
      if (this.getLevel(currentNode) < currentLevel) {
        return currentNode;
      }
    }
    return null;
  }
}

function makeTreeControl( treeControl: TreeControl ): TreeControl {
  treeControl.dataNodes = [];
  return treeControl;
}

export class EntityDataSource extends MatTreeFlatDataSource<ShortEntity, EntityNode> {

  constructor( public treeControl = new TreeControl() ) {
    super(makeTreeControl(treeControl), new MatTreeFlattener<ShortEntity, EntityNode>(
      ( { id, type, path, name, title, child, decryptedBrand }, level ) => ({
        id,
        path,
        type,
        title: name || title || path,
        level,
        expandable: (child || []).length > 0,
        decryptedBrand
      }),
      ( { level } ) => level,
      ( { expandable } ) => expandable,
      ( { child } ) => child
      ),
    );
  }
}

export class EntitySelectionModel {
  readonly changed = new Subject<OperatorsBrand[]>();
  private selection: SelectionModel<string>;
  private brands: { [id: string]: OperatorsBrand & { errors?: ValidationErrors } } = {};
  private subscription: Subscription | null = null;

  constructor( multiple: boolean ) {
    this.selection = new SelectionModel<string>(multiple);
    this.selection.changed.pipe().subscribe(() => {
      this.emitChanged();
    });
  }

  get length(): number {
    return this.selection.selected.length;
  }

  get selected(): string[] {
    return this.selection.selected;
  }

  get selectedBrands(): OperatorsBrand[] {
    const result: OperatorsBrand[] = [];
    this.selection.selected.forEach(id => {
      const brand = this.brands[id];
      if (brand) {
        result.push(brand);
      }
    });
    return result;
  }

  setMultiple( multiple: boolean ): void {
    this.setSelection(multiple, this.selection.selected);
  }

  setBrands( brands: OperatorsBrand[] ): void {
    this.brands = brands.reduce(( result, brand ) => ({
      ...result,
      [brand.id]: brand
    }), {});
    this.setSelection(this.selection.isMultipleSelection(), brands.map(( { id } ) => id));
  }

  clear() {
    this.brands = {};
    this.selection.clear();
  }

  toggle( { id, path, title }: ShortEntity ) {
    if (this.selection.isSelected(id)) {
      delete this.brands[id];
      this.selection.deselect(id);
    } else {
      this.brands[id] = { id, path, title: title || '' };
      this.selection.select(id);
    }
  }

  deselect( ids: string[] ): boolean {
    let deselected = false;
    const newSelected = ids.reduce(( set, id ) => set.add(id), new Set());
    this.selection.selected.filter(id => !newSelected.has(id)).forEach(id => {
      deselected = true;
      this.selection.deselect(id);
      delete this.brands[id];
    });
    return deselected;
  }

  isSelected( { id }: EntityNode ): boolean {
    return this.selection.isSelected(id);
  }

  getFunding( { id }: EntityNode ): number | undefined {
    return this.brands[id] && this.brands[id].funding;
  }

  setFunding( { id }: EntityNode, value: number | undefined ): void {
    if (this.brands[id]) {
      this.brands[id].funding = value;
      this.emitChanged();
    }
  }

  setFundingErrors( { id }: EntityNode, errors: ValidationErrors | undefined ): void {
    if (this.brands[id]) {
      this.brands[id].errors = errors;
    }
  }

  hasFundingErrors(): boolean {
    return this.getFundingErrors().length > 0;
  }

  getFundingErrors(): FundingErrors[] {
    const result: FundingErrors[] = [];
    Object.entries(this.brands).forEach(( [id, { errors }] ) => {
      if (errors && Object.keys(errors).length > 0) {
        result.push({ id, errors });
      }
    });
    return result;
  }

  destroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  private emitChanged() {
    this.changed.next(this.selectedBrands);
  }

  private setSelection( multiple: boolean, values: string[] ) {
    this.selection = new SelectionModel<string>(multiple, values);
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    this.subscription = this.selection.changed.subscribe(() => {
      const result: OperatorsBrand[] = [];
      this.selection.selected.forEach(id => {
        const brand = this.brands[id];
        if (brand) {
          result.push(brand);
        }
      });
      this.changed.next(result);
    });
  }
}
