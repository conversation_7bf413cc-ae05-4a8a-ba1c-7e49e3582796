<div class="restricted-countries">
  <div class="restricted-countries__toggle">Include only players from specific countries
    <mat-slide-toggle class="restricted-countries__toggle-switch" [formControl]="toggleControl"></mat-slide-toggle>
  </div>
  <mat-form-field *ngIf="toggleControl.value" appearance="outline">
    <mat-label>{{ 'JACKPOT.FORM.SEGMENTATION.countries' | translate }}</mat-label>
    <lib-swui-chips-autocomplete
      [items]="countries"
      [formControl]="countriesControl"
    ></lib-swui-chips-autocomplete>
    <mat-error>
      <lib-swui-control-messages [control]="countriesControl"></lib-swui-control-messages>
    </mat-error>
  </mat-form-field>
</div>
