import { Component, forwardRef, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  AbstractControl, ControlValueAccessor, FormControl, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors
} from '@angular/forms';
import { SWUI_CONTROL_MESSAGES } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CountriesService } from '../../services/countries.service';

@Component({
  selector: 'sw-restricted-countries',
  templateUrl: './restricted-countries.component.html',
  styleUrls: ['./restricted-countries.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RestrictedCountriesComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => RestrictedCountriesComponent),
      multi: true
    },
    {
      provide: SWUI_CONTROL_MESSAGES,
      useValue: {
        required: 'VALIDATION.countriesRequired',
      }
    },
  ]
})
export class RestrictedCountriesComponent implements OnInit, ControlValueAccessor, OnDestroy {
  toggleControl = new FormControl();
  countriesControl = new FormControl(null, this.countriesValidator.bind(this));
  countries: any[] = [];
  messages = {
    required: 'At least 1 country should be selected'
  };

  private destroy$ = new Subject();

  constructor( readonly countriesService: CountriesService ) {
    countriesService.countries
      .pipe(takeUntil(this.destroy$))
      .subscribe(countries => {
        this.countries = countries;
      });
  }

  onChange: ( value: string[] | null ) => void = () => {
  };

  ngOnInit(): void {
    this.toggleControl.valueChanges
      .pipe(
        takeUntil(this.destroy$)
      )
      .subscribe(value => {
        if (!value) {
          this.countriesControl.setValue(null);
        }
      });

    this.countriesControl.valueChanges
      .pipe(
        takeUntil(this.destroy$)
      )
      .subscribe(value => {
        this.onChange(value);
      });
  }

  registerOnChange( fn: any ): void {
    this.onChange = fn;
  }

  registerOnTouched(): void {
  }

  writeValue( obj: any ): void {
    this.toggleControl.setValue(Array.isArray(obj));
    this.countriesControl.patchValue(obj);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  validate(): ValidationErrors | null {
    return this.countriesControl.errors;
  }

  setDisabledState?( disabled: boolean ): void {
    if (disabled) {
      this.toggleControl.disable();
      this.countriesControl.disable();
    } else {
      this.toggleControl.enable();
      this.countriesControl.enable();
    }
  }

  private countriesValidator( control: AbstractControl ): ValidationErrors | null {
    if (!this.toggleControl.value) {
      return null;
    }

    return control.value?.length ? null : { required: true };
  }

}
