import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiChipsAutocompleteModule, SwuiControlMessagesModule } from '@skywind-group/lib-swui';
import { RestrictedCountriesComponent } from './restricted-countries.component';

@NgModule({
    imports: [
        CommonModule,
        MatSlideToggleModule,
        SwuiChipsAutocompleteModule,
        TranslateModule.forChild(),
        ReactiveFormsModule,
        MatFormFieldModule,
        SwuiControlMessagesModule,
    ],
  declarations: [
    RestrictedCountriesComponent
  ],
  exports: [
    RestrictedCountriesComponent
  ]
})
export class RestrictedCountriesModule {
}
