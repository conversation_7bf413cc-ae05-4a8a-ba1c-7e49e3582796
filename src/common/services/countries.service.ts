import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { OptionModel } from '@skywind-group/lib-swui/swui-autoselect/option.model';
import { BehaviorSubject, Observable } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { CountryModel } from '../models/country.model';

const URL = `${environment.CONFIGURATION_API}/configuration/countries`;

@Injectable()
export class CountriesService {
  private countries$: BehaviorSubject<OptionModel[]> = new BehaviorSubject<OptionModel[]>([]);

  constructor( private readonly http: HttpClient ) {
  }

  get countries(): Observable<OptionModel[]> {
    if (!this.countries$.value?.length) {
      this.getCountries();
    }

    return this.countries$;
  }

  private getCountries(): void {
    this.http.get<CountryModel[]>(URL).pipe(
      map(countries => countries.map(( { name, id } ) => ({ id, text: name }))),
      catchError(err => {
        console.error(err);
        return [];
      }),
    ).subscribe(countries => {
      this.countries$.next(countries);
    });
  }
}
