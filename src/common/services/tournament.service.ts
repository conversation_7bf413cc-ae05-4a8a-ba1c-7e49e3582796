import { HttpClient, HttpErrorResponse, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridDataService, } from '@skywind-group/lib-swui';
import { Observable, of } from 'rxjs';
import { catchError, filter } from 'rxjs/operators';
import { Tournament } from '../../components/pages/interfaces/tournament';

import { environment } from '../../environments/environment';
import { CommonService } from './common/common.service';

const URL = `${environment.TOURNAMENT_API}/features/tournament/`;

@Injectable()
export class TournamentService implements GridDataService<Tournament> {

  constructor(
    private readonly http: HttpClient,
    private readonly commonService: CommonService ) {
  }

  getGridData( params: HttpParams ): Observable<HttpResponse<Tournament[]>> {
    return this.http.get<Tournament[]>(URL, { params, observe: 'response' }).pipe(
      filter(resp => !!resp),
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  get( id: string ): Observable<Tournament> {
    return this.http.get<Tournament>(`${URL}${id}`).pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  create( body: Tournament ): Observable<Tournament> {
    return this.http.post<Tournament>(URL, body).pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  update( id: string, body: Tournament ): Observable<Tournament> {
    return this.http.put<Tournament>(`${URL}/${id}`, body).pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  patch( id: string, option: Object ): Observable<Tournament> {
    return this.http.patch<Tournament>(`${URL}/${id}`, option).pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  delete( id: string | undefined ): Observable<Tournament> {
    return this.http.delete<Tournament>(`${URL}${id}`)
      .pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  forceDelete( id: string | undefined ): Observable<Tournament> {
    return this.http.delete<Tournament>(`${URL}force/${id}`)
      .pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }
}

export class MockTournamentService {

  // noinspection JSUnusedGlobalSymbols
  getGridData(): Observable<HttpResponse<Tournament[]>> {
    return of(new HttpResponse<Tournament[]>({ body: [] }));
  }

  get(): Observable<Tournament | null> {
    return of(null);
  }
}
