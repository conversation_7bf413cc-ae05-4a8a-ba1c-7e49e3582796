import { HttpClient, HttpErrorResponse, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, of, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

import { environment } from '../../environments/environment';
import { PromoPlayer } from '../models/promo-player.model';
import { Promotion } from '../models/promotion';


function buildUrl( promoId = '', path?: string ): string {
  return `${environment.PROMOTION_API}${path && path !== ':' ? '/entities/' + path : ''}/promo/${promoId}/players`;
}

@Injectable()
export class PromotionPlayersService {

  constructor( private readonly http: HttpClient,
               private readonly notifications: SwuiNotificationsService
  ) {
  }

  getList( promoId: string, path: string, page: number ): Observable<HttpResponse<PromoPlayer[]>> {
    return this.http.get<PromoPlayer[]>(buildUrl(promoId, path), {
      params: {
        offset: `${page * 10}`,
        limit: '10'
      },
      observe: 'response'
    })
      .pipe(
        catchError(error => this.handleErrors(error)),
      );
  }

  update( promoId: string, path: string, ids: string[] ): Observable<any> {
    const url = buildUrl(promoId, path);
    const body = {
      code__in: ids.join(','),
    };

    return this.http.put(url, body)
      .pipe(
        catchError(error => this.handleErrors(error)),
      );
  }

  delete( promoId: string, path: string, id: string, forceDelete = 'false' ) {
    const url = `${environment.PROMOTION_API}${path ? '/entities/' + path : ''}/players/${id}/promo/${promoId}`;
    return this.http.delete(url, {
      params: {
        force: forceDelete
      },
    });
  }

  query( params: HttpParams | { [header: string]: string | string[] } ): Observable<Promotion[]> {
    return this.http.get<Promotion[]>(buildUrl(), { params });
  }

  get( id: string, path?: string ): Observable<Promotion> {
    return this.http.get<Promotion>(buildUrl(id, path)).pipe(
      catchError(error => this.handleErrors(error)),
    );
  }

  private handleErrors( response: HttpErrorResponse ): Observable<never> {
    const body = response.error;
    const message = body ? body.message : response.statusText;
    const title = body ? body.error : `Status: ${response.status}`;
    this.notifications.error(message, title);
    return throwError(response);
  }
}

export class MockPromotionPlayersService {
  getList(): Observable<HttpResponse<PromoPlayer[]>> {
    return of(new HttpResponse({
      body: [],
    }));
  }
}
