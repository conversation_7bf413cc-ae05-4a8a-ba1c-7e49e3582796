import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { TranslateModule } from '@ngx-translate/core';
import { SettingsService, SwuiNotificationsModule } from '@skywind-group/lib-swui';

import { PromotionService } from './promotion.service';

describe('PromotionService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports: [
      HttpClientTestingModule,
      TranslateModule.forRoot(),
      SwuiNotificationsModule.forRoot()
    ],
    providers: [
      SettingsService,
      PromotionService
    ],
    schemas: [NO_ERRORS_SCHEMA]
  }));

  it('should be created', () => {
    const service: PromotionService = TestBed.inject(PromotionService);
    expect(service).toBeTruthy();
  });
});
