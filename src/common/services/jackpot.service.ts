import { HttpClient, HttpErrorResponse, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridDataService } from '@skywind-group/lib-swui';
import { Observable } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { Jackpot } from '../../components/pages/interfaces/jackpot';
import { environment } from '../../environments/environment';

import { CommonService } from './common/common.service';

const URL = `${environment.JACKPOT_API}/features/jackpot/`;
const mappingUrl = `${environment.JACKPOT_CONFIGURATION_API}/mapping`;
const optionsUrl = `${environment.JACKPOT_CONFIGURATION_API}/options`;

@Injectable()
export class JackpotService implements GridDataService<Jackpot> {

  constructor( private readonly commonService: CommonService,
               private readonly http: HttpClient ) {
  }

  getGridData( params: HttpParams ): Observable<HttpResponse<Jackpot[]>> {
    return this.http.get<Jackpot[]>(URL, { params, observe: 'response' }).pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error))
    );
  }

  get( id: string ): Observable<Jackpot> {
    return this.http.get<Jackpot>(`${URL}${id}`).pipe(
      tap(data => {
        return this.processRecord(data);
      }),
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  create( body: Jackpot ): Observable<Jackpot> {
    return this.http.post<Jackpot>(URL, body).pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  update( id: string, body: Jackpot ): Observable<Jackpot> {
    return this.http.put<Jackpot>(`${URL}/${id}`, body).pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  patch( id: string, option: any ): Observable<Jackpot> {
    return this.http.patch<Jackpot>(`${URL}/${id}`, option).pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  delete( id: string | undefined ): Observable<Jackpot> {
    return this.http.delete<Jackpot>(`${URL}${id}`)
      .pipe(
        catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
      );
  }

  forceDelete( id: string | undefined ): Observable<Jackpot> {
    return this.http.delete<Jackpot>(`${URL}force/${id}`)
      .pipe(
        catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error))
      );
  }

  getConfiguration() {
    return this.http.get<any>(mappingUrl).pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  getOptions( pool: string ): Observable<any> {
    const params = new HttpParams().set('pool', pool);
    return this.http.get<any>(optionsUrl, { params }).pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  private processRecord( response: Jackpot ) {
    response.ui.poolId = response.configuration.poolId;
    return response;
  }
}

// @ts-ignore
export class MockJpnService implements JackpotService {
}
