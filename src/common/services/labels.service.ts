import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { catchError, share } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { Label } from '../models/label';

import { CommonService } from './common/common.service';

const URL = `${environment.MAPI}/labels`;

@Injectable()
export class LabelsService {

  public urlGetLabelGroups = `${environment.MAPI}/label-groups`;

  constructor( private readonly http: HttpClient,
               private readonly commonService: CommonService
  ) {
  }

  getList( type: 'game' | 'entity' | 'promotion' ): Observable<Label[]> {
    let params = { type };

    return this.http.get<Label[]>(URL, { params })
      .pipe(
        catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
      );
  }

  addLabel( body: { title: string, groupId: string } ) {
    return this.http
      .post(URL, body)
      .pipe(
        catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
      );
  }

  getLabelGroups( type?: string ) {
    let params = new HttpParams();
    if (type) {
      params = params.set('type', type);
    }

    return this.http
      .get<any[]>(this.urlGetLabelGroups, { params })
      .pipe(
        catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
        share(),
      );
  }
}

export class MockLabelsService {
  getLabelGroups(): Observable<any[]> {
    return of([]);
  }
}
