import { HttpClient, HttpErrorResponse, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { GridDataService, SettingsService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import moment from 'moment';
import { Observable, of, throwError } from 'rxjs';
import { catchError, take, tap } from 'rxjs/operators';
import { FORMAT_DATETIME } from 'src/app.constants';

import { environment } from '../../environments/environment';
import { simulateBrowserDownload } from '../lib/files';
import { Promotion, PromotionData } from '../models/promotion';

const csvSchema = [
  {
    name: 'title',
    title: 'PROMOTION.CSV.title',
  },
  {
    name: 'id',
    title: 'PROMOTION.CSV.id',
  },
  {
    name: 'brandPath',
    title: 'PROMOTION.CSV.brandPath',
  },
  {
    name: 'type',
    title: 'PROMOTION.CSV.type',
  },
  {
    name: 'createdUserId',
    title: 'PROMOTION.CSV.createdUserId',
  },
  {
    name: 'status',
    title: 'PROMOTION.CSV.status',
  },
  {
    name: 'state',
    title: 'PROMOTION.CSV.state',
  },
  {
    name: 'totalParticipated',
    title: 'PROMOTION.CSV.totalParticipated',
  },
  {
    name: 'labels',
    title: 'PROMOTION.CSV.labels',
  },
  {
    name: 'startDate',
    title: 'PROMOTION.CSV.startDate',
    transform: ( date: string, format: string ) => date ? moment(date).format(format) : '',
  },
  {
    name: 'endDate',
    title: 'PROMOTION.CSV.endDate',
    transform: ( date: string, format: string ) => date ? moment(date).format(format) : '',
  },
  {
    name: 'createdAt',
    title: 'PROMOTION.CSV.createdAt',
    transform: ( date: string, format: string ) => date ? moment(date).format(format) : '',
  },
  {
    name: 'actions',
    title: 'PROMOTION.CSV.actions',
  },
];

function buildUrl( url = '', path?: string ): string {
  return `${environment.PROMOTION_API}${path && path !== ':' ? '/entities/' + path : ''}/promo/${url}`;
}

@Injectable()
export class PromotionService implements GridDataService<Promotion> {

  constructor( private readonly http: HttpClient,
               private readonly translate: TranslateService,
               private readonly notifications: SwuiNotificationsService,
               private readonly settingsService: SettingsService,
  ) {
  }

  getGridData( params: HttpParams ): Observable<HttpResponse<Promotion[]>> {
    return this.http.get<Promotion[]>(buildUrl('', params.get('path') || ''), {
      params: params.append('includeTotals', 'true')
        .append('includeGames', 'true'),
      observe: 'response'
    }).pipe(
      catchError(error => this.handleErrors(error)),
    );
  }

  query( params: HttpParams | { [header: string]: string | string[] } ): Observable<Promotion[]> {
    return this.http.get<Promotion[]>(buildUrl(), { params });
  }

  get( id: string, path?: string ): Observable<Promotion> {
    return this.http.get<Promotion>(buildUrl(id, path)).pipe(
      catchError(error => this.handleErrors(error)),
    );
  }

  modify( body: Partial<PromotionData>, path?: string ): Observable<Promotion> {
    if (body.id) {
      return this.update(body, path);
    }
    return this.create(body, path);
  }

  downloadCsv() {
    this.http.get(buildUrl(), {
      params: {
        format: 'csv',
        limit: '10000',
        offset: '0',
        includeTotals: 'true'
      },
      responseType: 'text'
    }).pipe(take(1))
      .subscribe(data => {
        const fileName = `Export Promotions list ${moment().format('YYYY-MM-DD HH:MM')}`;
        this.transformCsv(data, fileName);
      });
  }

  changeStatus( id: string, status: 'active' | 'inactive', path?: string ): Observable<Promotion[]> {
    return this.http.put<Promotion[]>(buildUrl('status', path), {
      promosId: [id],
      status,
    }).pipe(
      catchError(error => this.handleErrors(error)),
    );
  }

  private transformCsv( data: string, fileName: string ) {
    const rows = data.trim().split('\n');

    const headers = (rows.shift() || '')
      .split(',')
      .map(item => item.replace(/\"/g, ''));
    const indexes = csvSchema
      .reduce(( res: number[], item ) => {
        const index = headers.indexOf(item.name);

        res.push(index);

        return res;
      }, []);
    const displayHeaders = csvSchema.map(( { title } ) => this.translate.instant(title));

    const rowsArray = rows
      .map(row => row.split(/,(?=\"|,)|,$/g)
        .map(item => item.replace(/\"/g, ''))
      );
    const newRows: string[][] = Array.from(new Array(rows.length)).map(() => []);
    const { appSettings: { dateFormat, timeFormat } } = this.settingsService;
    const format = dateFormat && timeFormat ? `${dateFormat} ${timeFormat}` : FORMAT_DATETIME;

    indexes.forEach(( headerIndex, index ) => {
      newRows.forEach(( _, i ) => {
        const schemaItem = csvSchema[index];
        const value = schemaItem.transform
          ? schemaItem.transform(rowsArray[i][headerIndex] || '', format)
          : rowsArray[i][headerIndex];

        newRows[i].push(value);
      });
    });

    simulateBrowserDownload([displayHeaders, ...newRows].join('\n'), fileName);
  }

  private create( body: Partial<PromotionData>, path?: string ): Observable<Promotion> {
    const { title } = body;
    return this.http.post<Promotion>(buildUrl('', path), body).pipe(
      catchError(error => this.handleErrors(error)),
      tap(() => {
        this.notifications.success(this.translate.instant('PROMOTION.created', { title }));
      })
    );
  }

  private update( body: Partial<PromotionData>, path?: string ): Observable<Promotion> {
    const { title } = body;
    return this.http.patch<Promotion>(buildUrl('', path), body).pipe(
      catchError(error => this.handleErrors(error)),
      tap(() => {
        this.notifications.success(this.translate.instant('PROMOTION.updated', { title }));
      })
    );
  }

  private handleErrors( response: HttpErrorResponse ): Observable<never> {
    const body = response.error;
    const message = body ? body.message : response.statusText;
    const title = body ? body.error : `Status: ${response.status}`;
    this.notifications.error(message, title);
    return throwError(response);
  }
}

export class MockPromotionService {
  // noinspection JSUnusedGlobalSymbols
  getGridData(): Observable<HttpResponse<Promotion[]>> {
    return of(new HttpResponse<Promotion[]>({ body: [] }));
  }

  query(): Observable<HttpResponse<Promotion[]>> {
    return of(new HttpResponse<Promotion[]>({ body: [] }));
  }
}
