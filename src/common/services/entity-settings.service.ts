import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { EntitySettings } from '../models/entity-settings.model';


function buildUrl( uri: string, path?: string ) {
  return `${environment.MAPI}/${(path && path !== ':' ? `entities/${path}` : '')}/${uri}`;
}

@Injectable()
export class EntitySettingsService {

  constructor( private readonly http: HttpClient ) {
  }

  getSettings( path: string = '' ): Observable<EntitySettings> {
    return this.http.get<EntitySettings>(buildUrl('/settings', path));
  }
}
