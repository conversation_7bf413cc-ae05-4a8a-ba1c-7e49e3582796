import { HttpErrorResponse, } from '@angular/common/http';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { throwError } from 'rxjs';
import { Injectable } from '@angular/core';

@Injectable()
export class CommonService {

  constructor( private readonly notifications: SwuiNotificationsService ) {
  }

  public handleErrors( httpErrorResponse: HttpErrorResponse ) {
    const body = httpErrorResponse.error;

    body ? this.notifications.error(body.message, body.error) :
      this.notifications.error(httpErrorResponse.statusText, `Status: ${httpErrorResponse.status}`);

    return throwError(httpErrorResponse);
  }
}
