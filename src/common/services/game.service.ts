import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { GameInfo } from '../models/game';

import { CommonService } from './common/common.service';

function buildUrl( path?: string ) {
  const uri = path && path !== ':' ? `/entities/${path}` : '';
  return `${environment.MAPI}${uri}/games`;
}

@Injectable()
export class GameService {

  constructor( private readonly http: HttpClient,
               private readonly commonService: CommonService
  ) {
  }

  query( path = ':' ): Observable<GameInfo[]> {
    const params = new HttpParams({ fromObject: { limit: '10000', offset: '0', shortInfo: 'true' } });
    return this.http.get<GameInfo[]>(buildUrl(path), { params }).pipe(
      catchError(error => this.commonService.handleErrors(error)),
    );
  }

  public getItem( code: string, path = ':' ): Observable<GameInfo> {
    return this.http.get<GameInfo>(`${buildUrl(path)}/${code}`).pipe(
      catchError(error => this.commonService.handleErrors(error)),
    );
  }

  public getAggregatedItem( code: string, path = ':', currency?: string ): Observable<GameInfo> {
    const paramsObj: Record<string, any> = {
      addAggregatedFinalLimits: 'true'
    };

    if (currency) {
      paramsObj.currency = currency;
    }

    const params = new HttpParams({
      fromObject: paramsObj
    });

    return this.http.get<GameInfo>(`${buildUrl(path)}/${code}`, { params }).pipe(
      catchError(error => this.commonService.handleErrors(error)),
    );
  }
}

@Injectable()
export class GamesResolver implements Resolve<GameInfo[]> {

  constructor( private service: GameService ) {
  }

  resolve(): Observable<GameInfo[]> {
    return this.service.query();
  }
}

// @ts-ignore
export class MockGameService implements GameService {

  query(): Observable<GameInfo[]> {
    return of([]);
  }
}
