import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { SwHubAuthService } from '@skywind-group/lib-swui';

import { EntityService } from './entity.service';
import { MockAuthService } from './mock-auth.service';

describe('EntityService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    providers: [
      EntityService,
      { provide: SwHubAuthService, useClass: MockAuthService }
    ],
    imports: [
      HttpClientTestingModule,
    ],
    schemas: [NO_ERRORS_SCHEMA],
  }));

  it('should be created', () => {
    const service: EntityService = TestBed.inject(EntityService);
    expect(service).toBeTruthy();
  });
});
