import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwHubAuthService } from '@skywind-group/lib-swui';

import { BehaviorSubject, Observable } from 'rxjs';

import { environment } from '../../environments/environment';
import { StoreService } from '../lib/store-service';
import { Entity, MerchantEntity, ShortEntity } from '../models/entity';

class ShortStructureService extends StoreService<ShortEntity> {

  constructor( private readonly http: HttpClient,
               private readonly auth: SwHubAuthService ) {
    super();
  }

  get entity$(): Observable<ShortEntity | null> {
    return this.item$;
  }

  protected load(): Observable<ShortEntity> {
    return this.http.get<ShortEntity>(`${environment.MAPI}/short-structure`,
      { params: this.auth.isSuperAdmin ? { includeDecryptedBrand: 'true'} : {} });
  }
}

class BriefService extends StoreService<Entity> {

  constructor( private readonly http: HttpClient ) {
    super();
  }

  get entity$(): Observable<Entity | null> {
    return this.item$;
  }

  protected load(): Observable<Entity> {
    return this.http.get<Entity>(`${environment.MAPI}/brief`);
  }
}

@Injectable()
export class EntityService {
  private readonly structureService: ShortStructureService;
  private readonly briefService: BriefService;

  constructor( private readonly http: HttpClient,
               private readonly auth: SwHubAuthService) {
    this.structureService = new ShortStructureService(this.http, this.auth);
    this.briefService = new BriefService(this.http);
  }

  get structure$(): Observable<ShortEntity | null> {
    return this.structureService.entity$;
  }

  get brief$(): Observable<Entity | null> {
    return this.briefService.entity$;
  }

  getEntity( path: string ): Observable<MerchantEntity> {
    return this.http.get<MerchantEntity>(`${environment.MAPI}/entities/${path}`);
  }

  getMerchantEntity( path: string ): Observable<MerchantEntity> {
    let url = path && path !== ':' ?
      `${environment.MAPI}/merchantentities/${path}` :
      `${environment.MAPI}/merchantentities/`;

    return this.http.get<MerchantEntity>(url);
  }
}

export class MockEntityService {
  structure$ = new BehaviorSubject<ShortEntity | null>(null);
  brief$ = new BehaviorSubject<Entity | null>(null);
}
