import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Observable } from 'rxjs';
import { Jackpot } from '../../../components/pages/interfaces/jackpot';

import { JackpotService } from '../jackpot.service';

@Injectable()
export class JackpotResolver implements Resolve<Jackpot> {

  constructor( private service: JackpotService ) {
  }

  resolve( { params: { id } }: ActivatedRouteSnapshot ): Observable<Jackpot> {
    return this.service.get(id);
  }
}
