import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { PromotionData } from '../../models/promotion';

import { PromotionService } from '../promotion.service';

@Injectable()
export class ClonePromotionResolver implements Resolve<PromotionData> {

  constructor( private readonly service: PromotionService ) {
  }

  resolve( { params: { id, path } }: ActivatedRouteSnapshot ): Observable<PromotionData> {
    return this.service.get(id, path).pipe(map(promotion => {
      const newPromotion: PromotionData = { ...promotion, brandPath: path };
      delete newPromotion.id;
      delete newPromotion.title;
      delete newPromotion.state;
      delete newPromotion.createdAt;
      delete newPromotion.updatedAt;
      return newPromotion;
    }));
  }
}
