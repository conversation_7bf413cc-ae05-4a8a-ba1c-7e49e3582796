import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { of } from 'rxjs';
import { catchError, map, mergeMap, take } from 'rxjs/operators';
import { EntityService } from '../entity.service';

@Injectable()
export class MerchantBriefResolver implements Resolve<any> {

  constructor( private entityService: EntityService ) {
  }

  resolve() {
    return this.entityService.brief$
      .pipe(
        take(1),
        mergeMap(brief => {
          if (brief?.isMerchant) {
            return this.entityService.getMerchantEntity(':').pipe(
              map(entity => ({
                ...brief,
                hasCustomers: entity?.merchant?.params?.isPromoInternal,
                params: entity?.merchant?.params,
              }))
            );
          }

          return of({ ...brief, hasCustomers: true });
        }),
        catchError(() => of(null)),
      );
  }
}
