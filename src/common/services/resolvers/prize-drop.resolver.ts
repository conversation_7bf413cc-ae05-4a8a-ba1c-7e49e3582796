import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Observable } from 'rxjs';
import { PrizeDrop } from '../../../components/pages/interfaces/prize-drop';
import { PrizeDropService } from '../prize-drop.service';

@Injectable()
export class PrizeDropResolver implements Resolve<PrizeDrop> {

  constructor( private service: PrizeDropService ) {
  }

  resolve( { params: { id } }: ActivatedRouteSnapshot ): Observable<PrizeDrop> {
    return this.service.get(id);
  }
}
