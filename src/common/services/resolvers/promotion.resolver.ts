import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Promotion } from '../../models/promotion';

import { PromotionService } from '../promotion.service';

@Injectable()
export class PromotionResolver implements Resolve<Promotion> {

  constructor( private readonly service: PromotionService ) {
  }

  resolve( { params: { id, path } }: ActivatedRouteSnapshot ): Observable<Promotion> {
    return this.service.get(id, path).pipe(
      map(promotion => ({ ...promotion, brandPath: path })),
    );
  }
}
