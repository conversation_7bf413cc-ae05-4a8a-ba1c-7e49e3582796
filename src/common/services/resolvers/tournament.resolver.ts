import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Observable } from 'rxjs';
import { Tournament } from '../../../components/pages/interfaces/tournament';

import { TournamentService } from '../tournament.service';

@Injectable()
export class TournamentResolver implements Resolve<Tournament> {

  constructor( private service: TournamentService ) {
  }

  resolve( { params: { id } }: ActivatedRouteSnapshot ): Observable<Tournament> {
    return this.service.get(id);
  }
}
