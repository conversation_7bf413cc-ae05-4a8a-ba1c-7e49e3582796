import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError, take } from 'rxjs/operators';
import { Entity } from '../../models/entity';
import { EntityService } from '../entity.service';

@Injectable()
export class BriefResolver implements Resolve<Entity | null> {

  constructor( private readonly service: EntityService ) {
  }

  resolve(): Observable<Entity | null> {
    return this.service.brief$.pipe(
      take(1),
      catchError(() => of(null)),
    );
  }
}
