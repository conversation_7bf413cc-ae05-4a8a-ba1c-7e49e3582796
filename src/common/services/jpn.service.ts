import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../environments/environment';

import { CommonService } from './common/common.service';

const exchangeUrl = `${environment.JPN_API}/exchange/`;

@Injectable()
export class JpnService {
  constructor( private readonly commonService: CommonService,
               private http: HttpClient
  ) {
  }

  getExchangeRate( baseCurrency: string | undefined = 'EUR', targetCurrency: string ): Observable<any> {
    return this.http.get<any>(`${exchangeUrl}${baseCurrency}/${targetCurrency}`).pipe(
      map(( item: any ) => {
        item.currency = targetCurrency;

        return item;
      }),
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }
}

// @ts-ignore
export class MockJpnService implements JpnService {

  getExchangeRate(): Observable<any> {
    return of({});
  }
}
