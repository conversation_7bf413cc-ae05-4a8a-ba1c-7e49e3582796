import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { CommonServiceModule } from './common/common.service.module';

import { GameService } from './game.service';

describe('GameService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    providers: [GameService],
    imports: [
      HttpClientTestingModule,
      CommonServiceModule,
    ],
    schemas: [NO_ERRORS_SCHEMA]
  }));

  it('should be created', () => {
    const service: GameService = TestBed.inject(GameService);
    expect(service).toBeTruthy();
  });
});
