import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { catchError, share } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { Label } from '../models/label';
import { CommonService } from './common/common.service';

@Injectable()
export class PromotionLabelsService {

  public urlPromotionLabels = 'promo-labels';

  constructor( private commonService: CommonService,
               private http: HttpClient,
  ) {
  }

  updatePromotionLabels( path: string, promoId: string, body: { id: string }[] ): Observable<any> {
    let url = `${this.getUrl(path)}/promo/${promoId}/${this.urlPromotionLabels}`;

    return this.http
      .put(url, body)
      .pipe(
        catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
        share(),
      );
  }

  getUrl( path: string ): string {
    if (path && path !== ':') {
      return path ? `${environment.PROMOTION_API}/entities/${path}/` : `${environment.PROMOTION_API}`;
    } else {
      return `${environment.PROMOTION_API}`;
    }
  }
}

export class MockPromotionLabelsService {
  query(): Observable<Label[]> {
    return of([]);
  }
}
