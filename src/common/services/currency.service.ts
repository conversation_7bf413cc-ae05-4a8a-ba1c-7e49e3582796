import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { CurrencyModel } from '../models/currency.model';

const URL = `${environment.MAPI}/currencies`;
const CODES = [
  'EUR', 'USD', 'GBP', 'AUD', 'AZN', 'BGN', 'BND', 'CAD', 'CHF', 'GEL', 'NZD', 'SGD', 'BMD', 'XXX', 'BRL', 'ILS',
  'MYR', 'PEN', 'PLN', 'RON', 'TRY', 'GHS', 'RUP', 'ARS', 'CNY', 'DKK', 'HKD', 'HRK', 'MAD', 'MOP', 'NOK', 'SEK',
  'VEF', 'ZAR', 'ZMW', 'CZK', 'DOP', 'HNL', 'INR', 'KGS', 'MDL', 'NIO', 'PHP', 'RUB', 'THB', 'TWD', 'UAH', 'UYU',
  'VES', 'ISK', 'JPY', 'RSD', 'KES', 'CLP', 'HUF', 'KZT', 'XOF', 'CRC', 'KRW', 'COP', 'MNT', 'TZS', 'MMK', 'PYG',
  'IDR', 'VND', 'IDS', 'VNS', 'VDO', 'MXN', 'BNS', 'XAF', 'CDF', 'NGN', 'SLL', 'UGX', 'AOA', 'GNF', 'MZN', 'MWK',
  'RWF', 'TND'
];

@Injectable()
export class CurrencyService {
  constructor( private readonly http: HttpClient ) {
  }

  query(): Observable<CurrencyModel[]> {
    return this.http.get<CurrencyModel[]>(URL).pipe(
      map(currencies => currencies.filter(( { code } ) => CODES.includes(code))),
      catchError(err => {
        console.error(err);
        return [];
      }),
    );
  }
}

@Injectable()
export class CurrenciesResolver implements Resolve<CurrencyModel[]> {

  constructor( private service: CurrencyService ) {
  }

  resolve(): Observable<CurrencyModel[]> {
    return this.service.query();
  }
}

export class MockCurrencyService {
  query(): Observable<CurrencyModel[]> {
    return of([]);
  }
}
