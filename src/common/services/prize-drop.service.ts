import { HttpClient, HttpErrorResponse, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridDataService, } from '@skywind-group/lib-swui';
import { Observable, of } from 'rxjs';
import { catchError, filter } from 'rxjs/operators';
import { PrizeDrop } from '../../components/pages/interfaces/prize-drop';

import { environment } from '../../environments/environment';
import { CommonService } from './common/common.service';

const URL = `${environment.PRIZE_DROP_API}/features/prizedrop/`;

@Injectable()
export class PrizeDropService implements GridDataService<PrizeDrop> {
  constructor(
    private readonly http: HttpClient,
    private readonly commonService: CommonService ) {
  }

  getGridData( params: HttpParams ): Observable<HttpResponse<PrizeDrop[]>> {
    return this.http.get<PrizeDrop[]>(URL, { params, observe: 'response' }).pipe(
      filter(resp => !!resp),
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  get( id: string ): Observable<PrizeDrop> {
    return this.http.get<PrizeDrop>(`${URL}${id}`).pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  create( body: PrizeDrop ): Observable<PrizeDrop> {
    return this.http.post<PrizeDrop>(URL, body).pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  update( id: string, body: PrizeDrop ): Observable<PrizeDrop> {
    return this.http.put<PrizeDrop>(`${URL}/${id}`, body).pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  patch( id: string, option: Object ): Observable<PrizeDrop> {
    return this.http.patch<PrizeDrop>(`${URL}/${id}`, option).pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  delete( id: string | undefined ): Observable<PrizeDrop> {
    return this.http.delete<PrizeDrop>(`${URL}${id}`)
      .pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }

  forceDelete( id: string | undefined ): Observable<PrizeDrop> {
    return this.http.delete<PrizeDrop>(`${URL}force/${id}`)
      .pipe(
      catchError(( error: HttpErrorResponse ) => this.commonService.handleErrors(error)),
    );
  }
}

export class MockPrizeDropService {

  // noinspection JSUnusedGlobalSymbols
  getGridData(): Observable<HttpResponse<PrizeDrop[]>> {
    return of(new HttpResponse<PrizeDrop[]>({ body: [] }));
  }

  get(): Observable<PrizeDrop | null> {
    return of(null);
  }
}
