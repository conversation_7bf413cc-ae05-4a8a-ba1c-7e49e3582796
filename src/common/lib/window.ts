export interface WindowOptions {
  width?: number;
  height?: number;
  left?: number;
  top?: number;
  toolbar?: number;
  location?: number;
}

export function createNewWindow( url: string, name: string, options: WindowOptions ): Window {
  const features = `width=${options.width},height=${options.height},left=${options.left},
    top=${options.top},location=${options.location},toolbar=${options.toolbar}`;
  return window.open(url, name, features) as Window;
}
