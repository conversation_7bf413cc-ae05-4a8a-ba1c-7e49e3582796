import { AbstractControl, FormControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { SegmentationBrand } from '../../components/pages/interfaces/feature';
import * as moment from 'moment';

export interface ErrorMessage {
  [key: string]: any;
}

export function digitsOnlyValidator( control: AbstractControl ) {
  const regexp = /^(\d+)$/;
  const value = control.value === null ? '' : control.value.toString();
  if (!value || value.match(regexp)) {
    return null;
  } else {
    return { 'digitsOnly': true };
  }
}

export function numbersOnlyValidator( control: AbstractControl ) {
  const regexp = /^\d+(\.\d{1,2})?$/; // example 10.142
  const value = control.value === null ? '' : control.value.toString();
  if (!value || value.match(regexp)) {
    return null;
  } else {
    return { 'numbersOnly': true };
  }
}

export function fractionsNumbersOnlyValidator( control: AbstractControl ) {
  const regexp = /^\d+(\.\d+)?$/;
  const value = control.value === null ? '' : control.value.toString();
  if (!value || value.match(regexp)) {
    return null;
  } else {
    return { 'fractionsNumbersOnly': true };
  }
}

export function ImageUrlValidator( control: AbstractControl ) {
  const regexpUrl = /(((http:\/\/www)|(http:\/\/)|(https:\/\/)|(www))[-a-zA-Z0-9@:%_\+.~#?&//=]+)\.(jpg|jpeg|gif|png|bmp|tiff|tga|svg)/g;
  const regexpBase64 = /^\s*data:([image])([a-z]+\/[a-z]+(;[a-z\-]+\=[a-z\-]+)?)?(;base64)?,[a-z0-9\!\$\&\'\,\(\)\*\+\,\;\=\-\.\_\~\:\@\/\?\%\s]*\s*$/i;
  const value = control.value === null ? '' : control.value.toString();
  if (!value || value.match(regexpUrl) || value.match(regexpBase64)) {
    return null;
  } else {
    return { 'invalidImageUrl': true };
  }
}

export function fractionsNumbersLengthValidator( control: AbstractControl ) {
  const regexp = /^-{0,1}\d+(\.\d{1,2})?$/;
  const value = control.value === null ? '' : control.value.toString();
  if (!value || value.match(regexp)) {
    return null;
  } else {
    return { 'fractionsNumbersLength': true };
  }
}

export function numberMaxLength( requiredLength: number ): ValidatorFn {
  return ( control: AbstractControl ) => {
    let letterCount = 0;
    if (control.value) {
      letterCount = control.value.toString().split('.')[0].length;
    }
    if (letterCount <= requiredLength) {
      return null;
    }
    return { 'maxWholePartLength': { valid: false, requiredLength} };
  };
}

export function positiveNumbers( control: AbstractControl ) {
  return control.value > 0 ? null : { 'positiveNumbers': true };
}

export function noWhitespaces( control: AbstractControl ) {
  return !control.value.includes(' ') ? null : { 'spacesAreNotAllowed': true };
}

export function jackpotNameValidator( control: AbstractControl ) {
  const regexp = /^([A-Za-z0-9\-_]+)$/;
  const value = control.value === null ? '' : control.value.toString();
  if (!value || value.match(regexp)) {
    return null;
  } else {
    return { 'invalidJackpotName': true };
  }
}

export function dateGreaterThanNow( { value }: AbstractControl ): { [key: string]: any } | null {
  if (!value || new Date(value).getTime() > new Date().getTime()) {
    return null;
  }
  return { 'dateGreaterThanNow': true };
}

export function dateGreaterThanNowWithTimezone( name: string ) {
  return ( control: AbstractControl ): { [key: string]: any } | null => {
    const { value, root } = control;
    const timezone = root.get(name)?.value;

    if (!value) {
      return null;
    }

    if (!timezone) {
      if (new Date(value).getTime() > new Date().getTime()) {
        return null;
      }
    } else {
      if (moment(value).subtract(moment.tz(timezone).utcOffset(), 'minutes').valueOf() > moment().valueOf()) {
        return null;
      }
    }

    return { 'dateGreaterThanNow': true };
  };
}

export function colorHexValidator( control: AbstractControl ) {
  const regexp = /^(#{1})([0-9A-Fa-f]{6}|[0-9A-Fa-f]{3})$/; // example #000000 || #000
  const value = control.value ? control.value.toString() : '';
  if (!value || value.match(regexp)) {
    return null;
  } else {
    return { 'invalidColorHexFormat': true };
  }
}

// startTime.value && new Date(value).getTime() > new Date(startTime.value).getTime())

export function startGreaterThanEndValidator( form: AbstractControl ) {
  const startControl = form.get('startTime') as FormControl;
  const endControl = form.get('endTime') as FormControl;

  const startTime = startControl.value ? new Date(startControl.value).getTime() : null;
  const endTime = endControl.value ? new Date(endControl.value).getTime() : null;

  const startErrors = startControl.errors || {};
  if (endTime && startTime && endTime < startTime) {
    startControl.setErrors(Object.assign(startErrors, { 'startGreaterThanEnd': true }));
  } else {
    if ('startGreaterThanEnd' in startErrors) {
      delete startErrors['startGreaterThanEnd'];
      if (Object.keys(startErrors).length === 0) {
        startControl.setErrors(null);
      }
    }
  }
}

export function JSONValidator( control: AbstractControl ) {
  let error: Object | null = null;
  try {
    JSON.parse(control.value);
  } catch (e) {
    if (control.value) {
      if (control.value === []) {
        error = null;
      } else {
        error = { 'JSON': { error: e.message } };
      }
    } else {
      error = null;
    }
  }
  return error;
}

export function ArrayValidator( control: AbstractControl ) {
  let error: Object | null = null;
  let isJSON = true;

  try {
    JSON.parse(control.value);
  } catch (e) {
    isJSON = false;
  }

  if (isJSON) {
    const val = JSON.parse(control.value);
    if (!Array.isArray(val)) {
      error = { 'isArray': true };
    }
  }

  return error;
}

export function playersLengthValidator( control: AbstractControl ): ValidationErrors | null {
  const length = control.value
    .reduce(( res: number, brand: SegmentationBrand ) => {
      res += brand.players.length;

      return res;
    }, 0);

  if (length > 50000) {
    return { invalidPlayersCount: true };
  }

  return null;
}

export function urlValidator( control: AbstractControl ): ValidationErrors | null {
  const regexp = /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/gi;
  const value = control.value === null ? '' : control.value.toString();
  if (!value || value.match(regexp)) {
    return null;
  } else {
    return { 'urlIsNotCorrect': true };
  }
}
