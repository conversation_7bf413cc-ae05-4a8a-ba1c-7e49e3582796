import { BehaviorSubject, combineLatest, Observable, of } from 'rxjs';
import { catchError, distinctUntilChanged, filter, map, share, shareReplay, switchMap, tap } from 'rxjs/operators';

export abstract class StoreService<T> {
  readonly item$: Observable<T | null>;
  private readonly store$ = new BehaviorSubject<T | null>(null);

  protected constructor() {
    this.item$ = combineLatest([
      this.fetch(),
      this.store$
    ]).pipe(
      map(( [, b] ) => b),
      distinctUntilChanged(),
      shareReplay(1)
    );
  }

  protected abstract load(): Observable<T>;

  private fetch(): Observable<T | null> {
    return this.store$.pipe(
      filter(item => item === null),
      switchMap(() => this.load()),
      catchError(() => of(null)),
      tap(items => {
        this.store$.next(items);
      }),
      share()
    );
  }
}
