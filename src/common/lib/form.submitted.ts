import { BehaviorSubject, Observable } from 'rxjs';
import { AbstractControl } from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import { SwuiIsControlInvalidService } from '@skywind-group/lib-swui';

export abstract class FormSubmitted implements SwuiIsControlInvalidService, ErrorStateMatcher {
  private _formSubmitted = new BehaviorSubject<boolean>(false);

  set formSubmitted( val: boolean ) {
    this._formSubmitted.next(val);
  }

  get formSubmitted(): boolean {
    return this._formSubmitted.value;
  }

  get formSubmitted$(): Observable<boolean> {
    return this._formSubmitted as Observable<boolean>;
  }

  isErrorState( control: AbstractControl | null ): boolean {
    if (!control) {
      return false;
    }
    if (this.formSubmitted) {
      return control.invalid;
    }
    return false;
  }

  isControlInvalid( control?: AbstractControl | null ): boolean {
    return this.isErrorState(control || null);
  }
}
