export function simulateBrowserDownload( csvData: any, filename: string ): void {
  let a = document.createElement<'a'>('a');
  a.setAttribute('style', 'display:none;');
  a.setAttribute('target', '_blank');
  document.body.appendChild(a);
  let blob = new Blob(['\ufeff', csvData], { type: 'text/csv' });
  let url = window.URL.createObjectURL(blob);
  a.href = url;
  a.download = `${filename}.csv`;
  // If you will any error in a.download then dont worry about this.
  a.click();
  a.remove();
}
