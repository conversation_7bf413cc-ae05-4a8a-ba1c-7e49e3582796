import { Directive, forwardRef, HostListener, <PERSON><PERSON><PERSON><PERSON>, OnInit, Provider } from '@angular/core';
import {
  ControlValueAccessor,
  FormGroup,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ValidationErrors,
  Validator
} from '@angular/forms';
import { Subject } from 'rxjs';
import { delay, takeUntil } from 'rxjs/operators';

@Directive()
// tslint:disable-next-line:directive-class-suffix
export abstract class AbstractFormValueAccessor<T> implements ControlValueAccessor, Validator, OnInit, OnDestroy {
  abstract readonly form: FormGroup;
  protected readonly destroyed = new Subject<void>();

  protected onChange: any | undefined;
  protected onTouched: any | undefined;
  protected onValidatorChange: any | undefined;

  @HostListener('blur') onblur() {
    if (this.onTouched) {
      this.onTouched();
    }
  }

  ngOnInit(): void {
    this.form.valueChanges.pipe(
      delay(0),
      takeUntil(this.destroyed)
    ).subscribe(value => {
      if (this.onChange) {
        this.onChange(this.transformForm(value));
      }
      this.form.markAllAsTouched();
      if (this.onTouched) {
        this.onTouched();
      }
    });
    this.form.statusChanges.pipe(
      delay(0),
      takeUntil(this.destroyed)
    ).subscribe(() => {
      if (this.onValidatorChange) {
        this.onValidatorChange();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroyed.next();
    this.destroyed.complete();
  }

  registerOnChange( fn: any ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( isDisabled: boolean ): void {
    isDisabled ? this.form.disable({ emitEvent: false }) : this.form.enable();
  }

  writeValue( value: T | undefined ): void {
    if (typeof value === 'undefined' || value === null) {
      return;
    }
    this.form.patchValue(this.transformValue(value), { emitEvent: false });
    this.form.markAsPristine();
    this.form.markAsUntouched();
  }

  validate(): ValidationErrors | null {
    return this.form.valid ? null : { invalidForm: { valid: false } };
  }

  registerOnValidatorChange( fn: () => void ): void {
    this.onValidatorChange = fn;
  }

  protected transformForm( value: any ): T | undefined {
    return value;
  }

  protected transformValue( value: T ): any {
    return value;
  }
}

export function formValueProviders( type: any ): Provider[] {
  return [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => type),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => type),
      multi: true
    }
  ];
}
