export function formatFloat( input: any = 0, fractionCount: number = 2 ): string {
  if (!input) {
    return '';
  }
  const parsedFloat = parseFloat(input);

  const result = parsedFloat.toString();
  const fraction = result.split('.').pop();
  const shouldBeFixed = !result.includes('.') || fraction && fraction.length < 2;

  return parsedFloat || parsedFloat === 0 ?
    (shouldBeFixed ? parsedFloat.toFixed(fractionCount) : result) :
    input;
}
