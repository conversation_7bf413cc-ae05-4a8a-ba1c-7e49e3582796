import { Directive, forwardRef, HostListener, Provider } from '@angular/core';
import { ControlValueAccessor, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors, Validator } from '@angular/forms';


@Directive()
// tslint:disable-next-line:directive-class-suffix
export abstract class AbstractControlValueAccessor implements ControlValueAccessor, Validator {
  disabled = false;
  isValid = true;
  isTouched = false;

  protected onTouched: any | undefined;

  @HostListener('blur') onblur() {
    this.isTouched = true;
    if (this.onTouched) {
      this.onTouched();
    }
  }

  abstract writeValue( obj: any ): void;

  registerOnChange( fn: any ): void {
    if (fn) {
      this.onChange = fn;
    }
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( isDisabled: boolean ): void {
    this.disabled = isDisabled;
  }

  validate(): ValidationErrors | null {
    return this.isValid ? null : { invalidForm: { valid: false } };
  }

  protected onChange: any | undefined = () => {
  };
}

export function controlValueProviders( type: any ): Provider[] {
  return [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => type),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => type),
      multi: true
    }
  ];
}
