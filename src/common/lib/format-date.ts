import * as moment from 'moment';
import 'moment-timezone';

export function formatDate( date: string | undefined, format: string, timezone?: string, skipTz = false, displayTimeZone = true ): string {
  if (!date) {
    return '-';
  }
  const tz = timezone ? timezone : moment.tz.guess();
  const zone = moment.tz.zone(tz);
  const abr = zone ? zone.abbr(moment.utc(date).unix()) : '';
  const ts = skipTz ? moment.utc(date).toArray() : date;

  return displayTimeZone ? moment.tz(ts, tz).format(format) + ' ' + abr : moment.tz(ts, tz).format(format);
}

export function formatDateToCurrentTz( date: string | undefined, format?: string, timezone?: string ): string {
  if (!date) {
    return '-';
  }
  const tz = timezone ? timezone : moment.tz.guess();
  const ts = moment.utc(date).toArray();
  return moment.tz(ts, tz).format(format);
}
