import { ErrorMessage } from '../lib/validators';

export const MESSAGE_ERRORS: ErrorMessage = {
  maxPrizesCount: 'VALIDATION.maxPrizesCount',
  required: 'VALIDATION.fieldRequired',
  fractionsNumbersOnly: 'VALIDATION.onlyNumbers',
  fractionsNumbersLength: 'VALIDATION.maxDecimalAllowed',
  maxlength: 'VALIDATION.maxLength',
  maxWholePartLength: 'VALIDATION.maxLength',
  numbersOnly: 'VALIDATION.invalidNumbersOnly',
  lessThanMax: 'VALIDATION.lessThanMax',
  nameExists: 'VALIDATION.nameExists',
  redeemAmountMaxGteMin: 'VALIDATION.redeemAmountMaxGteMin',
  redeemMinAmountGteAmount: 'VALIDATION.redeemMinAmountGteAmount',
  redeemMaxAmountGteAmount: 'VALIDATION.redeemMaxAmountGteAmount',
  dateGreaterThanNow: 'VALIDATION.dateGreaterThanNow',
  startGreaterThanEnd: 'VALIDATION.startGreaterThanEnd',
  startLessThanEarlier: 'VALIDATION.startLessThanEarlier',
  min: 'VALIDATION.min',
  max: 'VALIDATION.max',
  positiveNumbers: 'VALIDATION.positiveNumbers',
  digitsOnly: 'VALIDATION.noDecimals',
  jackpotNameExists: 'VALIDATION.jackpotNameExists',
  spacesAreNotAllowed: 'VALIDATION.spacesAreNotAllowed',
  invalidJackpotName: 'VALIDATION.invalidJackpotName',
  invalidImageUrl: 'VALIDATION.invalidImagePath',
  urlIsNotCorrect: 'VALIDATION.urlIsNotCorrect'
};
