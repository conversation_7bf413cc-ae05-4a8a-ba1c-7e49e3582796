import { Directive, ElementRef, Input, OnDestroy, OnInit, Optional, Renderer2, Self } from '@angular/core';
import { NgControl } from '@angular/forms';
import { SettingsService } from '@skywind-group/lib-swui';

import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

export function currencyFormatter( currencyFormat: string = 'de-DE', input: number = 0, currencyCode: string = 'USD' ): string {
  const inputNumber = input || 0;

  const inputCurrencySymbol = new Intl.NumberFormat(currencyFormat, {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(0).replace(/\d/g, '').trim();

  return inputNumber !== 0 ?
    new Intl.NumberFormat(currencyFormat, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2,
    }).format(inputNumber).replace(inputCurrencySymbol, '').replace(/\s+/g, '') :
    inputNumber.toString();
}

@Directive({ selector: '[swCurrencyFormatter]' })
export class SettingCurrencyFormatterDirective implements OnInit, OnDestroy {
  @Input()
  set currencyCode( val: string ) {
    if (!val) {
      return;
    }
    this._currencyCode = val;
    this.updateInputValue();
  }

  get currencyCode(): string {
    return this._currencyCode;
  }

  private div: any;
  private _currencyCode = 'USD';
  private readonly el: HTMLInputElement;
  private readonly destroyed$ = new Subject<void>();

  constructor( @Optional() @Self() public ngControl: NgControl,
               private elementRef: ElementRef,
               private renderer: Renderer2,
               private readonly settingsService: SettingsService
  ) {
    this.el = this.elementRef.nativeElement as HTMLInputElement;
  }

  ngOnInit() {
    this.div = this.renderer.createElement('div');
    this.renderer.setStyle(this.div, 'text-align', 'right');
    this.renderer.setStyle(this.div, 'overflow', 'hidden');
    this.renderer.setStyle(this.div, 'display', 'block');

    this.renderer.setStyle(this.el, 'display', 'none');
    this.renderer.parentNode(this.el).appendChild(this.div);

    this.settingsService.appSettings$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      this.updateInputValue();
    });

    this.setInputOpacity(this.el.disabled);

    this.ngControl.statusChanges?.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( status: string ) => this.setInputOpacity(status === 'DISABLED'));

    if (this.ngControl.control?.value !== undefined) {
      this.showEmptyValueInput(this.ngControl.control?.value);
    }

    this.ngControl.control?.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( inputValue: string ) => this.showEmptyValueInput(inputValue));

    this.onInputClickAction();
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
    this.renderer.destroy();
  }

  private setInputOpacity( status: boolean ) {
    this.renderer.setStyle(this.div, 'opacity', status ? '0.38' : '1');
  }

  private showEmptyValueInput( inputValue: string ) {
    if ((!inputValue || parseFloat(inputValue) === 0)) {
      this.renderer.setStyle(this.el, 'display', 'block');
      this.renderer.setStyle(this.div, 'display', 'none');
    } else if (this.el !== document.activeElement) {
      this.renderer.setStyle(this.el, 'display', 'none');
      this.renderer.setStyle(this.div, 'display', 'block');
    }

    this.updateInputValue();
  }

  private onInputClickAction() {
    if (this.el.parentElement) {
      this.el.parentElement.onclick = () => {
        if (!this.ngControl.control?.disabled) {
          this.renderer.setStyle(this.div, 'opacity', '1');
        }
        this.renderer.removeStyle(this.el, 'display');
        this.renderer.setStyle(this.div, 'display', 'none');
      };
      this.el.onblur = () => {
        if (this.el.value) {
          this.renderer.setStyle(this.el, 'display', 'none');
          this.renderer.removeStyle(this.div, 'display');
        }
      };
    }
  }

  private updateInputValue() {
    const inputNumber = parseFloat(this.el.value.toString());
    const currencyFormat = this.settingsService.appSettings.currencyFormat;

    const result = this.el.value.toString() !== 'MAX' ?
      currencyFormatter(currencyFormat, inputNumber, this.currencyCode) :
      this.el.value.toString();

    if (this.div) {
      this.renderer.setProperty(this.div, 'innerHTML', result);
    }
  }
}
