import { DefaultValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { Directive, forwardRef, HostListener } from '@angular/core';

const TRIM_VALUE_ACCESSOR: any = {
  provide: NG_VALUE_ACCESSOR,
  useExisting: forwardRef(() => TrimFormatterDirective),
  multi: true,
};

@Directive({
  selector: '[swTrimFormatter]',
  providers: [TRIM_VALUE_ACCESSOR],
})
export class TrimFormatterDirective extends DefaultValueAccessor {

  @HostListener('input', ['$event.target.value'])
  ngOnChange = ( val: string ) => {
    this.onChange(val.trim());
  };

  @HostListener('blur', ['$event.target.value'])
  applyTrim( val: string ) {
    this.writeValue(val);
  }

  writeValue( value: any ) {
    value = value ? value.trim() : '';

    super.writeValue(value);
  }
}
