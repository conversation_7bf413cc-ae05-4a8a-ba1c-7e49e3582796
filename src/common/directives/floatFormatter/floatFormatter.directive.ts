import { Directive, ElementRef, HostListener, OnInit } from '@angular/core';
import { formatFloat } from '../../lib/format-float';

@Directive({ selector: '[swFloatFormatter]' })
export class FloatFormatterDirective implements OnInit {

  private readonly el: HTMLInputElement;

  constructor( private elementRef: ElementRef ) {
    this.el = this.elementRef.nativeElement;
  }

  ngOnInit() {
    this.el.value = formatFloat(this.el.value);
  }

  @HostListener('blur', ['$event.target.value'])
  onBlur( value: any ) {
    this.el.value = formatFloat(value);
  }
}
