import { Directive, ElementRef, HostListener, OnInit } from '@angular/core';

function transform( input: any = 0 ) {
  if (!input) {
    return '';
  }
  const parsedInt = parseInt(input, 10);
  return parsedInt ? parsedInt.toString() : input;
}

@Directive({ selector: '[swIntFormatter]' })
export class IntFormatterDirective implements OnInit {

  private readonly el: HTMLInputElement;

  constructor( private elementRef: ElementRef ) {
    this.el = this.elementRef.nativeElement;
  }

  ngOnInit() {
    this.el.value = transform(this.el.value);
  }

  @HostListener('blur', ['$event.target.value'])
  onBlur( value: any ) {
    this.el.value = transform(value);
  }
}
