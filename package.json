{"name": "sw-ubo-hub-engagement", "version": "0.2.96", "scripts": {"mock-api": "NODE_OPTIONS='--max-http-header-size=40960' npx json-server --routes routes.json --watch db.json", "ng": "node --max_old_space_size=8192 --max-http-header-size=40960 ./node_modules/@angular/cli/bin/ng", "config": "scripty", "prebuild": "rimraf ./dist", "start": "env-cmd npm run ng serve", "start:prod": "env-cmd npm run ng serve -- --configuration production=true", "build": "npm run ng build", "build:prod": "npm run ng build -- --configuration production --source-map", "test": "npm run ng test", "test:coverage": "npm run ng test -- --no-watch --no-progress --code-coverage", "lint": "npm run ng lint", "e2e": "npm run ng e2e", "postinstall": "ngcc"}, "private": true, "dependencies": {"@angular/animations": "12.2.16", "@angular/cdk": "12.2.13", "@angular/common": "12.2.16", "@angular/compiler": "12.2.16", "@angular/core": "12.2.16", "@angular/flex-layout": "12.0.0-beta.34", "@angular/forms": "12.2.16", "@angular/material": "12.2.13", "@angular/platform-browser": "12.2.16", "@angular/platform-browser-dynamic": "12.2.16", "@angular/router": "12.2.16", "@auth0/angular-jwt": "5.0.2", "@kolkov/angular-editor": "1.2.0", "@ngneat/spectator": "7.2.0", "@ngx-formly/core": "5.6.2", "@ngx-formly/material": "5.6.2", "@ngx-formly/schematics": "5.6.2", "@ngx-translate/core": "13.0.0", "@ngx-translate/http-loader": "6.0.0", "@skywind-group/lib-swui": "0.1.649", "dexie": "3.0.3", "dom-autoscroller": "2.3.4", "json-server": "0.16.1", "lodash.merge": "4.6.2", "lodash.template": "4.5.0", "moment": "2.29.1", "moment-timezone": "0.5.32", "ng2-dragula": "2.1.1", "ngx-color-picker": "10.1.0", "rxjs": "6.6.3", "tslib": "2.0.0", "zone.js": "0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "12.2.16", "@angular/cli": "12.2.16", "@angular/compiler-cli": "12.2.16", "@angular/language-service": "12.2.16", "@types/jasmine": "3.6.0", "@types/jasminewd2": "2.0.8", "@types/lodash.merge": "4.6.6", "@types/lodash.template": "4.5.0", "@types/moment-timezone": "0.5.13", "@types/lodash": "4.14.119", "@types/node": "14.11.1", "codelyzer": "6.0.0", "env-cmd": "10.1.0", "jasmine-core": "3.6.0", "jasmine-spec-reporter": "5.0.0", "karma": "6.3.4", "karma-chrome-launcher": "3.1.0", "karma-coverage-istanbul-reporter": "3.0.3", "karma-jasmine": "4.0.0", "karma-jasmine-html-reporter": "1.5.0", "protractor": "7.0.0", "raw-loader": "4.0.2", "rimraf": "3.0.2", "rxjs-tslint": "0.1.8", "scripty": "2.0.0", "ts-node": "9.0.0", "tslint": "6.1.3", "typescript": "4.3.5"}}